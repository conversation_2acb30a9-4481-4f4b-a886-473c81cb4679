import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowRight, ArrowLeft } from 'lucide-react';
import { mapAiInvoiceStatus } from '../../../utils/aiUtils';
import { getStatusClass } from '../../../utils';
import SelectOption from '../../../ui-components/SelectOption';
import Checkbox from '../../../ui-components/fields/Checkbox';
import ZohoSubmitBtn from '../form-sections/components/zoho/ZohoSubmitBtn';
import TallySubmitBtn from '../form-sections/components/tally/TallySubmitBtn';
import { Tooltip } from 'react-tooltip';
import { Link, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../utils/apiUtils';
import usePrompt from '../../../global/hooks/usePrompt';
import DuplicateAlertModal from './DuplicateAlertModal';
import './AiForm.scss';
import { Menu, MenuItem, IconButton } from '@mui/material';
import { MoreVert } from '@mui/icons-material';
import SaveOutlinedIcon from '@mui/icons-material/SaveOutlined';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import FileCopyOutlinedIcon from '@mui/icons-material/FileCopyOutlined';
import KeyboardAltOutlinedIcon from '@mui/icons-material/KeyboardAltOutlined';
import { getOriginalInvoice, invoiceComment, saveDraft, submitExtraction } from '../../../services/aiServices';
import { createLoadingToast } from '../../../utils/toastUtils';
import { removeObjectsFromJson } from '../../../utils/jsonUtils';
import ticketServices from '../../../services/ticketServices';
import { getSectionsWithErrors } from '../../../utils/aiUtils';
import { syncZoho } from '../../../services/zohoServices';
import ValidationTooltip from './ValidationTooltip';
import LoadingWrapper from '../../../global/components/LoadingWrapper';
import ShortcutKeysModal from './ShortcutKeysModal';

function AiFormHeader({
  isLoading = false,
  // Essential props that need to come from parent
  categoryOptions,
  extractedData,
  refetch,
  handlePrevious,
  handleNext,
  reachedStatus,
  didNotFound,

  // Form data and state that needs to be shared
  formData,
  setFormData,
  invoiceType,
  setInvoiceType,
  isReadOnly,
  setIsReadOnly,
  sectionsWithErrors,
  setSectionsWithErrors,
  sectionsWithMissingFields,
  showOriginal,
  setShowOriginal,
  // Callbacks that need to be called in parent
  onShowOriginalChange,
  onInvoiceTypeChange,
}) {
  const { fileId, businessId } = useParams();
  const { prompt, PromptModal } = usePrompt();
  const [isByPassValidation, setIsBypassValidation] = useState(false);
  const [duplicateInvoicesObj, setDuplicateInvoicesObj] = useState(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [shortcutModalOpen, setShortcutModalOpen] = useState(false);

  const originalInvoice = React.useRef(null);

  const handleCategoryChange = useCallback(
    (selectedOption) => {
      if (!selectedOption || selectedOption.value === invoiceType) return;
      setInvoiceType(selectedOption.value);
      ticketServices
        .updateTicket(extractedData?.ticket_id, {
          category: selectedOption.value,
          sub_category: 'Invoice', // This seems to be hardcoded
        })
        .then(() => {
          toast.success('Category updated successfully');
          onInvoiceTypeChange?.(selectedOption.value);
          refetch(false);
        })
        .catch((err) => {
          setInvoiceType(invoiceType);
          const errorMessage = getErrorMessage(err);
          toast.error(errorMessage);
        });
    },
    [fileId, invoiceType, extractedData?.ticket_id, setInvoiceType, refetch, onInvoiceTypeChange]
  );

  const handleEdit = useCallback(async () => {
    let editComment;
    editComment = await prompt('Do you want to edit the invoice?', 'Please enter your reason', 20);
    if (editComment) {
      const payload = {
        message_type: 're_edit',
        user_comment: editComment,
      };
      invoiceComment(fileId, businessId, payload)
        .then(() => {
          toast.success('Invoice is now editable');
          refetch();
        })
        .catch((err) => {
          const error = getErrorMessage(err);
          toast.error(error);
        });
    }
  }, [fileId, businessId, refetch, prompt]);

  const handleDuplicate = useCallback(
    async (duplicateType, comment = '') => {
      setMenuAnchorEl(null);
      switch (duplicateType) {
        case 'MarkAsDuplicate':
          const duplicateJson = JSON.stringify(duplicateInvoicesObj || extractedData?.duplicate_validation);
          const payload = {
            message_type: 'duplicate',
            system_message: duplicateJson,
          };
          invoiceComment(fileId, businessId, payload)
            .then(() => {
              setDuplicateInvoicesObj(null);
              toast.success('Marked Invoice as Duplicate');
              refetch();
            })
            .catch((err) => {
              const error = getErrorMessage(err);
              toast.error(error);
            });
          return;

        case 'MarkAsNotDuplicate':
          const notDuplicatePayload = {
            user_comment: comment,
            message_type: 'duplicate_override',
          };
          invoiceComment(fileId, businessId, notDuplicatePayload)
            .then(() => {
              setDuplicateInvoicesObj(null);
              // handleSubmit(true); // This line was removed from the new_code, so it's removed here.
            })
            .catch((err) => {
              const error = getErrorMessage(err);
              toast.error(error);
            });
          return;

        case 'MarkAsDuplicateWithComment':
          let reason;
          reason = await prompt('Do you want to mark this invoice as duplicate?', 'Please enter your reason', 20);
          if (reason) {
            invoiceComment(fileId, businessId, { message_type: 'duplicate' })
              .then(() => {
                toast.success('Marked Invoice as Duplicate');
                refetch();
              })
              .catch((err) => {
                const error = getErrorMessage(err);
                toast.error(error);
              });
          }
          return;
        default:
          return;
      }
    },
    [extractedData?.duplicate_validation, duplicateInvoicesObj, fileId, businessId, refetch, prompt]
  );

  const handleViewOriginal = useCallback(() => {
    setMenuAnchorEl(null);
    if (originalInvoice.current) {
      setShowOriginal(!showOriginal);
      onShowOriginalChange?.(!showOriginal);
    } else {
      getOriginalInvoice(fileId, businessId)
        .then((res) => {
          originalInvoice.current = res;
          setShowOriginal(true);
          onShowOriginalChange?.(true, res);
        })
        .catch((err) => {
          const error = getErrorMessage(err);
          toast.error(error);
        });
    }
  }, [showOriginal, fileId, businessId, onShowOriginalChange]);

  const handleBypassValidation = useCallback(async () => {
    if (!isByPassValidation) {
      const bypassComment = await prompt('Do you want to Bypass Validation?', 'Please enter your reason', 20);
      if (bypassComment) {
        const payload = {
          message_type: 'validation_override',
          user_comment: bypassComment,
        };
        invoiceComment(fileId, businessId, payload)
          .then((res) => {
            toast.success(res?.message);
            setIsBypassValidation(true);
          })
          .catch((err) => {
            const error = getErrorMessage(err);
            toast.error(error);
          });
      }
    } else {
      setIsBypassValidation(false);
    }
  }, [isByPassValidation, fileId, businessId, prompt, setIsBypassValidation]);

  const handleSaveDraft = useCallback(() => {
    setMenuAnchorEl(null);
    const finalFormData = removeObjectsFromJson(formData, ['error', 'warning', 'recommended_fields', 'exact_match']);
    const loadingToast = createLoadingToast('Saving draft...');
    saveDraft(fileId, businessId, finalFormData)
      .then(() => {
        loadingToast('success', 'Draft saved successfully');
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        loadingToast('error', errorMessage);
      });
  }, [formData, fileId, businessId]);

  const handleOpenShortcutModal = useCallback(() => {
    setMenuAnchorEl(null);
    setShortcutModalOpen(true);
  }, []);

  const handleSync = useCallback(
    ({ formDataProp = null, isShowSuccessToast = false, isShowValidateAndSync = false }) => {
      let finalFormData;
      if (!formDataProp) {
        const parsedFormData =
          removeObjectsFromJson(formData, ['error', 'warning', 'recommended_fields', 'exact_match']) || [];

        finalFormData = {
          ...parsedFormData,
          is_by_pass_validation: isByPassValidation,
        };
      } else {
        finalFormData = formDataProp;
      }
      syncZoho(businessId, finalFormData)
        .then(() => {
          if (isShowSuccessToast) {
            toast.success('Synced successfully');
          }
          if (isShowValidateAndSync) {
            toast.success('Validation and Synced successfully');
          }
        })
        .catch((err) => {
          const errorMessage = getErrorMessage(err);
          toast.error(errorMessage);
        })
        .finally(() => {
          refetch(false);
        });
    },
    [formData, businessId, isByPassValidation, refetch]
  );

  const handleSubmit = useCallback(
    async (isForceBypassValidation = false) => {
      let finalFormData;
      const finalBypassValidation = isForceBypassValidation ? isForceBypassValidation : isByPassValidation;
      const parsedFormData =
        removeObjectsFromJson(formData, ['error', 'warning', 'recommended_fields', 'exact_match']) || [];

      finalFormData = {
        ...parsedFormData,
        is_by_pass_validation: finalBypassValidation,
      };

      const loadingToast = createLoadingToast('Validating extraction...');

      submitExtraction(fileId, businessId, finalFormData)
        .then((res) => {
          const errorMessage = res?.message || 'Failed to submit extraction';
          if (res?.status === 'success') {
            setIsReadOnly(true);
            setIsBypassValidation(false);
            if (
              extractedData?.enable_auto_sync_invoice &&
              extractedData?.accounting_platform?.toLowerCase() === 'zoho'
            ) {
              handleSync({
                formDataProp: finalFormData,
                refetch: true,
                isShowValidateAndSync: true,
              });
              loadingToast('dismiss');
            } else {
              loadingToast('success', 'Extraction submitted successfully');
              refetch();
            }
          } else {
            if (res?.duplicate_validation) {
              setDuplicateInvoicesObj(res?.duplicate_validation);
              loadingToast('dismiss');
            } else {
              loadingToast('error', errorMessage);
            }
            const sectionThatHasError = getSectionsWithErrors(res);
            setSectionsWithErrors(sectionThatHasError);
            setFormData(res);
          }
        })
        .catch((err) => {
          const errorMessage = getErrorMessage(err);
          loadingToast('error', errorMessage);
        });
    },
    [formData, fileId, businessId, isByPassValidation, extractedData, handleSync]
  );

  return (
    <div className="fixed top-0 left-0 right-0 z-10 w-full h-20 bg-white overflow-hidden">
      <LoadingWrapper loading={isLoading} useBlankSkeleton={true}>
        <div id="form-header" className="flex justify-between items-center p-4">
          <div className="flex items-center gap-2 flex-shrink-0">
            <Link
              to="/invoices"
              className="flex items-center gap-2 text-nowrap px-2 py-1 text-primary-color border border-accent1-border bg-accent1-bg no-underline font-semibold text-sm rounded-xl transition-all duration-200 hover:bg-accent1-bg-hover"
              aria-label="Go back to invoices list"
            >
              <ArrowLeft className="w-4 h-4" /> Go to invoices
            </Link>
            <div className="h-7 border-l border-gray-300 mx-1" />
            <div className="flex flex-col justify-center min-w-[180px]">
              <span className="font-semibold text-base text-[#181d27] leading-tight">
                {extractedData?.bill_to_details?.invoice_no}
              </span>
              <span className="text-xs text-gray-700 mt-0.5">{extractedData?.document_name}</span>
            </div>
          </div>

          <div className="flex items-center flex-wrap gap-4 mr-auto">
            <SelectOption
              label="Category"
              options={categoryOptions}
              defaultValue={invoiceType}
              onChange={handleCategoryChange}
              placeholder="Select Invoice Type"
              disabled={
                extractedData?.status === '3' ||
                extractedData?.status === '4' ||
                extractedData?.status === '5' ||
                extractedData?.status === '6' ||
                extractedData?.status === '7' ||
                extractedData?.status === '8'
              }
            />
            <SelectOption label="Sub-category" disabled={true} placeholder="Invoice" className="min-w-11" />
            {extractedData?.status && (
              <div className="flex flex-col">
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <div
                  className={`w-fit min-w-11 flex items-center justify-center select-none rounded-full py-1 px-2 text-base ${getStatusClass(
                    extractedData?.status,
                    true
                  )}`}
                >
                  {mapAiInvoiceStatus(extractedData?.status)}
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {isReadOnly && !showOriginal && (
              <button type="button" className="btn" onClick={handleEdit}>
                <PencilLine size={20} strokeWidth={2} />
                <span>Edit</span>
              </button>
            )}

            <div className="flex items-center gap-3">
              {!(
                extractedData?.status === '3' ||
                extractedData?.status === '4' ||
                extractedData?.status === '5' ||
                extractedData?.status === '6' ||
                extractedData?.status === '7' ||
                extractedData?.status === '8'
              ) && (
                <Checkbox label="Bypass Validation" checked={isByPassValidation} onChange={handleBypassValidation} />
              )}
              {extractedData?.accounting_platform?.toLowerCase() === 'zoho' ? (
                <ZohoSubmitBtn
                  enable_auto_sync={extractedData?.enable_auto_sync_invoice}
                  handleSubmit={handleSubmit}
                  isDisabled={
                    (sectionsWithErrors.length > 0 || sectionsWithMissingFields.length > 0 || !invoiceType) &&
                    !isByPassValidation
                  }
                  status={extractedData?.status}
                  handleSync={handleSync}
                />
              ) : (
                <TallySubmitBtn
                  handleSubmit={handleSubmit}
                  isDisabled={
                    (sectionsWithErrors.length > 0 || sectionsWithMissingFields.length > 0 || !invoiceType) &&
                    !isByPassValidation
                  }
                  status={extractedData?.status}
                />
              )}
            </div>

            <div className="flex items-center gap-2">
              <IconButton
                onClick={(e) => setMenuAnchorEl(e.currentTarget)}
                className="btn !p-1"
                size="small"
                aria-controls={Boolean(menuAnchorEl) ? 'actions-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={Boolean(menuAnchorEl) ? 'true' : undefined}
              >
                <MoreVert />
              </IconButton>

              <button
                type="button"
                className="btn"
                onClick={handlePrevious}
                disabled={reachedStatus.reachedTop || didNotFound}
                data-tooltip-id="tooltip-nav-btns"
                data-tooltip-content={
                  didNotFound
                    ? 'Invoice not found'
                    : reachedStatus.reachedTop
                      ? 'You are on the latest invoice'
                      : 'Go to previous invoice'
                }
              >
                <ArrowLeft className="w-5 h-5" />
              </button>

              <button
                type="button"
                className="btn"
                onClick={handleNext}
                disabled={reachedStatus.reachedEnd || didNotFound}
                data-tooltip-id="tooltip-nav-btns"
                data-tooltip-content={
                  didNotFound
                    ? 'Invoice not found'
                    : reachedStatus.reachedEnd
                      ? 'You are on the oldest invoice'
                      : 'Go to next invoice'
                }
              >
                <ArrowRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </LoadingWrapper>

      {/* Tooltip for navigation buttons */}
      <Tooltip id="tooltip-nav-btns" />
      <PromptModal />
      {(sectionsWithErrors.length > 0 || sectionsWithMissingFields.length > 0) &&
        !isByPassValidation &&
        extractedData?.status !== '3' && (
          <ValidationTooltip
            sectionsWithErrors={sectionsWithErrors}
            sectionsWithMissingFields={sectionsWithMissingFields}
            transformSectionName={{
              gst_ledgers: 'GST Summary',
            }}
          />
        )}
      <DuplicateAlertModal
        isOpen={!!duplicateInvoicesObj}
        duplicateData={duplicateInvoicesObj?.duplicate_invoices || []}
        duplicateType={duplicateInvoicesObj?.type || 'possible'}
        onMarkAsDuplicate={() => setDuplicateInvoicesObj(null)}
        onClose={() => setDuplicateInvoicesObj(null)}
        onMarkAsNotDuplicate={(comment) => setDuplicateInvoicesObj(null)}
      />

      {/* Save Draft, Mark As Duplicate, View Original */}
      <Menu
        id="actions-menu"
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={() => setMenuAnchorEl(null)}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        {!isReadOnly && (
          <MenuItem
            onClick={() =>
              handleDuplicate(extractedData?.duplicate_validation ? 'MarkAsDuplicate' : 'MarkAsDuplicateWithComment')
            }
          >
            <FileCopyOutlinedIcon className="mr-2" />
            Mark As Duplicate
          </MenuItem>
        )}
        <MenuItem onClick={handleViewOriginal}>
          <RemoveRedEyeOutlinedIcon className="mr-2" />
          {showOriginal ? 'Hide Original' : 'View Original'}
        </MenuItem>
        {!isReadOnly && !showOriginal && (
          <MenuItem onClick={handleSaveDraft}>
            <SaveOutlinedIcon className="mr-2" />
            Save As Draft
          </MenuItem>
        )}
        <MenuItem onClick={handleOpenShortcutModal}>
          <KeyboardAltOutlinedIcon className="mr-2" />
          Keyboard Shortcuts
        </MenuItem>
      </Menu>

      {/* Shortcut Keys Modal */}
      <ShortcutKeysModal open={shortcutModalOpen} onClose={() => setShortcutModalOpen(false)} />
    </div>
  );
}

export default AiFormHeader;
