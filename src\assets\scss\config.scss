/*-------------------
       Fonts
--------------------*/

$secondaryFont: 'DMSans';
$fontVarient3: 'Inter';
$primaryFont: 'DMSans';

$black: black;
$white: #ffffff;
$black2: rgb(112 112 112);
$bold: bold;
$normal: normal;

/*-------------------
      Base Sizes
--------------------*/

/* This is the single variable that controls them all */
$emSize: 14px;

/* The size of page text  */
$largeSize: 16px; //This Variable only need config.less
$fontSize: 14px;
$smallSize: 12px; //This Variable only need config.less

/*-------------------
    Brand Colors
--------------------*/
$primaryBgColor: #ffffff;
$secondaryBgColor: #f5f7fa;
$primaryColor: #0b1a30;
$accentColor1: #4ac2ff;
$accentBgColor1: #4ac2ff1a;
$accentBorder1: #4ac2ff50;
$accentHover1: #4ac2ff33;
$accentColor2: #2186d0;
$accentBorder2: #2186d080;
$accentHover2: #1b6caf;
$borderColor: #e0e6ed;
$successColor: #5cd68a;
$errorColor: #ff6b6b;

/* Legacy colors kept for backward compatibility */
$greenVarient2: #58b5a6;
$blueBackGround: #f5f7fa;

$yellowColor: #f6d659;
$blueBorder: #0077ff;

$newVarient: #d9d9d9;

$redColor1: #ff6b6b;

// $borderColor: #B8B8B8;

/*-------------------
     Breakpoints
--------------------*/
$mobileBreakpoint: 480px;
$tabletBreakpoint: 768px;
$bigTabletBreakpoint: 992px;
$mediumMonitorBreakpoint: 1200px;

$media_queries: (
  'mobileScreen': #{'only screen and (max-width: #{$mobileBreakpoint})'},
  'tabletScreen': #{'screen and (min-width: #{$mobileBreakpoint + 1}) and (max-width: #{$tabletBreakpoint}) '},
  'bigTabletScreen': #{'screen and (min-width: #{$tabletBreakpoint + 1}) and (max-width: #{$bigTabletBreakpoint}) '},
  'mediumScreen': #{'screen and (min-width: #{$bigTabletBreakpoint + 1}) and (max-width: #{$mediumMonitorBreakpoint}) '},
  'computer': #{'only screen and (min-width: #{$mediumMonitorBreakpoint + 1})'},
);

@mixin for_media($breakpoint) {
  @if map-has-key($media_queries, $breakpoint) {
    @media #{map-get($media_queries, $breakpoint)} {
      @content;
    }
  } @else {
    @warn "Breakpoint '#{$breakpoint}' not found in the media queries map!";
  }
}

$grayColor1: #a7a7a7;
$grayColor4: #a7a7a740;
$grayColor5: #f5f5f5;

/* -------------------
     AiFrom
--------------------*/

$field-gap: 0.5em;

// for tailwind
:root {
  --field-gap: #{$field-gap};
}
