import React, { useState, useRef, useCallback } from 'react';
import { Modal } from 'semantic-ui-react';
import { motion, AnimatePresence } from 'motion/react';
import MinimumCharCount from '../../ui-components/MinimumCharCount';

const PromptModalInner = React.forwardRef((props, ref) => {
  const {
    open,
    handleConfirm,
    handleCancel,
    modalProps,
    cancelText = 'Cancel',
    confirmText = 'Submit',
    size = 'small',
  } = props;
  const { title, placeholder, inputRows, minChars = 0, maxChars = Infinity } = modalProps;
  const [inputValue, setInputValue] = useState('');
  const charCount = inputValue.length;
  const isSubmitEnabled = inputValue.trim() !== '' && charCount >= minChars;
  return (
    <Modal open={open} onClose={handleCancel} size={size}>
      <div className="bg-white rounded-lg shadow-lg w-full">
        <div className="p-6">
          <h2 className="text-xl font-bold mb-6">{title}</h2>
          <div className="mb-6">
            <div className="relative">
              <textarea
                ref={ref}
                id="alertInput"
                className="input-field"
                rows={inputRows}
                placeholder={placeholder}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                autoFocus
                maxLength={maxChars !== Infinity ? maxChars : undefined}
              />
              <div className="absolute -bottom-7 right-0 flex justify-between items-center mt-2">
                <MinimumCharCount minChars={minChars} charCount={charCount} />
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center space-x-4 mt-10">
            <button
              onClick={handleCancel}
              className="w-fit py-3 px-4 text-nowrap font-bold bg-gray-200 text-gray-800 rounded-full hover:bg-gray-300 transition-colors"
            >
              {cancelText}
            </button>
            <button
              onClick={handleConfirm}
              className={`w-fit py-3 px-4 text-nowrap rounded-full transition-colors ${
                isSubmitEnabled ? 'bg-accent2 hover:bg-accent2-hover' : 'bg-[#D5D7DA] text-[#535862] cursor-not-allowed'
              }`}
              disabled={!isSubmitEnabled}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
});

const usePrompt = () => {
  const [open, setOpen] = useState(false);
  const inputRef = useRef(null);
  const [modalProps, setModalProps] = useState({
    title: 'Alert',
    placeholder: 'Enter your text here',
    minChars: 0,
    maxChars: Infinity,
    inputRows: 5,
  });
  const resolveRef = useRef(null);

  const prompt = useCallback(
    (title = 'Alert', placeholder = 'Enter your text here', minChars = 0, maxChars = Infinity, inputRows = 5) => {
      setModalProps({ title, placeholder, minChars, maxChars, inputRows });
      setOpen(true);
      return new Promise((resolve) => {
        resolveRef.current = resolve;
      });
    },
    []
  );

  const handleConfirm = () => {
    const userInput = inputRef.current?.value;
    if (resolveRef.current) {
      resolveRef.current(userInput);
    } else if (resolveRef.current) {
      resolveRef.current(null);
    }
    setOpen(false);
  };

  const handleCancel = () => {
    if (resolveRef.current) resolveRef.current(null);
    setOpen(false);
  };

  const PromptModal = () => (
    <PromptModalInner
      open={open}
      handleConfirm={handleConfirm}
      handleCancel={handleCancel}
      modalProps={modalProps}
      ref={inputRef}
    />
  );

  return { prompt, PromptModal };
};

export default usePrompt;
