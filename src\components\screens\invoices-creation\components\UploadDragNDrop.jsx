import { useRef } from 'react';
import { FileText, Plus, Upload } from 'lucide-react';

export default function UploadDragNDrop({ isDragActive, handleFiles = null }) {
  const fileInputRef = useRef(null);
  return (
    <div
      className={`min-h-0 relative border-2 border-dashed rounded-xl p-8 transition-all duration-300 cursor-pointer group overflow-hidden ${
        isDragActive
          ? 'border-accent2 bg-blue-50 shadow-lg transform scale-105'
          : 'border-gray-300 hover:border-accent2 hover:bg-gray-50 hover:shadow-md'
      }`}
      onClick={() => fileInputRef.current?.click()}
    >
      <div className="flex flex-col min-h-0 items-center justify-center text-center">
        {/* Dynamic Icon with Animation */}
        <div className={`mb-6 relative transition-transform duration-300`}>
          <div className={`p-4 rounded-full transition-colors duration-300 bg-blue-100`}>
            <FileText className="h-12 w-12 transition-colors duration-300 text-accent2" />
          </div>
          <div className="absolute -bottom-1 -right-1 rounded-full p-1.5 transition-colors duration-300 bg-accent2">
            <Plus className="h-4 w-4 text-white" />
          </div>
        </div>

        {/* Primary Action Text */}
        <div className="mb-6">
          <h3
            className={`text-xl font-semibold mb-2 transition-colors duration-300 ${
              isDragActive ? 'text-accent2' : 'text-gray-800'
            }`}
          >
            {isDragActive ? 'Drop files here!' : 'Upload Documents'}
          </h3>
          <p
            className={`text-base transition-colors duration-300 ${
              isDragActive ? 'text-accent2 font-medium' : 'text-gray-600'
            }`}
          >
            {isDragActive ? 'Release to upload your files' : 'Drag and drop files here'}
          </p>
        </div>

        {/* Divider */}
        <div className="flex items-center w-full max-w-xs mb-6">
          <div className="flex-1 h-px bg-gray-300"></div>
          <span className="px-3 text-sm text-gray-500">or</span>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>

        {/* Browse Button */}
        <div
          className={`inline-flex items-center px-6 py-3 font-medium rounded-lg cursor-pointer transition-all duration-200 shadow-sm select-none transform hover:scale-105 ${
            isDragActive
              ? 'bg-accent2 text-white shadow-lg'
              : 'bg-accent2 hover:bg-accent2-hover text-white hover:shadow-md'
          }`}
          onClick={(e) => {
            e.stopPropagation();
            fileInputRef.current?.click();
          }}
        >
          <Upload className="h-5 w-5 mr-2" />
          Browse Files
        </div>

        {/* File Type Info */}
        <p className="text-xs text-gray-500 mt-4 max-w-sm">Supports: PDF, PNG, JPG, JPEG files</p>

        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          multiple
          accept=".pdf, .png, .jpg, .jpeg, application/pdf, image/png, image/jpeg"
          onChange={(e) => {
            handleFiles && handleFiles(Array.from(e.target.files));
            fileInputRef.current.value = '';
          }}
        />
      </div>
    </div>
  );
}
