import React, { useState, useEffect } from 'react';
import { Icon, Image, Popup, Dropdown, Modal } from 'semantic-ui-react';
import ls from 'local-storage';
import logo from '../../assets/Images/headerIcon.png';
import style from './scss/header.module.scss';
// import { processLogout } from '../utils';
import { mediaBreakpoint } from './MediaBreakPointes';
import defaultLogo from '../../assets/Images/bussiness.png';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Avatar from 'react-avatar';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { decryptData } from '../utils/cryptoUtils';
import darkLogo from '../../assets/Images/darkLogo.png';
import darkText from '../../assets/Images/darkLogo2.png';
import lightLogo from '../../assets/Images/lightLogo.png';
import lightText from '../../assets/Images/lightLogo2.png';
import Notification from '../screens/Notification';
import { closeIcon } from './Icons';
import OmniSageAiLogo from '../../assets/Images/omnisage_ai_logo_transparent.png';

const Header = () => {
  const userName = ls.get('userName');
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;
  const isLoginOrForgetPassword =
    currentPath === '/login' ||
    currentPath === '/forgetPassword' ||
    currentPath === '/500' ||
    currentPath === '/forgetPassword/reset';
  const isHome = currentPath === '/';
  const { headerLogo, userInfo, hasNewNotification, setBusiness } = useAuth();

  const [bussinessLogo, setBussinessLogo] = useState(headerLogo);
  const [businessList, setBusinessList] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState();
  const [dropdownOpen, setDropdown] = useState(false);
  const [noticationActive, setNoticationActive] = useState(false);

  const roleEncripted = ls.get('access_token')?.role;
  const role = roleEncripted && decryptData(roleEncripted);

  useEffect(() => {
    const userId = userInfo?.user_id || userInfo?.userId;
    if (userId) {
      const delayCall = setTimeout(() => {
        GlobalService.generalSelect(
          (respdata) => {
            const { results } = respdata;
            setBusinessList(results);
            setBusiness(results);
            const selectedInLs = ls.get('selectedBusiness');

            if (results?.length > 0) {
              const matchingBusiness = results.find((business) => business?.business_image === headerLogo);

              if (selectedInLs) {
                const bussinessLogo = selectedInLs?.business_image || defaultLogo;
                setBussinessLogo(bussinessLogo);
                setSelectedBusiness(selectedInLs);
              } else {
                if (matchingBusiness) {
                  setSelectedBusiness(matchingBusiness);
                  setBussinessLogo(matchingBusiness.business_image || defaultLogo);
                } else {
                  const bussinessLogo = results[0]?.business_image || defaultLogo;
                  setBussinessLogo(bussinessLogo);
                  setSelectedBusiness(results[0]);
                  ls.set('selectedBusiness', results[0]);
                }
              }
            }
          },
          `${resturls.getBusinesses}`,
          {},
          'GET'
        );
      }, 100);
      return () => clearTimeout(delayCall);
    }
  }, []);

  useEffect(() => {
    if (currentPath.startsWith('/ticketList/') || currentPath.startsWith('/createTicket/')) {
      const businessId = currentPath.split('/')[2];
      const matchingBusiness = businessList.find((b) => b.business_id === businessId);
      if (matchingBusiness) {
        setSelectedBusiness(matchingBusiness);
        setBussinessLogo(matchingBusiness.business_image || defaultLogo);
      }
    }
  }, [currentPath, businessList]);

  // const extractNameFromEmail = (email) => {
  //   if (email && email.includes('@')) {
  //     return email.split('@')[0];
  //   }
  //   return '';
  // };

  // const name = extractNameFromEmail(userName);

  const defaultLogo2 = () => (
    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37323 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37323 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
        fill="#0A0D12"
      />
    </svg>
  );

  const DropdownIcon = () => (
    <svg
      width="8"
      height="6"
      viewBox="0 0 8 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={style.dropdownIcon}
    >
      <path d="M1 1.5L4 4.5L7 1.5" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  );

  const handleNotificationPanel = () => {
    if (isResponsive) {
      navigate('/notification');
    } else {
      setNoticationActive(true);
    }
  };

  const renderNotificationContent = () => {
    return (
      <div className={style.notificationModalContent}>
        <div className={style.desktopHeader}>
          <p>Notifications</p>
          <span onClickCapture={() => setNoticationActive(false)}>{closeIcon()}</span>
        </div>
        <Notification setNoticationActive={setNoticationActive} />
      </div>
    );
  };

  const reportPaths = [
    '/revenue-report',
    '/ledger-report',
    '/expense-report',
    '/receivable-report',
    '/accounts-payables',
    '/inventory-report',
    '/cash-flow',
  ];
  const handleChange = (value) => {
    setSelectedBusiness(value);
    const headerlogo = value.business_image || defaultLogo;
    setBussinessLogo(headerlogo);
    navigate(`/ticketList/${value.business_id}`);
    ls.set('selectedBusiness', value);
    setDropdown(false);
    const currentPath = location.pathname;
    const features = value?.features || [];
    const isReportsEnabled = features.some((feature) => feature.name === 'Reports' && feature.is_active);
    if (reportPaths.includes(currentPath) && !isReportsEnabled) {
      navigate('/');
    } else {
      const keysToRemove = [
        'revenueDetails',
        'revenueTrendLineDetails',
        'revenueDetailedReport',
        'CostReportPayload',
        'CostDetailsTrendlineData',
        'details',
        'inventoryDetails',
        'payableData',
        'customerData',
        'invoiceData',
        'projectCashFLow',
        'receivableDetails',
        'receivableData',
        'Cost Report Page_logs',
        'ledgerTimeline',
        'CostDetailedReport',
        'Revenue Report Page_logs',
        'activeAccountList',
        'ledgerReportList',
        'overviewInfo',
        'transactionInfo',
        'cashFlowData',
        'totalIncome',
        'totalExpense',
      ];

      keysToRemove.forEach((key) => ls.remove(key));

      if (window.location.pathname === '/') {
        window.location.reload();
      } else {
        navigate('/');
      }
    }
  };

  const bussinessOptions = businessList?.map((business, index) => ({
    key: index,
    text: business.business_name,
    value: business,
    logo: business?.business_image,
  }));

  const BusinessDropdown = () => {
    return (
      <>
        <p onClickCapture={() => setDropdown(true)}>
          {selectedBusiness?.business_name} {DropdownIcon()}
        </p>
      </>
    );
  };

  const renderBusinessList = () => {
    if (businessList?.length === 0 || isHome || isLoginOrForgetPassword) return;
    if (businessList?.length > 1) {
      return BusinessDropdown();
    } else {
      return <p>{businessList[0]?.business_name}</p>;
    }
  };

  if (role === 'superuser' && !isResponsive) {
    const icon = isLoginOrForgetPassword ? lightLogo : darkLogo;
    const text = isLoginOrForgetPassword ? lightText : darkText;
    return (
      <div className={`${style.defaultHeader} ${style.adminHeader} ${style.mainContent}`}>
        <div className={style.logoWrappper}>
          <div className={style.mainLogoWrappper} onClickCapture={() => navigate('/')}>
            {/* <div className={`${style.adminIcon} ${style.iconWrapper}`}>
                <Image className={style.logo} src={icon} size='medium' alt="OmniSageAi" />
              </div> */}
            <Image src={OmniSageAiLogo} className={style.textImg} size="medium" alt="OmniSageAi" />
            {/* <p className={style.title}>OmniSageAi</p> */}
          </div>
        </div>
        {!isLoginOrForgetPassword && (
          <div className={style.rightWrapper}>
            {/* <div
              className={style.bellIcon}
              onClickCapture={handleNotificationPanel}
            >
              {bellIcon()}
            </div> */}
            <div className={style.avatarWrapper}>
              <Avatar
                className={style.avatarProfile}
                onClick={() => navigate('/profile')}
                color={isResponsive ? '#011638' : '#293056'}
                name={userName || 'User'}
              />
            </div>
          </div>
        )}
        <Modal
          open={noticationActive}
          onClose={() => setNoticationActive(false)}
          className={`customModal ${style.notificationModal}`}
          size="fullscreen"
        >
          {renderNotificationContent()}
        </Modal>
      </div>
    );
  }

  const isAdmin = ['accountant', 'manager']?.includes(role);
  // const dark = isResponsive ? darkMobile : darkDesktop;
  // const light = isResponsive ? lightMobile : lightDesktop;
  const icon =
    (role !== 'superuser' || isLoginOrForgetPassword) && (!isResponsive || isLoginOrForgetPassword)
      ? lightLogo
      : darkLogo;
  const text = role !== 'superuser' || isLoginOrForgetPassword ? lightText : darkText;
  const selectedLogo = !isLoginOrForgetPassword && isResponsive ? bussinessLogo : icon;

  // const selctedImage =  isAdmin ? light : dark;
  return (
    <div
      className={`${isLoginOrForgetPassword ? style.defaultHeader : ''} ${
        !isLoginOrForgetPassword && isResponsive ? style.bussinesHeader : ''
      } ${style.mainContent}`}
    >
      <div className={style.logoWrappper}>
        {isResponsive && !isLoginOrForgetPassword && !isAdmin && !isHome ? (
          <Image src={selectedLogo} className={style.logo} size="medium" alt="" onClickCapture={() => navigate('/')} />
        ) : (
          <div className={style.mainLogoWrappper} onClickCapture={() => navigate('/')}>
            {!isResponsive || isLoginOrForgetPassword ? (
              <Image src={OmniSageAiLogo} className={style.textImg} size="medium" alt="OmniSageAi" />
            ) : (
              <div className={style.iconWrapper}>
                <Image className={style.logo} src={OmniSageAiLogo} size="medium" alt="OmniSageAi" />
              </div>
            )}
            {/* <p className={style.title}>OmniSageAi</p> */}
          </div>
        )}
        {isResponsive && !isAdmin && <div className={style.selectedBussiness}>{renderBusinessList()}</div>}
      </div>
      {!isLoginOrForgetPassword && (
        <div className={style.rightWrapper}>
          {/* <div
            className={style.bellIcon}
            onClickCapture={handleNotificationPanel}
          >
            {bellIcon()}
          </div> */}
          <div className={style.avatarWrapper}>
            <Avatar
              className={style.avatarProfile}
              onClick={() => navigate('/profile')}
              color={isResponsive ? '#011638' : '#293056'}
              name={userName || 'User'}
            />
          </div>
        </div>
      )}
      {dropdownOpen && <div className="dimmer" onClick={() => setDropdown(false)}></div>}
      <div className={`${style.bussinessListConatiner} ${dropdownOpen ? style.activeListContainer : ''}`}>
        <p>Your Organisations{` (${bussinessOptions?.length})`}</p>
        {bussinessOptions?.map((item) => {
          return (
            <div
              className={`${style.menuItem} ${selectedBusiness?.business_name === item?.text ? style.activeMenu : ''}`}
              onClickCapture={() => handleChange(item?.value)}
            >
              <div className={style.logoWrapper}>{item?.logo ? <Image src={item?.logo} /> : defaultLogo2()}</div>
              <p>{item?.text}</p>
            </div>
          );
        })}
      </div>
      <Modal
        open={noticationActive}
        onClose={() => setNoticationActive(false)}
        className={`customModal ${style.notificationModal}`}
        size="fullscreen"
      >
        {renderNotificationContent()}
      </Modal>
    </div>
  );
};

export default Header;
