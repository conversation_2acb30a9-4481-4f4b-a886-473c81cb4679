@import '../../../../../../assets/scss/main.scss';

.ticketListContainer {
  width: 100%;
  background-color: $white;
  border-radius: 10px;
  box-shadow: 0 0 1px 1px $borderColor;
  margin-top: 2em;
  min-height: 40vh;
  @include for_media(mobileScreen) {
    margin: 1em;
    width: auto;
  }
}

.headerPart {
  padding: 1.5em;
  display: flex;
  justify-content: space-between;
  .btnWrapper {
    display: flex;
    gap: 1em;
    .downloadBtn {
      display: flex;
      gap: 1em;
      align-items: center;
      width: 10em;
      height: 2.5em;
      border-radius: 35px;
      border: 1px solid #d5d7da;
      justify-content: center;
      color: #293056;
      font-size: 1.4em;
      svg {
        width: 25px;
        height: 25px;
      }
    }
    .viewAllBtn {
      display: flex;
      gap: 1em;
      align-items: center;
      width: 8em;
      height: 2.5em;
      border-radius: 35px;
      border: 1px solid #9ea5d1;
      justify-content: center;
      color: #293056;
      background-color: #eaecf5;
      font-size: 1.4em;
      cursor: pointer;
      svg {
        width: 25px;
        height: 25px;
      }
      @include for_media(mobileScreen) {
        font-size: 1.1em;
      }
    }
    .addBtn {
      display: flex;
      gap: 1em;
      align-items: center;
      width: 10em;
      height: 2.5em;
      border-radius: 35px;
      border: 1px solid #d5d7da;
      justify-content: center;
      color: $white;
      background-color: #4e5ba6;
      font-size: 1.4em;
      svg {
        width: 25px;
        height: 25px;
      }
    }
  }
}
