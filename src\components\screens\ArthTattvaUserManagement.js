import React, { useEffect, useState } from 'react';
import style from './scss/arthTattvaUsers.module.scss';
import { useNavigate } from 'react-router-dom';

import { Image, Table, Card, Checkbox, Input, Popup, Dropdown, Modal, Form, Button } from 'semantic-ui-react';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import {
  deactiveUserIcon,
  deleteIcon,
  downloadIcon,
  dropdownIcon,
  DropdownIcon,
  editIcon,
  lockIcon,
  mailIcon,
  orgIcon,
  orLineSvg,
  phoneIcon,
  superUserIcon,
  threeDotIcon,
} from '../global/Icons';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import infoIcon from '../../assets/Images/infoIcon.png';

const ArthTattvaUserManagement = () => {
  // const [ businessList, setBusinessList ] = useState();
  const [activeModal, setActiveModal] = useState();
  const [activePopup, setActivePopup] = useState();
  const [selctedInfo, setSelectedInfo] = useState();
  const [initialValues, setInitialValues] = useState({
    server: '',
    email: '',
    password: '',
  });
  const [step, setStep] = useState({
    step1: true,
    step2: false,
    step3: false,
    active: 'step1',
  });
  const navigate = useNavigate();

  const statusOptions = [
    { key: 'open', value: 'Open', text: 'Open', className: style.openStatus },
    { key: 'pending', value: 'Pending', text: 'Pending', className: style.pendingStatus },
    { key: 'closed', value: 'Closed', text: 'Closed', className: style.closedStatus },
    { key: 'verified', value: 'Verified', text: 'Verified', className: style.verifiedStatus },
    { key: 'deleted', value: 'Deleted', text: 'Deleted', className: style.deletedStatus },
  ];
  const roleOptions = [
    { key: 'Business user', value: 'Business user', text: 'Business user' },
    { key: 'Business super user', value: 'Business superuser', text: 'Business super user' },
    { key: 'Accountant', value: 'Accountant', text: 'Accountant' },
    { key: 'Manager', value: 'Manager', text: 'Manager' },
  ];

  const reportingManagerList = [
    { key: 'Deepali Sethi', value: 'Deepali Sethi', text: 'Deepali Sethi' },
    { key: 'Deepali Sethi', value: 'Deepali Sethi', text: 'Deepali Sethi' },
    { key: 'Deepali Sethi', value: 'Deepali Sethi', text: 'Deepali Sethi' },
    { key: 'Deepali Sethi', value: 'Deepali Sethi', text: 'Deepali Sethi' },
  ];

  const organisationList = [
    {
      name: 'Deepali Sethi',
      logo: null,
      userId: '*********',
      role: 'Super User',
      status: 'active',
      ticket: '3 Active  ·  2 Pending',
    },
    {
      name: 'Deepali Sethi',
      logo: null,
      userId: '*********',
      role: 'Super User',
      status: 'active',
      ticket: '3 Active  ·  2 Pending',
    },
    {
      name: 'Deepali Sethi',
      logo: null,
      userId: '********',
      role: 'Super User',
      status: 'Inactive',
      ticket: '3 Active  ·  2 Pending',
    },
  ];

  const formik = useFormik({
    initialValues: initialValues,
    // validationSchema,
    onSubmit: (values) => {
      GlobalService.generalSelect(
        (respdata) => {
          // navigate('/')
        },
        `${resturls.createBusiness}`,
        values,
        'POST'
      );
    },
  });

  const handleActiveModal = (key, info) => {
    // if(key === "add"){
    //     setStep({...step, step2: true})
    // }
    if (key === 'edit') {
      setSelectedInfo(info);
      // setInitialValues(info)
    }
    setActiveModal(key);
    setActivePopup(false);
  };

  const renderPopupContent = (info) => {
    const list = [
      { name: 'Reset password', icon: lockIcon(), key: 'reset' },
      { name: 'Edit User Info', icon: editIcon(), key: 'edit' },
      { name: 'Deactivate User', icon: deactiveUserIcon(), key: 'delete' },
    ];
    return (
      <div className={style.popupList}>
        {list?.map((item) => {
          return (
            <div className={style.popupItem} onClickCapture={() => handleActiveModal(item?.key, info)}>
              {item?.icon} <p>{item?.name}</p>
            </div>
          );
        })}
      </div>
    );
  };

  const renderLogo = (logo) => {
    if (logo) {
      return <Image src={logo} />;
    }
    return orgIcon();
  };

  const renderList = () => {
    return (
      <Table basic="very">
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell>
              <div className="customCheckBox ">
                <Checkbox indeterminate className={`${style.checkbox}`} />
              </div>
            </Table.HeaderCell>
            <Table.HeaderCell className={style.subjectheaderRow}>User Name & ID</Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
            <Table.HeaderCell>ID</Table.HeaderCell>
            <Table.HeaderCell>Role</Table.HeaderCell>
            <Table.HeaderCell>Tickets</Table.HeaderCell>
            <Table.HeaderCell></Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {organisationList?.map((data) => (
            <Table.Row>
              <Table.Cell>
                <div className="customCheckBox">
                  <Checkbox className={`${style.checkbox}`} />
                </div>
              </Table.Cell>
              <Table.Cell className={style.userInfo}>
                <div className={style.userInfoWrapper}>
                  <div className={style.logo}>{renderLogo(data?.logo)}</div>
                  <div>
                    <p className={style.userName}>{data?.name}</p>
                    <span className={data?.status === 'active' ? style.activeStatus : style.inActive}>
                      {data?.status}
                    </span>
                  </div>
                </div>
              </Table.Cell>
              <Table.Cell>
                <div className={style.contactInfo}>
                  <div className={style.iconWrapper}>
                    <Popup
                      inverted
                      className={style.popup}
                      trigger={<span>{phoneIcon()}</span>}
                      content={
                        <div className={style.popupContent}>
                          <p className={style.label}>Phone Number</p>
                          <p>+91 96542864596</p>
                        </div>
                      }
                      position="top right"
                      hoverable
                    />
                    <Popup
                      inverted
                      className={style.popup}
                      trigger={<span>{mailIcon()}</span>}
                      content={
                        <div className={style.popupContent}>
                          <p className={style.label}>Email</p>
                          <p><EMAIL></p>
                        </div>
                      }
                      position="top right"
                      hoverable
                    />
                  </div>
                </div>
              </Table.Cell>
              <Table.Cell>{data?.userId}</Table.Cell>
              <Table.Cell>{data?.role}</Table.Cell>
              <Table.Cell>{data?.ticket}</Table.Cell>
              <Table.Cell>
                <div className={style.dotContainer}>
                  <Popup
                    content={renderPopupContent(data)}
                    on="click"
                    pinned
                    position="bottom right"
                    trigger={threeDotIcon()}
                    onOpen={() => setActivePopup(data?.userId)}
                    onClose={() => setActivePopup(false)}
                    className={style.popup}
                    open={activePopup === data?.userId}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    );
  };

  const renderButtons = (content, className) => {
    console.log(activeModal, 'activeModal');

    const handleStep = () => {
      console.log(step?.active, 'step?.active');

      if (step?.active === 'step1') {
        setStep({ ...step, active: 'step2', step2: 'true' });
      } else if (step?.active === 'step2') {
        setStep({ ...step, active: 'step3', step3: 'true' });
      }
    };

    const handleStepBack = () => {
      if (step?.active === 'step2') {
        setStep({ ...step, active: 'step1', step2: 'true' });
      } else if (step?.active === 'step3') {
        setStep({ ...step, active: 'step2', step3: 'true' });
      }
    };

    if (activeModal === 'add' || activeModal === 'e') {
      if (step?.active !== 'step1') {
        return (
          <div className={`${style.type2Btns} ${style.buttonWrapper}`}>
            <Button type="button" className={style.emptyCancel} onClick={() => setActiveModal(false)}>
              Cancel
            </Button>
            <div className={style.subBtnWrapper}>
              <Button type="button" className={style.backBtn} onClick={() => handleStepBack()}>
                Back
              </Button>
              <Button type="button" onClick={() => handleStep()} className={`${className} ${style.nextBtn}`}>
                {step?.active === 'step3' ? 'Save' : 'Next'}
              </Button>
            </div>
          </div>
        );
      }
      return (
        <div className={`${style.type2Btns} ${style.buttonWrapper}`}>
          <Button type="button" className={style.emptyCancel} onClick={() => setActiveModal(false)}>
            Cancel
          </Button>
          <Button type="button" onClick={() => handleStep()} className={`${className} ${style.nextBtn}`}>
            {step?.active === 'step3' ? 'Save' : 'Next'}
          </Button>
        </div>
      );
    }
    return (
      <div className={style.buttonWrapper}>
        <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
          Cancel
        </Button>
        <Button type="submit" className={`${className} ${style.nextBtn}`}>
          {content || 'Save Details'}
        </Button>
      </div>
    );
  };

  const options = [
    { key: 'user1', value: 'user1', text: 'User 1' },
    { key: 'user2', value: 'user2', text: 'User 2' },
    { key: 'user3', value: 'user3', text: 'User 3' },
    { key: 'user4', value: 'user4', text: 'User 4' },
  ];

  const addUserModal = () => {
    if (step?.active === 'step3') {
      return (
        <div className={style.formContainer}>
          <Form.Field className={style.formField}>
            <label>Tally User ID</label>
            <Dropdown
              id="tallyId"
              name="tallyId"
              placeholder="Select tally user id"
              className="customDropdown4"
              fluid
              multiple
              search
              selection
              icon={<DropdownIcon />}
              options={options}
              // onChange={handleChange}
              onBlur={formik.handleBlur}
              renderLabel={() => null}
              error={
                formik.touched.tallyId && formik.errors.tallyId
                  ? { content: formik.errors.tallyId, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>
        </div>
      );
    }
    if (step?.active === 'step2') {
      return (
        <div className={style.formContainer}>
          <Form.Field className={style.formField}>
            <label>Role</label>
            <Dropdown
              id="role"
              name="role"
              placeholder="Select role"
              className="customDropdown4"
              fluid
              multiple
              search
              selection
              icon={<DropdownIcon />}
              options={options}
              // onChange={handleChange}
              onBlur={formik.handleBlur}
              renderLabel={() => null}
              error={
                formik.touched.role && formik.errors.role ? { content: formik.errors.role, pointing: 'below' } : null
              }
            />
          </Form.Field>
          <Form.Field className={style.formField}>
            <label>Reporting Manager</label>
            <Dropdown
              id="reportingManager"
              name="reportingManager"
              placeholder="Select reporting manager"
              className="customDropdown4"
              fluid
              multiple
              search
              selection
              icon={<DropdownIcon />}
              options={options}
              // onChange={handleChange}
              onBlur={formik.handleBlur}
              renderLabel={() => null}
              error={
                formik.touched.reportingManager && formik.errors.reportingManager
                  ? { content: formik.errors.users, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>
          <Form.Field className={style.formField}>
            <label>Primary Service Provider</label>
            <Dropdown
              id="primaryService"
              name="primaryService"
              placeholder="Primary Service Provider"
              className="customDropdown4"
              fluid
              multiple
              search
              selection
              icon={<DropdownIcon />}
              options={options}
              // onChange={handleChange}
              onBlur={formik.handleBlur}
              renderLabel={() => null}
              error={
                formik.touched.primaryService && formik.errors.primaryService
                  ? { content: formik.errors.primaryService, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>
        </div>
      );
    }
    return (
      <div className={style.formContainer}>
        <Form.Field className={style.formField}>
          <label>User Name</label>
          <Form.Input
            id="userName"
            name="userName"
            placeholder="Type here"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.userName}
            error={
              formik.touched.userName && formik.errors.userName
                ? { content: formik.errors.userName, pointing: 'below' }
                : null
            }
          />
        </Form.Field>

        <Form.Field className={style.formField}>
          <label>User ID</label>
          <Form.Input
            id="userId"
            name="userId"
            placeholder="Type here"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.userId}
            error={
              formik.touched.userId && formik.errors.userId
                ? { content: formik.errors.userId, pointing: 'below' }
                : null
            }
          />
        </Form.Field>
        <div className={style.fieldWrapper}>
          <Form.Field className={style.formField}>
            <label>Email</label>
            <Form.Input
              id="email"
              name="email"
              placeholder="Type here"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email}
              type="email"
              error={
                formik.touched.email && formik.errors.email ? { content: formik.errors.email, pointing: 'below' } : null
              }
            />
          </Form.Field>
          <Form.Field className={style.formField}>
            <label>Phone</label>
            <Form.Input
              id="phone"
              name="phone"
              placeholder="Type here"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.phone}
              type="phone"
              error={
                formik.touched.phone && formik.errors.phone ? { content: formik.errors.phone, pointing: 'below' } : null
              }
            />
          </Form.Field>
        </div>
      </div>
    );
  };

  const resetPasswordModal = () => (
    <div className={style.formContainer}>
      <Form.Field className={style.formField}>
        <label>New Password</label>
        <Form.Input
          id="password"
          name="password"
          placeholder="Type here"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.password}
          error={
            formik.touched.password && formik.errors.password
              ? { content: formik.errors.password, pointing: 'below' }
              : null
          }
        />
      </Form.Field>
      <Form.Field className={style.formField}>
        <label>Confirm Password</label>
        <Form.Input
          id="confirmPassword"
          name="confirmPassword"
          placeholder="Type here"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.confirmPassword}
          error={
            formik.touched.confirmPassword && formik.errors.confirmPassword
              ? { content: formik.errors.confirmPassword, pointing: 'below' }
              : null
          }
        />
      </Form.Field>
    </div>
  );

  const renderModalContent = () => {
    if (activeModal === 'add') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <h5>Add New ArthTattva User</h5>
          <div className={style.progressWrapper}>
            <div className={style.progress}>
              <span
                className={`${step?.step1 ? style.enableDot : ''} ${step?.active === 'step1' ? style.activeDot : ''} ${style.dot}`}
              />
              <span className={style.line} />
              <span
                className={`${step?.step2 ? style.enableDot : ''} ${step?.active === 'step2' ? style.activeDot : ''} ${style.dot}`}
              />
              <span className={style.line} />
              <span
                className={`${step?.step3 ? style.enableDot : ''} ${step?.active === 'step3' ? style.activeDot : ''} ${style.dot}`}
              />
            </div>
            <div className={style.labelWrapper}>
              <div className={style.label}>
                <span className={style.firstLable}>Basic Info</span>
                <span>Role</span>
                <span>Other Info</span>
              </div>
            </div>
          </div>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {addUserModal()}
            {renderButtons()}
          </Form>
        </div>
      );
    }

    if (activeModal === 'edit') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <h5>Edit User</h5>
          <div className={style.progressWrapper}>
            <div className={style.progress}>
              <span
                className={`${step?.step1 ? style.enableDot : ''} ${step?.active === 'step1' ? style.activeDot : ''} ${style.dot}`}
              />
              <span className={style.line} />
              <span
                className={`${step?.step2 ? style.enableDot : ''} ${step?.active === 'step2' ? style.activeDot : ''} ${style.dot}`}
              />
              <span className={style.line} />
              <span
                className={`${step?.step3 ? style.enableDot : ''} ${step?.active === 'step3' ? style.activeDot : ''} ${style.dot}`}
              />
            </div>
            <div className={style.labelWrapper}>
              <div className={style.label}>
                <span className={style.firstLable}>Basic Info</span>
                <span>Role</span>
                <span>Other Info</span>
              </div>
            </div>
          </div>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {addUserModal()}
            {renderButtons()}
          </Form>
        </div>
      );
    }

    if (activeModal === 'reset') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <h5>Reset Password</h5>
          <div className={style.sendPasswordBtn}>
            <p>Send Password Reset Link</p>
          </div>
          <div className={style.orline}>{orLineSvg()}</div>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {resetPasswordModal()}
            {renderButtons('Reset password')}
          </Form>
        </div>
      );
    }

    if (activeModal === 'superuser') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <div className={style.imgWrapper}>
            <Image src={infoIcon} />
          </div>
          <div className={style.descContent}>
            <h5>Are you sure you want to Activate Deepali Sethi?</h5>
            <p>
              Activating this user will grant them access to the platform. You can deactivate their account at any time
              to revoke access.
            </p>
          </div>
          {renderButtons('Activate', style.grteenBtn)}
        </div>
      );
    }

    if (activeModal === 'delete') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <div className={style.imgWrapper}>
            <Image src={infoIcon} />
          </div>
          <div className={style.descContent}>
            <h5>Are you sure you want to deactivate Deepali Sethi?</h5>
            <p>
              Deactivating this user will revoke their access to the platform. You can reactivate their account to
              restore access at any time.
            </p>
          </div>
          {renderButtons('Deactivate', style.redBtn)}
        </div>
      );
    }
  };

  return (
    <div>
      <Header />
      <div className={style.bussinessListScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>
          <div className={style.headerPart}>
            <div>
              <div className={style.screenName}>
                <h4>ArthTattva Users</h4>
                <span>240</span>
              </div>
              <p className={style.desc}>Assign roles, update permissions, and manage organization users</p>
            </div>
            <div className={style.btnWrapper}>
              <div className={style.downloadBtn}>{downloadIcon()} Download</div>
              <div className={style.addBtn} onClick={() => setActiveModal('add')}>
                Add {dropdownIcon()}
              </div>
            </div>
          </div>
          <div className={style.searchWrapper}>
            <Input className={style.searchInput} icon="search" placeholder="Search" iconPosition="left" />
            <Dropdown
              placeholder="Role"
              className={`customDropdown3 ${style.statusDropdown}`}
              icon={<DropdownIcon />}
              options={roleOptions}
            />
            <Dropdown
              placeholder="Reporting Manager"
              className={`customDropdown3 ${style.statusDropdown}`}
              icon={<DropdownIcon />}
              options={roleOptions}
            />
            <Dropdown
              placeholder="Status"
              className={`customDropdown3 ${style.statusDropdown}`}
              icon={<DropdownIcon />}
              options={reportingManagerList}
            />
          </div>
          <div className={style.tableWrapper}>{renderList()}</div>
        </div>
      </div>
      <Modal basic size="small" open={activeModal} onClose={() => setActiveModal(false)}>
        {renderModalContent()}
      </Modal>
    </div>
  );
};

export default ArthTattvaUserManagement;
