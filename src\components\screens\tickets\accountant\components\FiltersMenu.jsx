import React, { useRef } from 'react';
import style from './scss/filtersMenu.module.scss';
import { CloseIcon, DocumentWithRupeeIcon, OrganizationIcon } from '../../../../../assets/svgs';
import CheckBoxListing from '../../../../generic-components/CheckBoxListing';
import { resturls } from '../../../../utils/apiurls';
import { Button } from 'semantic-ui-react';
import { isEqual } from 'lodash';
import { statusOptions } from '../../../../../constants/filterOptions';

function FiltersMenu({ setIsShowFiltersMenu, setSelectedOptions, selectedOptions, setIsFilterApplied }) {
  const selectedOptionsRef = useRef(selectedOptions);
  const handleOptionChange = (key, value, isChecked) => {
    const filter = { ...selectedOptions };
    if (isChecked) {
      if (filter[key]) {
        filter[key] = [...filter[key], value];
      } else {
        filter[key] = [value];
      }
    } else {
      if (filter[key]) {
        filter[key] = filter[key].filter((filterValue) => filterValue !== value);
        if (filter[key].length === 0) delete filter[key];
      }
    }
    setSelectedOptions(filter);
  };
  return (
    <div className={style.adminFilterConatiner} onClick={(e) => e.stopPropagation()}>
      <div className={style.mainWrapper}>
        <div className={style.headerWrapper}>
          <h5>All Filters</h5>
          <div className={style.closeIconWrapper} onClick={() => setIsShowFiltersMenu(false)}>
            <CloseIcon />
          </div>
        </div>
        <div className={style.filterListContainer}>
          <CheckBoxListing
            label="Category"
            paramsKey="category"
            url={resturls.obtainCategortList}
            transformOptionsObj={{
              key: 'id',
              value: 'name',
              label: 'name',
            }}
            defaultLogo={DocumentWithRupeeIcon}
            onSelectionChange={handleOptionChange}
            selectedValues={selectedOptions?.category || []}
          />
          <CheckBoxListing
            options={statusOptions}
            label="Status"
            paramsKey="status"
            onSelectionChange={handleOptionChange}
            selectedValues={selectedOptions?.status || []}
          />
          <CheckBoxListing
            label="Organization"
            paramsKey="business_id"
            selectedValues={selectedOptions?.business_id || []}
            onSelectionChange={handleOptionChange}
            url={resturls.getBusinesses}
            transformOptionsObj={{
              key: 'business_id',
              value: 'business_id',
              label: 'business_name',
              src: 'business_image',
            }}
            defaultLogo={OrganizationIcon}
            showSearch={true}
            searchParamName="business_name"
            searchPlaceholder="Search organizations..."
          />
        </div>
      </div>
      <div className={style.btnWrapper}>
        <Button
          className={style.cancelBtn}
          onClick={() => {
            setSelectedOptions({});
            setIsFilterApplied(0);
            setIsShowFiltersMenu(false);
          }}
        >
          Clear all
        </Button>
        <Button
          className={style.applyBtn}
          disabled={Object.keys(selectedOptions).length === 0}
          onClick={() => {
            setIsFilterApplied((prev) => {
              if (!isEqual(selectedOptions, selectedOptionsRef.current)) {
                return prev + 1;
              }
              return prev;
            });
            setIsShowFiltersMenu(false);
          }}
        >
          Apply
        </Button>
      </div>
    </div>
  );
}

export default FiltersMenu;
