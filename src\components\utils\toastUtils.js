import { Flip, toast } from 'react-toastify';
import { defaultToastOptions } from './constants';

export const createLoadingToast = (message = 'Loading...') => {
  const toastId = toast.loading(message);

  return function (type, message = 'No message provided') {
    if (type === 'dismiss') {
      toast.dismiss(toastId);
    } else {
      toast.update(toastId, {
        render: message,
        type,
        ...defaultToastOptions,
      });
    }
  };
};

export const PopupToast = (message, toastType = 'info') => {
  toast(message, {
    type: toastType,
    position: 'top-center',
    autoClose: false,
    closeOnClick: false,
    draggable: false,
    transition: Flip,
  });
};
