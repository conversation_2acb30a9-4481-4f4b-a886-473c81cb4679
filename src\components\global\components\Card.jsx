import React from 'react';
import style from './scss/card.module.scss';

function Card({ logo, title, count, bgColor = '#EAECF5', iconColor = '#363F72', className = '', children }) {
  return (
    <div className={`${style.card} ${className}`}>
      <div className={style.infoWrapper}>
        {logo && title && (
          <div className={style.titleWrapper}>
            <div className={style.iconWrapper} style={{ '--bgColor': bgColor, '--iconColor': iconColor }}>
              {logo}
            </div>
            <p>{title}</p>
          </div>
        )}
        {count && <p>{count}</p>}
      </div>
      {children}
    </div>
  );
}

export default Card;
