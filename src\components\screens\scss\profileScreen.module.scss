@import '../../../assets//scss/main.scss';

.profileScreen {
  height: 100vh;
  .detailWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .header {
    padding: 1.5em 1em;
    background-color: $white;
    // position: fixed;
    top: 0;
    width: 100%;
    display: flex;
    svg {
      width: 25px;
      height: 25px;
      cursor: pointer;
    }
    p {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
  .detailsContainer {
    padding: 1.5em;
    background-color: $white;
    box-shadow: 0 0 1px 2px $borderColor;
    margin: 2em;
    border-radius: 15px;
    width: 30%;
    @include for_media(mobileScreen) {
      width: 100%;
      margin: 1em;
    }
  }
  .profileWrapper {
    padding: 1em;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .avatarProfile {
    border-radius: 100%;
    height: 5.5em !important;
    width: 5.5em !important;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $white;
    cursor: pointer;
    // border: 1px solid $primaryColor;
    span {
      font-size: 1.2em;
      color: #363f72 !important;
      font-weight: 900;
    }
  }
  .details {
    margin: 1.5em 0;
    .label {
      color: #535862;
      font-size: 1em;
    }
    p {
      margin: 0.5em 0;
    }
  }
  .logoutBtn {
    width: 100%;
    display: flex;
    height: 2.5em;
    align-items: center;
    justify-content: center;
    gap: 1em;
    font-size: 1.4em;
    box-shadow: 0 0 1px 2px $borderColor;
    border-radius: 35px;
    color: #b42318;
    svg {
      width: 20px;
      height: 20px;
    }
  }
}

.orgListWrapper {
  margin: 1em;
}
.orgItem {
  display: flex;
  gap: 1em;
  margin-top: 1em;
  padding: 1em;
  background-color: $white;
  border-radius: 10px;
  align-items: center;
  justify-content: space-between;
  .leftContent {
    display: flex;
    gap: 1em;
    align-items: center;
  }
  .logo {
    padding: 0.5em;
    box-shadow: 0 0 1px 2px $borderColor;
    border-radius: 10px;
    img,
    svg {
      width: 30px;
      height: 30px;
    }
  }
  p {
    margin: 0;
    font-size: 1em !important;
  }
  .content {
    .name {
      font-weight: 900;
    }
  }
  .viewDetail {
    font-size: 1em !important;
    display: flex;
    color: #363f72;
    align-items: center;
    gap: 0.5em;
    svg {
      width: 15px;
      height: 12px;
    }
  }
}

.orgLogo {
  padding: 1em;
  box-shadow: 0 0 1px 2px #9ea5d1;
  border-radius: 100%;
  background-color: #eaecf5;
  img,
  svg {
    width: 40px;
    height: 40px;
  }
}

.address {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .mapIcon {
    padding: 0.6em 0.3em 0.3em 0.3em;
    border-radius: 10px;
    box-shadow: 0 0 1px 2px $borderColor;
    svg {
      width: 30px;
      height: 30px;
    }
  }
}
.userList {
  display: flex;
  flex-direction: column;
  gap: 1em;
  margin: 1em;
  p {
    margin: 0;
  }
}
.userItem {
  display: flex;
  padding: 1em;
  gap: 1em;
  align-items: center;
  background-color: $white;
  border-radius: 10px;
  .avatarProfile {
    height: 3.5em !important;
    width: 3.5em !important;
    span {
      font-size: 1em !important;
    }
  }
  .info {
    p {
      margin: 0;
      display: inline-block;
    }
  }
}

.contentWrapper {
  overflow-y: auto;
  height: 85vh;
}

.superUser {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  p {
    padding: 0.5em;
    background-color: #363f72;
    border-radius: 25px;
    color: $white;
    width: 6em;
    font-size: 1em !important;
  }
}

.paginationWrapper {
  padding: 2em 0;
}

.loaderContainer {
  height: 35vh;
  position: relative;
}
