@import '../../../../assets/scss/main.scss';

.card {
  display: flex;
  flex-direction: column;
  gap: 1.5em;
  padding: 1.5em;
  background-color: #fff;
  border-radius: 0.5em;
  width: 100%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  user-select: none;
  transition: transform 0.3s ease-in-out;

  .infoWrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .titleWrapper {
      display: flex;
      align-items: center;
      gap: 0.8em;

      .iconWrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
        border-radius: 100%;

        &::before {
          content: '';
          height: 2.5em;
          width: 2.5em;
          border-radius: 50%;
          position: absolute;
          left: -5px;
          background-color: var(--bgColor, #eaecf5) !important;
        }

        svg {
          height: 35px;
          width: 35px;
          fill: var(--iconColor, '#363F72') !important;
          z-index: 100;
        }
      }
    }
  }

  @include for_media(tabletScreen) {
    max-width: 100%;
    padding: 1.2em;
  }

  @include for_media(mobileScreen) {
    padding: 1em;
    .infoWrapper {
      flex-direction: column;
      align-items: flex-start;
    }
  }
}
