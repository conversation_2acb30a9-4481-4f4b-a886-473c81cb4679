import React from 'react';
import { Pie } from 'react-chartjs-2';
import { Chart, ArcElement, Tooltip, Legend } from 'chart.js';

Chart.register(ArcElement, Tooltip, Legend);

const TicketStatusPieChart = ({ ticketCountInfo = [] }) => {
  if (!ticketCountInfo || ticketCountInfo.length === 0) return <></>;

  const { labels, values, colors } = ticketCountInfo.reduce(
    (acc, item) => {
      acc.labels.push(item.status);
      acc.values.push(item.value);
      acc.colors.push(item.color);
      return acc;
    },
    { labels: [], values: [], colors: [] }
  );

  const data = {
    labels,
    datasets: [
      {
        data: values,
        backgroundColor: colors,
        borderColor: 'transparent',
        borderWidth: 0,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 300,
      easing: 'easeInOutQuad',
    },
    plugins: {
      legend: {
        display: false,
      },
    },
  };

  return <Pie data={data} options={options} />;
};

export default React.memo(TicketStatusPieChart);
