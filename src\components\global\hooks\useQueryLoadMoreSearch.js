import { useMemo } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import apiClient from '../../services/apiClient';
import useDebounce from './useDebounce';

const useQueryLoadMoreSearch = ({
  url,
  searchParam = 'search',
  searchValue = '',
  debounceDelay = 500,
  enabled = true,
  additionalParams = {},
  ...queryOptions
}) => {
  const debouncedSearchValue = useDebounce(searchValue, debounceDelay);

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, error, refetch } = useInfiniteQuery({
    queryKey: ['query-pagination-search', url, debouncedSearchValue],
    queryFn: async ({ pageParam = 1 }) => {
      const res = await apiClient.get(url, {
        params: {
          [searchParam]: debouncedSearchValue,
          page: pageParam,
          ...additionalParams,
        },
      });

      let items = [];
      let nextPage = null;
      if (res?.pagination) {
        items = res.data;
        const pagination = res.pagination || {};
        const currentPage = pagination.page || 1;
        const totalPages = pagination.total_pages || 1;
        if (currentPage < totalPages) {
          nextPage = pagination.page + 1;
        }
      } else if (res?.results) {
        items = res.results;
        if (res?.next) {
          const nextUrl = new URL(res.next);
          const pageParam = nextUrl.searchParams.get('page');
          nextPage = pageParam ? parseInt(pageParam) : null;
        }
      }

      return {
        items,
        nextPage,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled: enabled && !!url,
    ...queryOptions,
  });

  const options = useMemo(() => {
    const flatOptions = data?.pages.flatMap((page) => page.items) || [];
    return hasNextPage ? [...flatOptions, { loadMore: true }] : flatOptions;
  }, [data, hasNextPage]);

  const loadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  return {
    options,
    data: data?.pages.flatMap((page) => page.items) || [],
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    loadMore,
    refetch,
  };
};

export default useQueryLoadMoreSearch;
