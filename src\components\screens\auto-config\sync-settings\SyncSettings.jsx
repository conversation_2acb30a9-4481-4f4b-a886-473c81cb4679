import React, { useEffect, useMemo, useState } from 'react';
import AccountingPlatform from './components/AccountingPlatform';
import { getSyncStatus, getZohoConfigs } from '../../../services/syncSettingsServices';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../../../contexts/AuthContext';
import LoadingWrapper from '../../../global/components/LoadingWrapper';
import TallyFields from './components/tally/TallyFields';
import { useOutletContext } from 'react-router-dom';
import ZohoFields from './components/zoho/ZohoFields';
import SyncStatus from './components/zoho/SyncStatus';

const SyncSettings = () => {
  const { businessPreferences, businessPreferencesLoading } = useOutletContext();
  const { globSelectedBusiness } = useAuth();
  const [selectedPlatform, setSelectedPlatform] = useState({
    id: businessPreferences?.platform_details?.value,
    platform_name: businessPreferences?.platform_details?.platform_name,
  });
  const [isConnected, setIsConnected] = useState(false);

  const {
    data: zohoConfigs,
    status: zohoConfigsStatus,
    isLoading: zohoConfigsLoading,
  } = useQuery({
    queryKey: ['zoho-configs', globSelectedBusiness?.business_id],
    queryFn: () => getZohoConfigs(globSelectedBusiness?.business_id),
    refetchOnMount: true,
    enabled: businessPreferences?.platform_details?.platform_name?.toLowerCase() === 'zoho',
    retry: false,
    staleTime: 0,
    gcTime: 0,
  });
  const {
    data: syncData,
    isLoading: syncDataLoading,
    refetch: refetchSyncStatus,
  } = useQuery({
    queryKey: ['sync-status', globSelectedBusiness?.business_id],
    refetchOnMount: true,
    queryFn: () => getSyncStatus(globSelectedBusiness?.business_id),
    enabled: !!globSelectedBusiness?.business_id,
    retry: false,
    staleTime: 0,
    gcTime: 0,
  });

  useEffect(() => {
    if (zohoConfigsStatus === 'success') {
      setIsConnected(zohoConfigs?.status?.toLowerCase() === 'connected' || false);
    }
    if (zohoConfigsStatus === 'error') {
      setIsConnected(false);
    }
  }, [zohoConfigsStatus]);

  const platform = useMemo(() => {
    return selectedPlatform?.platform_name?.toLowerCase();
  }, [selectedPlatform]);

  return (
    <div className="relative h-full py-8 pt-0 px-4 mx-auto">
      <LoadingWrapper loading={zohoConfigsLoading || businessPreferencesLoading || syncDataLoading}>
        <AccountingPlatform
          selectedPlatform={selectedPlatform}
          setSelectedPlatform={setSelectedPlatform}
          isConnected={isConnected}
          refetchSyncStatus={refetchSyncStatus}
          syncProgress={syncData?.sync_progress || 100}
          didSyncFailed={syncData?.sync_status?.toLowerCase() === 'failed'}
        />

        {platform === 'zoho' && (
          <div className="flex justify-between gap-6">
            <div className="min-w-[55%]">
              <ZohoFields
                setIsConnected={setIsConnected}
                isConnected={isConnected}
                zohoConfigs={zohoConfigs}
                syncData={syncData}
              />
            </div>

            <div className="w-full">
              <SyncStatus syncData={syncData} />
            </div>
          </div>
        )}
        {platform === 'tallyprime' && <TallyFields />}
      </LoadingWrapper>
    </div>
  );
};

export default SyncSettings;
