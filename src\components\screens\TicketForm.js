// TicketForm.js
import React, { useState, useRef, useEffect } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Form, Button, Input, TextArea, Loader, Image, Label, Icon } from 'semantic-ui-react';
import style from './scss/ticketCreation.module.scss';
import { toast } from 'react-toastify';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import pdfIcon from '../../assets/Images/pdfIcon.png';
import docIcon from '../../assets/Images/doc.png';
import excelIcon from '../../assets/Images/xls.png';
import ls from 'local-storage';
import { useDropzone } from 'react-dropzone';
import SearchableFormSelect from '../custom-components/SearchableFormSelect';
import { decryptData } from '../utils/cryptoUtils';
import { CloseIcon } from '../../assets/svgs';
import FilePreview from '../custom-components/FilePreview';
import { Upload } from 'lucide-react';

const TicketForm = ({
  idList,
  commentScreen,
  editIdList,
  setModalAction,
  obtainUsersList,
  initialValues,
  organisationList,
  categoryList,
  onSubmit,
  hideFields,
  userList,
  loadingFiles,
  handleFileUpload,
  loadMoreCategory,
  loadMoreItems,
  data,
}) => {
  const [previews, setPreviews] = useState([]);
  const fileInputRef = useRef(null);
  const [fileIdList, setFileInfo] = useState([]);
  const [previewFiles, setPreviewFiles] = useState([]);
  const [activeCamera, setActiveCamera] = useState(false);
  const [subCategoryList, setSubCategoryList] = useState([]);
  const [requestUserList, setRequestUsers] = useState([]);
  const [errorList, setErrorsList] = useState([]);
  const videoRef = useRef(null);
  const dropdownRef = useRef(null);
  const categoryRef = useRef(null);
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const roleEncripted = ls.get('access_token')?.role;

  const userListOption = userList;

  const role = roleEncripted && decryptData(roleEncripted);

  useEffect(() => {
    if (data?.attachements?.length > 0) {
      setPreviewFiles(data?.attachements);
      const newIdList = data?.attachements?.map((info) => info?.id);
      editIdList(newIdList);
      handlePreviewFiles(data?.attachements);
    }
  }, [data]);

  useEffect(() => {
    if (previewFiles?.length > 0) {
      handlePreviewFiles(previewFiles);
    }
  }, [previewFiles]);

  const handlePreviewFiles = (attachements) => {
    const existingPreviews = attachements.map((file) => {
      if (file) {
        const fileUrl = file?.url || file.file;
        const fileName = fileUrl?.split('/').pop();
        const isImage = /\.(jpg|jpeg|png|gif)$/i.test(fileName);
        const isPdf = /\.pdf$/i.test(fileName); // Check for PDF files
        const isDoc = /\.(doc|docx)$/i.test(fileName); // Check for DOC/DOCX files
        const isXls = /\.(xls|xlsx)$/i.test(fileName); // Check for XLS/XLSX files

        if (isImage) {
          return { url: fileUrl, name: fileName, type: 'image' };
        } else if (isPdf) {
          return { url: fileUrl, name: fileName, type: 'pdf' };
        } else if (isDoc) {
          return { url: fileUrl, name: fileName, type: 'doc' }; // For DOC/DOCX files
        } else if (isXls) {
          return { url: fileUrl, name: fileName, type: 'xls' }; // For XLS/XLSX files
        } else {
          return { name: fileName, icon: getFileIcon(fileName), type: 'other' }; // Default for other file types
        }
      }
    });

    setPreviews(existingPreviews);
  };

  const obtainAllUserList = (id) => {
    if (id) {
      GlobalService.generalSelect(
        (respdata) => {
          if (respdata && respdata.results) {
            const { results } = respdata;
            const list = results?.filter(
              (item) => item?.role === 'business_user' || item?.role === 'business_superuser'
            );
            const updatedList = list.map((user) => ({
              key: Number(user.id),
              value: Number(user.id),
              text: user.full_name || user.email,
            }));
            setRequestUsers(updatedList);
          }
        },
        `${resturls.obtainUsersList}${id}`,
        {},
        'GET'
      );
    }
  };

  useEffect(() => {
    if (data?.business?.business_id) {
      obtainAllUserList(data?.business?.business_id);
    }
  }, [data]);

  const handleOnfocus = () => {
    const id = formik.values.organization;
    if (!id) {
      toast.error('you must need to select organization');
    }
  };

  const validationSchema = Yup.object({
    assignUser: Yup.string().required('User is required'),
    organization: Yup.string().required('Organization is required'),
  });

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: validationSchema,
    onSubmit: (values) => onSubmit(values, fileIdList),
  });

  const handleFileChange = (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;
    formik.setFieldValue('attachments', [...formik.values.attachments, ...files]);

    handleFileUpload(files, setFileInfo, setPreviewFiles);
  };

  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
      case 'pdf':
        return <Icon name="file pdf outline" size="large" color="red" />;
      case 'docx':
      case 'doc':
        return <Icon name="file word outline" size="large" color="blue" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <Icon name="file image outline" size="large" color="green" />;
      default:
        return <Icon name="file outline" size="large" />;
    }
  };

  const handleDivClick = (() => {
    let isTriggered = false;
    return () => {
      if (isTriggered) return;
      isTriggered = true;
      if (fileInputRef?.current && typeof fileInputRef?.current?.click === 'function') {
        fileInputRef?.current?.click();
      }
      setTimeout(() => {
        isTriggered = false;
      }, 300);
    };
  })();

  const handleDropdownChange = (e, { value }) => {
    const isAdmin = ['manager', 'accountant', 'superuser'].includes(role);

    if (value === 'load_more') {
      loadMoreItems();
      if (dropdownRef.current) {
        dropdownRef.current.setState({ open: true });
      }
      return;
    }
    formik.setFieldValue('organization', value);
    if (isAdmin) {
      const errors = errorList?.filter((info) => info !== 'organization');
      setErrorsList(errors);
    }
    obtainUsersList(value);
    obtainAllUserList(value);
  };

  const handleOnchange = (key, value) => {
    const isAdmin = ['manager', 'accountant', 'superuser'].includes(role);
    if (key === 'assignToMe') {
      formik.setFieldValue('assignToMe', !formik.values.assignToMe);
      formik.setFieldValue('assignTo', '');
      if (isAdmin) {
        const errors = errorList?.filter((info) => info !== 'assignTo');
        setErrorsList(errors);
      }
      return;
    }
    formik.setFieldValue(key, value);
    if (isAdmin) {
      const errors = errorList?.filter((info) => info !== key);
      setErrorsList(errors);
    }
  };
  const handleCategoryChange = (e, { value }) => {
    const isAdmin = ['manager', 'accountant', 'superuser'].includes(role);
    if (value === 'load_more') {
      loadMoreCategory();
      if (categoryRef.current) {
        categoryRef.current.setState({ open: true });
      }
      return;
    }
    formik.setFieldValue('category', value);
    if (isAdmin) {
      const errors = errorList?.filter((info) => info !== 'category');
      setErrorsList(errors);
    }
    obtainSubCategory(value);
  };

  const handleSubjectChange = (e, { value }) => {
    formik.setFieldValue('subject', value);
    if (!value.trim()) {
      setErrorsList([...errorList, 'subject']);
    } else {
      setErrorsList(errorList?.filter((info) => info !== 'subject'));
    }
  };

  const uploadIcon = () => (
    <div className={style.iconContainer}>
      <svg width="17" height="20" viewBox="0 0 17 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M3.32812 1.75C2.9966 1.75 2.67866 1.8817 2.44424 2.11612C2.20982 2.35054 2.07812 2.66848 2.07812 3V17C2.07812 17.3315 2.20982 17.6495 2.44424 17.8839C2.67866 18.1183 2.9966 18.25 3.32812 18.25H13.3281C13.6596 18.25 13.9776 18.1183 14.212 17.8839C14.4464 17.6495 14.5781 17.3315 14.5781 17V6.75H11.3281C10.864 6.75 10.4189 6.56563 10.0907 6.23744C9.7625 5.90925 9.57812 5.46413 9.57812 5V1.75H3.32812ZM11.0781 2.81066L13.5175 5.25H11.3281C11.2618 5.25 11.1982 5.22366 11.1513 5.17678C11.1045 5.12989 11.0781 5.0663 11.0781 5V2.81066ZM1.38358 1.05546C1.89931 0.539731 2.59878 0.25 3.32812 0.25H10.3281C10.527 0.25 10.7178 0.329018 10.8585 0.46967L15.8585 5.46967C15.9991 5.61032 16.0781 5.80109 16.0781 6V17C16.0781 17.7293 15.7884 18.4288 15.2727 18.9445C14.7569 19.4603 14.0575 19.75 13.3281 19.75H3.32812C2.59878 19.75 1.89931 19.4603 1.38358 18.9445C0.867856 18.4288 0.578125 17.7293 0.578125 17V3C0.578125 2.27065 0.867856 1.57118 1.38358 1.05546ZM4.57812 11C4.57812 10.5858 4.91391 10.25 5.32812 10.25H11.3281C11.7423 10.25 12.0781 10.5858 12.0781 11C12.0781 11.4142 11.7423 11.75 11.3281 11.75H5.32812C4.91391 11.75 4.57812 11.4142 4.57812 11ZM4.57812 15C4.57812 14.5858 4.91391 14.25 5.32812 14.25H11.3281C11.7423 14.25 12.0781 14.5858 12.0781 15C12.0781 15.4142 11.7423 15.75 11.3281 15.75H5.32812C4.91391 15.75 4.57812 15.4142 4.57812 15Z"
          fill="#181D27"
        />
      </svg>
    </div>
  );

  const handleSubmit = (values, fileIdList, event) => {
    event.preventDefault();
    const isAdmin = ['manager', 'accountant', 'superuser'].includes(role);
    const errors = [];
    let errorMessage = ''; // To accumulate all error messages
    if (isAdmin && !commentScreen) {
      if (!values.organization || values.organization?.length == 0) {
        errors.push('organization');
        errorMessage += 'Organization is required. '; // Add to error message
      }

      if (!values.assignToMe && (!values.assignTo || values.assignTo?.length == 0)) {
        errors.push('assignTo');
        errorMessage += 'Assign to is required. '; // Add to error message
      }

      if (values.assignToMe) {
        const index = errors.indexOf('assignTo');
        if (index > -1) {
          errors.splice(index, 1); // Remove "assignTo" from the errors array
        }
      }
      if (!values.category || values.category?.length == 0) {
        errors.push('category');
        errorMessage += 'Category By is required. '; // Add to error message
      }
      //TODO: dublicate toast
    }
    if (values.category === '') {
      delete values.category;
    }
    if (!values.subject?.trim() || values.subject?.length === 0) {
      errors.push('subject');
    }
    setErrorsList(errors);
    if (errors.length > 0) {
      return;
    }
    onSubmit(values, fileIdList);
  };

  const handleCaptureImage = async () => {
    try {
      if (videoRef?.current?.srcObject) {
        stopStream(videoRef.current.srcObject);
      }
      const facingMode = isResponsive ? 'environment' : 'user';
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode },
      });
      videoRef.current.srcObject = stream;
      setActiveCamera(true);
    } catch (error) {
      toast.error('Unable to access the camera');
      console.error(error);
    }
  };

  const capturePhoto = () => {
    setActiveCamera(false);
    const canvas = document.createElement('canvas');
    const video = videoRef?.current;
    canvas.width = video?.videoWidth;
    canvas.height = video?.videoHeight;
    canvas?.getContext('2d')?.drawImage(video, 0, 0, canvas.width, canvas.height);
    const imageUrl = canvas?.toDataURL('image/png');
    const timestamp = new Date()?.toISOString();
    const fileName = `captured_image${timestamp}.png`;
    setPreviews((prevPreviews) => [...prevPreviews, { url: imageUrl, name: fileName }]);
    formik.setFieldValue('attachments', [...formik.values.attachments, imageUrl]);
    handleFileUpload([canvas], fileName, setFileInfo);
    video?.srcObject?.getTracks()?.forEach((track) => track?.stop());
  };

  const stopStream = (stream) => {
    stream.getTracks().forEach((track) => track.stop());
  };

  const camera = () => (
    <div className={style.iconContainer}>
      <svg width="21" height="18" viewBox="0 0 21 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M7.66406 1.75C7.59776 1.75 7.53417 1.77634 7.48729 1.82322C7.4404 1.87011 7.41406 1.9337 7.41406 2C7.41406 2.72935 7.12433 3.42882 6.60861 3.94454C6.09288 4.46027 5.39341 4.75 4.66406 4.75H3.66406C3.33254 4.75 3.0146 4.8817 2.78018 5.11612C2.54576 5.35054 2.41406 5.66848 2.41406 6V15C2.41406 15.3315 2.54576 15.6495 2.78018 15.8839C3.0146 16.1183 3.33254 16.25 3.66406 16.25H17.6641C17.9956 16.25 18.3135 16.1183 18.5479 15.8839C18.7824 15.6495 18.9141 15.3315 18.9141 15V6C18.9141 5.66848 18.7824 5.35054 18.5479 5.11612C18.3135 4.8817 17.9956 4.75 17.6641 4.75H16.6641C15.9347 4.75 15.2352 4.46027 14.7195 3.94454C14.2038 3.42882 13.9141 2.72935 13.9141 2C13.9141 1.9337 13.8877 1.87011 13.8408 1.82322C13.794 1.77634 13.7304 1.75 13.6641 1.75H7.66406ZM6.42663 0.762563C6.75481 0.434375 7.19993 0.25 7.66406 0.25H13.6641C14.1282 0.25 14.5733 0.434375 14.9015 0.762563C15.2297 1.09075 15.4141 1.53587 15.4141 2C15.4141 2.33152 15.5458 2.64946 15.7802 2.88388C16.0146 3.1183 16.3325 3.25 16.6641 3.25H17.6641C18.3934 3.25 19.0929 3.53973 19.6086 4.05546C20.1243 4.57118 20.4141 5.27065 20.4141 6V15C20.4141 15.7293 20.1243 16.4288 19.6086 16.9445C19.0929 17.4603 18.3934 17.75 17.6641 17.75H3.66406C2.93472 17.75 2.23524 17.4603 1.71952 16.9445C1.20379 16.4288 0.914062 15.7293 0.914062 15V6C0.914062 5.27065 1.20379 4.57118 1.71952 4.05546C2.23524 3.53973 2.93472 3.25 3.66406 3.25H4.66406C4.99558 3.25 5.31353 3.1183 5.54795 2.88388C5.78237 2.64946 5.91406 2.33152 5.91406 2C5.91406 1.53587 6.09844 1.09075 6.42663 0.762563ZM8.01241 7.34835C8.71567 6.64509 9.6695 6.25 10.6641 6.25C11.6586 6.25 12.6125 6.64509 13.3157 7.34835C14.019 8.05161 14.4141 9.00544 14.4141 10C14.4141 10.9946 14.019 11.9484 13.3157 12.6517C12.6125 13.3549 11.6586 13.75 10.6641 13.75C9.6695 13.75 8.71567 13.3549 8.01241 12.6517C7.30915 11.9484 6.91406 10.9946 6.91406 10C6.91406 9.00544 7.30915 8.05161 8.01241 7.34835ZM10.6641 7.75C10.0673 7.75 9.49503 7.98705 9.07307 8.40901C8.65112 8.83097 8.41406 9.40326 8.41406 10C8.41406 10.5967 8.65112 11.169 9.07307 11.591C9.49503 12.0129 10.0673 12.25 10.6641 12.25C11.2608 12.25 11.8331 12.0129 12.2551 11.591C12.677 11.169 12.9141 10.5967 12.9141 10C12.9141 9.40326 12.677 8.83097 12.2551 8.40901C11.8331 7.98705 11.2608 7.75 10.6641 7.75Z"
          fill="#181D27"
        />
      </svg>
    </div>
  );

  const capture = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8 3C8.25488 3.00028 8.50003 3.09788 8.68537 3.27285C8.8707 3.44782 8.98224 3.68695 8.99717 3.94139C9.01211 4.19584 8.92933 4.44638 8.76574 4.64183C8.60215 4.83729 8.3701 4.9629 8.117 4.993L8 5H6C5.75507 5.00003 5.51866 5.08996 5.33563 5.25272C5.15259 5.41547 5.03566 5.63975 5.007 5.883L5 6V8C4.99972 8.25488 4.90212 8.50003 4.72715 8.68537C4.55218 8.8707 4.31305 8.98224 4.05861 8.99717C3.80416 9.01211 3.55362 8.92933 3.35817 8.76574C3.16271 8.60215 3.0371 8.3701 3.007 8.117L3 8V6C2.99996 5.23479 3.29233 4.49849 3.81728 3.94174C4.34224 3.38499 5.06011 3.04989 5.824 3.005L6 3H8ZM4 15C4.24493 15 4.48134 15.09 4.66437 15.2527C4.84741 15.4155 4.96434 15.6397 4.993 15.883L5 16V18C5.00003 18.2449 5.08996 18.4813 5.25272 18.6644C5.41547 18.8474 5.63975 18.9643 5.883 18.993L6 19H8C8.25488 19.0003 8.50003 19.0979 8.68537 19.2728C8.8707 19.4478 8.98224 19.687 8.99717 19.9414C9.01211 20.1958 8.92933 20.4464 8.76574 20.6418C8.60215 20.8373 8.3701 20.9629 8.117 20.993L8 21H6C5.23479 21 4.49849 20.7077 3.94174 20.1827C3.38499 19.6578 3.04989 18.9399 3.005 18.176L3 18V16C3 15.7348 3.10536 15.4804 3.29289 15.2929C3.48043 15.1054 3.73478 15 4 15ZM18 3C18.7652 2.99996 19.5015 3.29233 20.0583 3.81728C20.615 4.34224 20.9501 5.06011 20.995 5.824L21 6V8C20.9997 8.25488 20.9021 8.50003 20.7272 8.68537C20.5522 8.8707 20.313 8.98224 20.0586 8.99717C19.8042 9.01211 19.5536 8.92933 19.3582 8.76574C19.1627 8.60215 19.0371 8.3701 19.007 8.117L19 8V6C19 5.75507 18.91 5.51866 18.7473 5.33563C18.5845 5.15259 18.3603 5.03566 18.117 5.007L18 5H16C15.7451 4.99972 15.5 4.90212 15.3146 4.72715C15.1293 4.55218 15.0178 4.31305 15.0028 4.05861C14.9879 3.80416 15.0707 3.55362 15.2343 3.35817C15.3979 3.16271 15.6299 3.0371 15.883 3.007L16 3H18ZM20 15C20.2449 15 20.4813 15.09 20.6644 15.2527C20.8474 15.4155 20.9643 15.6397 20.993 15.883L21 16V18C21 18.7652 20.7077 19.5015 20.1827 20.0583C19.6578 20.615 18.9399 20.9501 18.176 20.995L18 21H16C15.7451 20.9997 15.5 20.9021 15.3146 20.7272C15.1293 20.5522 15.0178 20.313 15.0028 20.0586C14.9879 19.8042 15.0707 19.5536 15.2343 19.3582C15.3979 19.1627 15.6299 19.0371 15.883 19.007L16 19H18C18.2449 19 18.4813 18.91 18.6644 18.7473C18.8474 18.5845 18.9643 18.3603 18.993 18.117L19 18V16C19 15.7348 19.1054 15.4804 19.2929 15.2929C19.4804 15.1054 19.7348 15 20 15ZM12 8C12.7826 8 13.548 8.22958 14.2014 8.66029C14.8549 9.09101 15.3676 9.70394 15.6761 10.4232C15.9846 11.1424 16.0753 11.9363 15.9371 12.7066C15.7988 13.4769 15.4376 14.1898 14.8983 14.7568C14.3589 15.3239 13.665 15.7202 12.9026 15.8968C12.1402 16.0734 11.3427 16.0225 10.6089 15.7503C9.87519 15.4782 9.23739 14.9967 8.77454 14.3657C8.31169 13.7346 8.04413 12.9816 8.005 12.2L8 12L8.005 11.8C8.05631 10.775 8.4996 9.80901 9.24319 9.10172C9.98677 8.39444 10.9738 8 12 8Z"
        fill="black"
      />
    </svg>
  );

  const fileIcon = () => (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={style.fileIcon}
    >
      <g clip-path="url(#clip0_249_5129)">
        <path
          d="M27.5385 3.88891V31.3748H6.10305C5.485 31.3748 4.98438 30.8734 4.98438 30.2554V9.68828L12.0817 2.76953H26.4191C27.0372 2.76953 27.5385 3.27086 27.5385 3.88891Z"
          fill="#FDFDFD"
        />
        <path
          d="M27.5457 20.6973V31.3742H16.8828V23.63C16.8828 22.01 18.1955 20.6973 19.8155 20.6973H27.5457Z"
          fill="#FDFDFD"
        />
        <path
          d="M26.4232 2.76953H25.0312C25.6494 2.76953 26.1505 3.27058 26.1505 3.8887V31.3744H27.5425V3.88877C27.5424 3.27065 27.0413 2.76953 26.4232 2.76953Z"
          fill="#FDFDFD"
        />
        <path
          d="M25.2267 1.64672V29.1326H3.79125C3.1732 29.1326 2.67188 28.6313 2.67188 28.0132V6.43148L8.58586 0.527344H24.1073C24.7254 0.527344 25.2267 1.02867 25.2267 1.64672Z"
          fill="#FDFDFD"
        />
        <path
          d="M25.2303 20.6973V29.1327H16.8828V23.63C16.8828 22.01 18.1955 20.6973 19.8155 20.6973H25.2303Z"
          fill="#FDFDFD"
        />
        <path
          d="M24.1107 0.527344H22.4375C23.0556 0.527344 23.5567 1.02839 23.5567 1.64651V29.1322H25.23V1.64651C25.2299 1.02839 24.7288 0.527344 24.1107 0.527344Z"
          fill="#FDFDFD"
        />
        <path
          d="M30.3976 35.4727H21.4872C19.8676 35.4727 18.5547 34.1598 18.5547 32.5402V23.6298C18.5547 22.0102 19.8676 20.6973 21.4872 20.6973H30.3976C32.0172 20.6973 33.3302 22.0102 33.3302 23.6298V32.5402C33.3302 34.1598 32.0172 35.4727 30.3976 35.4727Z"
          fill="#4E5BA6"
        />
        <path
          d="M30.392 20.6973H28.7188C30.3383 20.6973 31.6513 22.0102 31.6513 23.6298V32.5402C31.6513 34.1598 30.3383 35.4727 28.7188 35.4727H30.392C32.0116 35.4727 33.3245 34.1598 33.3245 32.5402V23.6299C33.3245 22.0102 32.0116 20.6973 30.392 20.6973Z"
          fill="#4E5BA6"
        />
        <path
          d="M29.635 26.9988L26.9888 23.7013C26.4516 23.0319 25.4328 23.0319 24.8957 23.7013L22.2496 26.9988C21.593 27.7737 22.2008 29.0536 23.2211 29.024H24.2473V32.3C24.2473 32.6699 24.5471 32.9698 24.917 32.9698H26.9676C27.3374 32.9698 27.6373 32.67 27.6373 32.3V29.024H28.6635C29.6836 29.0537 30.2916 27.7738 29.635 26.9988Z"
          fill="#FDFDFD"
        />
        <path
          d="M29.635 26.9988L26.9888 23.7013C26.4516 23.0319 25.4328 23.0319 24.8957 23.7013L22.2496 26.9988C21.593 27.7737 22.2008 29.0536 23.2211 29.024H24.2473V32.3C24.2473 32.6699 24.5471 32.9698 24.917 32.9698H26.9676C27.3374 32.9698 27.6373 32.67 27.6373 32.3V29.024H28.6635C29.6836 29.0537 30.2916 27.7738 29.635 26.9988Z"
          fill="#FDFDFD"
        />
        <path
          d="M29.635 26.9988L26.9888 23.7013C26.4516 23.0319 25.4328 23.0319 24.8957 23.7013L22.2496 26.9988C21.593 27.7737 22.2008 29.0536 23.2211 29.024H24.2473V32.3C24.2473 32.6699 24.5471 32.9698 24.917 32.9698H26.9676C27.3374 32.9698 27.6373 32.67 27.6373 32.3V29.024H28.6635C29.6836 29.0537 30.2916 27.7738 29.635 26.9988Z"
          fill="#FDFDFD"
        />
        <path
          d="M29.636 26.9988L26.9899 23.7013C26.4527 23.0319 25.4339 23.0319 24.8967 23.7013L24.75 23.8841C25.1492 23.8751 25.5516 24.041 25.8272 24.3845L27.9629 26.9988C28.6196 27.7738 28.0114 29.0536 26.9914 29.024H26.5625C26.2326 29.024 25.9651 29.2915 25.9651 29.6214V32.3C25.9651 32.6699 25.6653 32.9698 25.2954 32.9698H26.9686C27.3385 32.9698 27.6384 32.67 27.6384 32.3V29.024H28.6646C29.6846 29.0537 30.2927 27.7738 29.636 26.9988Z"
          fill="#FDFDFD"
        />
        <path
          d="M2.67188 6.43134L8.58572 0.527344V5.31211C8.58572 5.93023 8.0846 6.43134 7.46648 6.43134H2.67188Z"
          fill="#FDFDFD"
        />
        <path
          d="M6.91604 2.19776V5.31211C6.91604 5.93023 6.41499 6.43127 5.79688 6.43127H7.4701C8.08822 6.43127 8.58927 5.93023 8.58927 5.31211V0.527344L6.91604 2.19776Z"
          fill="#FDFDFD"
        />
        <path
          d="M17.2996 5.94562H12.3476C11.7112 5.94562 11.1953 5.42974 11.1953 4.79334V4.79095C11.1953 4.15455 11.7112 3.63867 12.3476 3.63867H17.2996C17.936 3.63867 18.4518 4.15455 18.4518 4.79095V4.79334C18.4518 5.42974 17.936 5.94562 17.2996 5.94562Z"
          fill="#FDFDFD"
        />
        <path
          d="M17.2982 3.63867H15.625C16.2614 3.63867 16.7773 4.15456 16.7773 4.79095V4.79334C16.7773 5.42974 16.2614 5.94563 15.625 5.94563H17.2982C17.9346 5.94563 18.4505 5.42974 18.4505 4.79334V4.79095C18.4505 4.15463 17.9346 3.63867 17.2982 3.63867Z"
          fill="#FDFDFD"
        />
        <path
          d="M30.3994 20.1698H25.758V3.29625H26.4266C26.7531 3.29625 27.0187 3.56182 27.0187 3.88828V18.2372C27.0187 18.5285 27.2548 18.7646 27.546 18.7646C27.8372 18.7646 28.0734 18.5285 28.0734 18.2372V3.88828C28.0734 2.98027 27.3347 2.24156 26.4266 2.24156H25.758V1.64672C25.758 0.738704 25.0193 1.11594e-06 24.1112 1.11594e-06H11.0558C10.7645 1.11594e-06 10.5284 0.23611 10.5284 0.527345C10.5284 0.818579 10.7645 1.05469 11.0558 1.05469H24.1112C24.4377 1.05469 24.7033 1.32026 24.7033 1.64672V20.1698H21.4889C19.5812 20.1698 18.029 21.7219 18.029 23.6297V28.6052H3.79516C3.4687 28.6052 3.20312 28.3397 3.20312 28.0132V6.95869H7.47053C8.37848 6.95869 9.11704 6.22006 9.11704 5.31218V0.527345C9.11704 0.314157 8.98865 0.121923 8.7917 0.0402199C8.59483 -0.0414129 8.368 0.00351674 8.21711 0.154126L2.30334 6.05813C2.24118 6.15178 2.17213 6.15677 2.14844 6.43149V28.0132C2.14844 28.9212 2.88714 29.6599 3.79516 29.6599H4.46453V30.2548C4.46453 31.1628 5.20295 31.9015 6.11055 31.9015H18.029V32.5401C18.029 34.4479 19.5811 36 21.4889 36H30.3994C32.3072 36 33.8592 34.4479 33.8592 32.5401V23.6297C33.8592 21.7219 32.3072 20.1698 30.3994 20.1698ZM8.06242 1.79895V5.31211C8.06242 5.6385 7.79692 5.90393 7.4706 5.90393H3.95048L8.06242 1.79895ZM6.11055 30.8468C5.78451 30.8468 5.51922 30.5812 5.51922 30.2548V29.6599H18.029V30.8468H6.11055ZM32.8045 32.5401C32.8045 33.8664 31.7256 34.9453 30.3994 34.9453H21.4889C20.1627 34.9453 19.0837 33.8664 19.0837 32.5401C19.0837 32.5401 19.0837 29.1303 19.0837 29.1292V23.6297C19.0837 22.3035 20.1626 21.2245 21.4889 21.2245H30.3994C31.7256 21.2245 32.8045 22.3034 32.8045 23.6297V32.5401Z"
          fill="#181D27"
        />
        <path
          d="M27.3995 23.372C26.7293 22.485 25.2196 22.434 24.4837 23.372L21.8416 26.6645C20.8967 27.7348 21.7984 29.6088 23.2273 29.5522H23.7193V32.3008C23.7193 32.9609 24.2563 33.4978 24.9164 33.4978H26.967C27.627 33.4978 28.164 32.9609 28.164 32.3008V29.5522H28.6559C30.0696 29.6319 30.9812 27.748 30.0417 26.6646L27.3995 23.372ZM29.3007 28.0886C29.2714 28.1649 29.0642 28.4917 28.6629 28.4976H27.6367C27.3455 28.4976 27.1093 28.7337 27.1093 29.0249V32.3009C27.1093 32.3794 27.0455 32.4432 26.967 32.4432H24.9164C24.8379 32.4432 24.7741 32.3794 24.7741 32.3009V29.0249C24.7741 28.7337 24.538 28.4976 24.2467 28.4976H23.2205C22.5473 28.4337 22.3443 27.8418 22.6603 27.3297L25.3064 24.0322C25.5657 23.7108 26.1983 23.5767 26.5769 24.0322L29.2231 27.3297C29.3728 27.501 29.4238 27.8285 29.3007 28.0886Z"
          fill="#181D27"
        />
        <path
          d="M17.3035 6.47297C18.2296 6.47297 18.9831 5.7195 18.9831 4.79095C18.9831 3.8648 18.2296 3.11133 17.3035 3.11133H12.3515C11.4253 3.11133 10.6719 3.8648 10.6719 4.79334C10.6719 5.7195 11.4253 6.47297 12.3515 6.47297H17.3035ZM11.7265 4.79088C11.7265 4.44628 12.0068 4.16595 12.3514 4.16595H17.3035C17.6481 4.16595 17.9284 4.44628 17.9284 4.79327C17.9284 5.13787 17.6481 5.41821 17.3035 5.41821H12.3515C12.0068 5.41828 11.7265 5.13788 11.7265 4.79088Z"
          fill="#181D27"
        />
        <path
          d="M6.13281 10.4121C6.13281 10.7033 6.36892 10.9395 6.66016 10.9395H21.2735C21.5647 10.9395 21.8008 10.7033 21.8008 10.4121C21.8008 10.1209 21.5647 9.88477 21.2735 9.88477H6.66016C6.36892 9.88477 6.13281 10.1209 6.13281 10.4121Z"
          fill="#181D27"
        />
        <path
          d="M21.2735 13.0117H6.66016C6.36892 13.0117 6.13281 13.2478 6.13281 13.5391C6.13281 13.8303 6.36892 14.0664 6.66016 14.0664H21.2735C21.5647 14.0664 21.8008 13.8303 21.8008 13.5391C21.8008 13.2478 21.5647 13.0117 21.2735 13.0117Z"
          fill="#181D27"
        />
        <path
          d="M21.2719 16.1387H18.543C18.2517 16.1387 18.0156 16.3748 18.0156 16.666C18.0156 16.9572 18.2517 17.1934 18.543 17.1934H21.2719C21.5631 17.1934 21.7992 16.9572 21.7992 16.666C21.7992 16.3748 21.5631 16.1387 21.2719 16.1387Z"
          fill="#181D27"
        />
        <path
          d="M16.5966 16.666C16.5966 16.3748 16.3605 16.1387 16.0692 16.1387H6.66016C6.36892 16.1387 6.13281 16.3748 6.13281 16.666C6.13281 16.9572 6.36892 17.1934 6.66016 17.1934H16.0692C16.3605 17.1934 16.5966 16.9572 16.5966 16.666Z"
          fill="#181D27"
        />
        <path
          d="M13.0322 19.2656H6.66016C6.36892 19.2656 6.13281 19.5017 6.13281 19.793C6.13281 20.0842 6.36892 20.3203 6.66016 20.3203H13.0322C13.3235 20.3203 13.5596 20.0842 13.5596 19.793C13.5596 19.5017 13.3235 19.2656 13.0322 19.2656Z"
          fill="#181D27"
        />
        <path
          d="M13.0322 22.3926H6.66016C6.36892 22.3926 6.13281 22.6287 6.13281 22.9199C6.13281 23.2112 6.36892 23.4473 6.66016 23.4473H13.0322C13.3235 23.4473 13.5596 23.2112 13.5596 22.9199C13.5596 22.6287 13.3235 22.3926 13.0322 22.3926Z"
          fill="#181D27"
        />
      </g>
      <defs>
        <clipPath id="clip0_249_5129">
          <rect width="36" height="36" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );

  const statusOptions = [
    { key: 'open', value: 'Open', text: 'Open' },
    { key: 'pending', value: 'Pending', text: 'Pending' },
    { key: 'closed', value: 'Closed', text: 'Closed' },
    { key: 'verified', value: 'Verified', text: 'Verified' },
    { key: 'deleted', value: 'Deleted', text: 'Deleted' },
  ];

  const obtainSubCategory = (value) => {
    const selectedCategory = categoryList.find((category) => category.value === value);

    if (!selectedCategory) {
      return;
    }

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.results && respdata.results.length > 0) {
          // Find the category object in the API response
          const categoryData = respdata.results.find((category) => category.id === selectedCategory.key);

          if (categoryData) {
            if (categoryData.subcategories && categoryData.subcategories.length > 0) {
              const list = categoryData.subcategories.map((subcategory) => ({
                key: subcategory.id,
                value: subcategory.id,
                text: subcategory.name,
              }));
              setSubCategoryList([...list]); // Update subcategories
            } else {
              setSubCategoryList([]); // Clear the subcategory list if empty
            }
          } else {
            setSubCategoryList([]); // Handle missing category
          }
        } else {
          setSubCategoryList([]); // Clear the subcategory list
        }
      },
      `${resturls.obtainSubCategory}?search=${selectedCategory.text}`,
      'GET'
    );
  };

  const FileUpload = () => {
    const onDrop = (acceptedFiles) => {
      const validFiles = acceptedFiles.filter((file) => {
        const extension = file.name.toLowerCase().split('.').pop();
        const validExtensions = ['rar', 'zip', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'gif'];
        return validExtensions.includes(extension);
      });

      if (validFiles.length === 0) return;
      formik.setFieldValue('attachments', [...formik.values.attachments, ...acceptedFiles]);
      handleFileUpload(acceptedFiles, setFileInfo, setPreviewFiles);
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
      onDrop,
      accept: {
        'application/zip': ['.zip'],
        'application/x-rar-compressed': ['.rar'],
        'application/pdf': ['.pdf'],
        'application/msword': ['.doc'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'], // DOCX files
        'application/vnd.ms-excel': ['.xls'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'], // XLSX files
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/gif': ['.gif'],
      },
    });

    return (
      <div
        {...getRootProps({
          className: `${style.fileInputContainer} ${isDragActive ? style.dragging : ''}`,
        })}
      >
        <input {...getInputProps()} accept=".rar,.zip,.pdf,.doc,.docx,.xls,.xlsx,image/*" />
        <label className={style.customFileInput}>
          {fileIcon()}
          <span>{isDragActive ? 'Drop files here...' : 'Drag and drop file here, or'}</span>
          <div className={style.uploadBtn} onClickCapture={handleDivClick}>
            <Upload />
            Upload
          </div>
        </label>
      </div>
    );
  };

  useEffect(() => {
    if (formik.values.category) {
      obtainSubCategory(formik.values.category); // Trigger on dropdown change
    }
  }, [formik.values.category]); // Depend on dropdown value instead of external data

  if (isResponsive && !commentScreen) {
    return (
      <div className={`${style.formWrapper} ${data ? style.modalForm : ''}`}>
        <Form onSubmit={(e) => handleSubmit(formik.values, fileIdList, e)} className={style.form}>
          {!data && <h4>New Ticket</h4>}
          <div className={style.formContent}>
            <Form.Field className={style.formField}>
              <label className={style.detailLabel}>Documents</label>
              <p className={style.fileInstuction}>
                You can upload one or multiple documents related to the same transaction.
              </p>
              <div className={style.fileField}>
                <div className={`${style.fileInput} ${activeCamera && style.fileUploadContainer}`}>
                  <input
                    type="file"
                    ref={fileInputRef}
                    multiple
                    name="attachments"
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx,.xlsx,.xls,.pptx,.csv,image/*"
                    style={{ display: 'none' }}
                  />

                  <p className={style.closeIconWrapper}>
                    <Icon onClickCapture={() => setActiveCamera(false)} name="close" />
                  </p>
                  <div className={activeCamera ? style.activeCameraContainer : ''}>
                    <video
                      className={`${activeCamera ? style.activeCamera : ''} ${style.videoContent}`}
                      ref={videoRef}
                      autoPlay
                    />
                    <label
                      htmlFor="file-upload"
                      className={`${activeCamera ? style.disableLabel : ''}  ${style.customFileInput}`}
                      onClickCapture={handleCaptureImage}
                    >
                      {camera()}
                      <span>Take photo</span>
                    </label>
                    {activeCamera && (
                      <p className={style.captureBtn} onClickCapture={capturePhoto}>
                        Capture {capture()}
                      </p>
                    )}
                  </div>
                  <div className={activeCamera ? style.disableLabel : ''} onClickCapture={handleDivClick}>
                    <label htmlFor="file-upload" className={`${style.customFileInput}`}>
                      {uploadIcon()}
                      <span>Upload file</span>
                    </label>
                  </div>
                </div>
                {previews.length > 0 && (
                  <>
                    <div className={style.previewContainer}>
                      {previews.map((preview, index) => {
                        const isLoading = loadingFiles?.includes(preview.name);
                        const handleOpenInNewTab = (e) => {
                          e.preventDefault();
                          if (preview.url) {
                            window.open(preview.url, '_blank');
                          } else {
                            console.error('Invalid URL for opening in new tab');
                          }
                        };
                        const handleRemove = () => {
                          const updatedPreviews = previews.filter((_, i) => i !== index);
                          setPreviews(updatedPreviews);
                          const updatedIdList = idList?.filter((_, i) => i !== index);
                          if (data?.attachements?.length > 0) {
                            const filterAttachement = data?.attachements?.filter((info) =>
                              updatedIdList?.includes(info?.id)
                            );
                            setModalAction({
                              ...data,
                              attachements: filterAttachement,
                            });
                          }
                          const updatedPreviewFiles = previewFiles.filter((_, i) => i !== index);
                          setPreviewFiles(updatedPreviewFiles);
                          editIdList(updatedIdList);
                        };
                        return <FilePreview preview={preview} handleRemove={handleRemove} />;
                      })}
                    </div>
                  </>
                )}
                {loadingFiles?.length > 0 && (
                  <Loader className={`${style.loader} ${style.commonLoader}`} active inline="centered" size="small" />
                )}
              </div>
            </Form.Field>
            <label className={style.detailLabel}>Details</label>
            <div className={style.detailsFeild}>
              {['manager', 'accountant', 'superuser'].includes(role) && (
                <Form.Field className={style.formField}>
                  <label>Organization</label>
                  <SearchableFormSelect
                    ref={dropdownRef}
                    name="organization"
                    value={formik.values.organization}
                    url={resturls.getBusinesses}
                    transformOptions={{
                      key: 'business_id',
                      value: 'business_id',
                      text: 'business_name',
                    }}
                    className="customDropdown customDropdown4withLoadMore"
                    selection
                    onChange={handleDropdownChange}
                    placeholder="Select Organization"
                    onBlur={(e) => {
                      e.preventDefault();
                      return false;
                    }}
                    onClick={(e) => {
                      if (e.target.classList.contains('load-more-option')) {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                    error={errorList?.includes('organization')}
                  />
                </Form.Field>
              )}
              {!hideFields?.includes('priority') && (
                <Form.Field className={style.formField}>
                  <label>Priority</label>
                  <div className={style.priorityList}>
                    {['Normal', 'High'].map((priority, index) => (
                      <span
                        key={index}
                        className={
                          formik.values.priority === (priority === 'High' ? 'High priority' : priority)
                            ? style.activeItem
                            : ''
                        }
                        onClick={() =>
                          formik.setFieldValue('priority', priority === 'High' ? 'High priority' : priority)
                        }
                      >
                        {priority}
                      </span>
                    ))}
                  </div>
                </Form.Field>
              )}
              {!hideFields?.includes('subject') && (
                <Form.Field className={style.formField}>
                  <label>Subject</label>
                  <Input
                    placeholder="Add a subject here"
                    name="subject"
                    onChange={handleSubjectChange}
                    value={formik.values.subject}
                    error={errorList?.includes('subject')}
                  />
                </Form.Field>
              )}
              {['manager', 'accountant', 'superuser'].includes(role) && (
                <Form.Field className={style.formField}>
                  <label>Category</label>
                  <Form.Select
                    ref={categoryRef}
                    name="category"
                    value={formik.values.category}
                    options={categoryList}
                    className="customDropdown customDropdown4withLoadMore"
                    selection
                    onChange={handleCategoryChange}
                    placeholder="Select Category"
                    onBlur={(e) => {
                      e.preventDefault();
                      return false;
                    }}
                    onClick={(e) => {
                      if (e.target.classList.contains('load-more-option')) {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                    error={errorList?.includes('category')}
                  />
                </Form.Field>
              )}
              {['manager', 'accountant', 'superuser'].includes(role) && (
                <Form.Field className={style.formField}>
                  <label>Sub-category</label>
                  <Form.Select
                    name="subCategory"
                    value={formik.values.subCategory}
                    options={subCategoryList}
                    className="customDropdown"
                    disabled={!formik.values.category}
                    selection
                    onChange={(e, { value }) => formik.setFieldValue('subCategory', value)} // Update Formik value on change
                    placeholder="Select Category"
                  />
                </Form.Field>
              )}
              {['manager', 'accountant', 'superuser'].includes(role) && (
                <Form.Field className={`${style.assignToField} ${style.formField}`}>
                  <label>Assign To</label>
                  <Form.Select
                    className="customDropdown"
                    placeholder="Select user"
                    value={formik.values.assignTo}
                    options={userListOption}
                    name="assignTo"
                    onFocus={handleOnfocus}
                    disabled={formik.values.assignToMe}
                    onChange={(e, { value }) => handleOnchange('assignTo', value)}
                    error={errorList?.includes('assignTo')}
                  />
                  <Form.Field className={style.checkboxField}>
                    <Form.Checkbox
                      label="Assign to me"
                      name="assignTo"
                      checked={formik.values.assignToMe}
                      onChange={(_, data) => {
                        formik.setFieldValue('assignToMe', !formik.values.assignToMe);
                        formik.setFieldValue('assignTo', '');
                      }}
                    />
                  </Form.Field>
                </Form.Field>
              )}
              {['manager', 'accountant', 'superuser'].includes(role) && (
                <Form.Field className={`${style.assignToField} ${style.formField}`}>
                  <label>Requested By (Optional)</label>
                  <Form.Select
                    className="customDropdown"
                    placeholder="Select user"
                    value={formik.values.requestedBy}
                    options={requestUserList}
                    onClickCapture={handleOnfocus}
                    name="requestedBy"
                    onChange={(e, { value }) => handleOnchange('requestedBy', value)}
                    error={errorList?.includes('requestedBy')}
                  />
                </Form.Field>
              )}

              {['manager', 'accountant', 'superuser'].includes(role) && data && (
                <Form.Field className={`${style.assignToField} ${style.formField}`}>
                  <label>Status</label>
                  <Form.Select
                    name="status"
                    selection
                    className="customDropdown"
                    value={formik.values.status}
                    options={statusOptions}
                    onChange={(e, { value }) => formik.setFieldValue('status', value)} // Update Formik value on change
                    placeholder="Select Status"
                  />
                </Form.Field>
              )}
              {!hideFields?.includes('description') && (
                <Form.Field className={style.formField}>
                  <label>Description (Optional)</label>
                  <TextArea
                    placeholder="Add a description for your documents (optional). Or, describe a task for your accountant, like updating bank details."
                    name="description"
                    onChange={formik.handleChange}
                    value={formik.values.description}
                  />
                </Form.Field>
              )}
            </div>
          </div>

          <div className={style.buttonWrapper}>
            <Button
              type="submit"
              disabled={loadingFiles?.length > 0 || errorList?.length > 0}
              primary
              className={style.submitBtn}
            >
              {data ? 'Update' : 'Submit'}
            </Button>
          </div>
        </Form>
      </div>
    );
  }

  if (commentScreen) {
    const hasChanges = (formik) => {
      const isChange = Object.keys(formik.values).some((key) => {
        const currentValue = formik.values[key];
        const initialValue = initialValues[key];
        if (
          typeof currentValue === 'object' &&
          currentValue !== null &&
          typeof initialValue === 'object' &&
          initialValue !== null
        ) {
          return JSON.stringify(currentValue) !== JSON.stringify(initialValue);
        }
        return currentValue !== initialValue;
      });

      return isChange;
    };

    return (
      <div className={`${style.formConatiner} ${style.commentScreenForm}`}>
        <Form onSubmit={(e) => handleSubmit(formik.values, fileIdList, e)} className={style.form}>
          <div className={style.rightForm}>
            <div className={style.detailsFeild}>
              {['manager', 'accountant', 'superuser'].includes(role) && (
                <Form.Field className={style.formField}>
                  <label>Organization</label>
                  <SearchableFormSelect
                    ref={dropdownRef}
                    name="organization"
                    value={formik.values.organization}
                    url={resturls.getBusinesses}
                    transformOptions={{
                      key: 'business_id',
                      value: 'business_id',
                      text: 'business_name',
                    }}
                    className="customDropdown customDropdown4withLoadMore"
                    selection
                    onChange={handleDropdownChange}
                    placeholder="Select Organization"
                    onBlur={(e) => {
                      e.preventDefault();
                      return false;
                    }}
                    onClick={(e) => {
                      if (e.target.classList.contains('load-more-option')) {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                    error={errorList?.includes('organisation')}
                  />
                </Form.Field>
              )}
              {!hideFields?.includes('subject') && (
                <Form.Field className={style.formField}>
                  <label>Subject</label>
                  <Input
                    placeholder="Add a subject here"
                    name="subject"
                    onChange={handleSubjectChange}
                    value={formik.values.subject}
                    error={errorList?.includes('subject')}
                  />
                </Form.Field>
              )}
              <div className={style.combainWraper}>
                <Form.Field className={`${style.formField}`}>
                  <label>Priority</label>
                  <div className={style.priorityList}>
                    {['Normal', 'High'].map((priority, index) => (
                      <span
                        key={index}
                        className={
                          formik.values.priority === (priority === 'High' ? 'High priority' : priority)
                            ? style.activeItem
                            : ''
                        }
                        onClick={() =>
                          formik.setFieldValue('priority', priority === 'High' ? 'High priority' : priority)
                        }
                      >
                        {priority}
                      </span>
                    ))}
                  </div>
                </Form.Field>
                {['manager', 'accountant', 'superuser'].includes(role) && (
                  <Form.Field className={style.formField}>
                    <label>Category</label>
                    <Form.Select
                      ref={categoryRef}
                      name="category"
                      value={formik.values.category}
                      options={categoryList}
                      className="customDropdown customDropdown4withLoadMore"
                      selection
                      onChange={handleCategoryChange}
                      placeholder="Select Category"
                      onBlur={(e) => {
                        e.preventDefault();
                        return false;
                      }}
                      onClick={(e) => {
                        if (e.target.classList.contains('load-more-option')) {
                          e.preventDefault();
                          e.stopPropagation();
                        }
                      }}
                    />
                  </Form.Field>
                )}
                {['manager', 'accountant', 'superuser'].includes(role) && (
                  <Form.Field className={style.formField}>
                    <label>Sub-category</label>
                    <Form.Select
                      name="subCategory"
                      value={formik.values.subCategory}
                      options={subCategoryList}
                      className="customDropdown"
                      disabled={!formik.values.category}
                      selection
                      onChange={(e, { value }) => formik.setFieldValue('subCategory', value)} // Update Formik value on change
                      placeholder="Select Category"
                    />
                  </Form.Field>
                )}
                {['manager', 'accountant', 'superuser'].includes(role) && (
                  <Form.Field className={`${style.assignToField} ${style.formField}`}>
                    <label>Assign To</label>
                    <Form.Select
                      className="customDropdown"
                      placeholder="Select user"
                      value={formik.values.assignTo}
                      options={userList}
                      name="assignTo"
                      onChange={(e, { value }) => formik.setFieldValue('assignTo', value)}
                      disabled={formik.values.assignToMe}
                      onFocus={handleOnfocus}
                    />
                    <Form.Field className={style.checkboxField}>
                      <Form.Checkbox
                        label="Assign to me"
                        name="assignTo"
                        checked={formik.values.assignToMe}
                        onChange={(_, data) => {
                          formik.setFieldValue('assignToMe', !formik.values.assignToMe);
                          formik.setFieldValue('assignTo', '');
                        }}
                      />
                    </Form.Field>
                  </Form.Field>
                )}

                {['manager', 'accountant', 'superuser'].includes(role) && (
                  <Form.Field className={`${style.assignToField} ${style.formField}`}>
                    <label>Requested By (Optional)</label>
                    <Form.Select
                      className="customDropdown"
                      placeholder="Select user"
                      value={formik.values.requestedBy}
                      onFocus={handleOnfocus}
                      options={requestUserList}
                      disabled
                      name="requestedBy"
                      onChange={(e, { value }) => formik.setFieldValue('requestedBy', value)}
                    />
                  </Form.Field>
                )}
              </div>
            </div>
            <div className={style.btnsWrapper}>
              <Button
                type="submit"
                disabled={loadingFiles?.length > 0 || !hasChanges(formik)}
                primary
                className={style.submitBtn}
              >
                {data ? 'Save' : 'Submit'}
              </Button>
            </div>
          </div>
        </Form>
      </div>
    );
  }
  return (
    <div className={style.formConatiner}>
      <Form onSubmit={(e) => handleSubmit(formik.values, fileIdList, e)} className={style.form}>
        <div className={style.formContent}>
          <Form.Field className={style.leftForm}>
            <label className={style.detailLabel}>Documents</label>
            <p className={style.fileInstuction}>
              You can upload one or multiple documents related to the same transaction.
            </p>
            <div className={style.fileField}>
              <FileUpload />
              {previews.length > 0 && (
                <>
                  <div className={style.previewContainer}>
                    {previews.map((preview, index) => {
                      const isLoading = loadingFiles?.includes(preview.name);
                      const handleOpenInNewTab = (e) => {
                        e.preventDefault();
                        if (preview.url) {
                          window.open(preview.url, '_blank');
                        } else {
                          console.error('Invalid URL for opening in new tab');
                        }
                      };
                      const handleRemove = () => {
                        const updatedPreviews = previews.filter((_, i) => i !== index);
                        setPreviews(updatedPreviews);
                        const updatedIdList = idList?.filter((_, i) => i !== index);
                        if (data?.attachements?.length > 0) {
                          const filterAttachement = data?.attachements?.filter((info) =>
                            updatedIdList?.includes(info?.id)
                          );
                          setModalAction({
                            ...data,
                            attachements: filterAttachement,
                          });
                        }
                        const updatedPreviewFiles = previewFiles.filter((_, i) => i !== index);
                        setPreviewFiles(updatedPreviewFiles);
                        editIdList(updatedIdList);
                      };

                      return <FilePreview preview={preview} handleRemove={handleRemove} />;
                    })}
                  </div>
                </>
              )}
              {loadingFiles?.length > 0 && (
                <Loader className={`${style.loader} ${style.commonLoader}`} active inline="centered" size="small" />
              )}
            </div>
          </Form.Field>
          <div className={style.rightForm}>
            <label className={style.detailLabel}>Details</label>
            <p className={style.fileInstuction}>Description</p>
            <div className={style.detailsFeild}>
              {['manager', 'accountant', 'superuser'].includes(role) && (
                <Form.Field className={style.formField}>
                  <label>Organization</label>
                  <SearchableFormSelect
                    ref={dropdownRef}
                    name="organization"
                    value={formik.values.organization}
                    url={resturls.getBusinesses}
                    transformOptions={{
                      key: 'business_id',
                      value: 'business_id',
                      text: 'business_name',
                    }}
                    className="customDropdown customDropdown4withLoadMore"
                    selection
                    onChange={handleDropdownChange}
                    placeholder="Select Organization"
                    onBlur={(e) => {
                      e.preventDefault();
                      return false;
                    }}
                    onClick={(e) => {
                      if (e.target.classList.contains('load-more-option')) {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                    error={errorList?.includes('organization')}
                  />
                </Form.Field>
              )}
              {!hideFields?.includes('subject') && (
                <Form.Field className={style.formField}>
                  <label>Subject</label>
                  <Input
                    placeholder="Add a subject here"
                    name="subject"
                    onChange={handleSubjectChange}
                    value={formik.values.subject}
                    error={errorList?.includes('subject')}
                  />
                </Form.Field>
              )}
              <div className={style.combainWraper}>
                <Form.Field className={`${style.formField}`}>
                  <label>Priority</label>
                  <div className={style.priorityList}>
                    {['Normal', 'High'].map((priority, index) => (
                      <span
                        key={index}
                        className={
                          formik.values.priority === (priority === 'High' ? 'High priority' : priority)
                            ? style.activeItem
                            : ''
                        }
                        onClick={() =>
                          formik.setFieldValue('priority', priority === 'High' ? 'High priority' : priority)
                        }
                      >
                        {priority}
                      </span>
                    ))}
                  </div>
                </Form.Field>
                {['manager', 'accountant', 'superuser'].includes(role) && (
                  <Form.Field className={style.formField}>
                    <label>Category</label>
                    <Form.Select
                      ref={categoryRef}
                      name="category"
                      value={formik.values.category}
                      options={categoryList}
                      className="customDropdown customDropdown4withLoadMore"
                      selection
                      onChange={handleCategoryChange}
                      placeholder="Select Category"
                      onBlur={(e) => {
                        e.preventDefault();
                        return false;
                      }}
                      onClick={(e) => {
                        if (e.target.classList.contains('load-more-option')) {
                          e.preventDefault();
                          e.stopPropagation();
                        }
                      }}
                      error={errorList?.includes('category')}
                    />
                  </Form.Field>
                )}
                {['manager', 'accountant', 'superuser'].includes(role) && (
                  <Form.Field className={style.formField}>
                    <label>Sub-category</label>
                    <Form.Select
                      name="subCategory"
                      value={formik.values.subCategory}
                      options={subCategoryList}
                      className="customDropdown"
                      selection
                      disabled={!formik.values.category}
                      onChange={(e, { value }) => formik.setFieldValue('subCategory', value)} // Update Formik value on change
                      placeholder="Select Category"
                    />
                  </Form.Field>
                )}
                {['manager', 'accountant', 'superuser'].includes(role) && (
                  <Form.Field className={`${style.assignToField} ${style.formField}`}>
                    <label>Assign To</label>
                    <Form.Select
                      className="customDropdown"
                      placeholder="Select user"
                      value={formik.values.assignTo}
                      options={userListOption}
                      name="assignTo"
                      onChange={(e, { value }) => handleOnchange('assignTo', value)}
                      disabled={formik.values.assignToMe}
                      onFocus={handleOnfocus}
                      error={errorList?.includes('assignTo')}
                    />
                    <Form.Field className={style.checkboxField}>
                      <Form.Checkbox
                        label="Assign to me"
                        name="assignTo"
                        checked={formik.values.assignToMe}
                        onChange={(_, data) => handleOnchange('assignToMe')}
                      />
                    </Form.Field>
                  </Form.Field>
                )}

                {['manager', 'accountant', 'superuser'].includes(role) && (
                  <Form.Field className={`${style.assignToField} ${style.formField}`}>
                    <label>Requested By (Optional)</label>
                    <Form.Select
                      className="customDropdown"
                      placeholder="Select user"
                      value={formik.values.requestedBy}
                      options={requestUserList}
                      onFocus={handleOnfocus}
                      name="requestedBy"
                      error={errorList?.includes('requestedBy')}
                      onChange={(e, { value }) => handleOnchange('requestedBy', value)}
                    />
                  </Form.Field>
                )}

                {['manager', 'accountant', 'superuser'].includes(role) && data && (
                  <Form.Field className={`${style.assignToField} ${style.formField}`}>
                    <label>Status</label>
                    <Form.Select
                      name="status"
                      className="customDropdown"
                      value={formik.values.status}
                      options={statusOptions}
                      onChange={(e, { value }) => formik.setFieldValue('status', value)} // Update Formik value on change
                      placeholder="Select Status"
                    />
                  </Form.Field>
                )}
              </div>
              {!hideFields?.includes('description') && (
                <Form.Field className={style.formField}>
                  <label>Description (Optional)</label>
                  <TextArea
                    placeholder="Add a description for your documents (optional). Or, describe a task for your accountant, like updating bank details."
                    name="description"
                    onChange={formik.handleChange}
                    value={formik.values.description}
                  />
                </Form.Field>
              )}
            </div>
          </div>
        </div>
        <div className={style.buttonWrapper}>
          <Button
            type="submit"
            disabled={loadingFiles?.length > 0 || errorList?.length > 0}
            primary
            className={style.submitBtn}
          >
            {data ? 'Update' : 'Submit'}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default TicketForm;
