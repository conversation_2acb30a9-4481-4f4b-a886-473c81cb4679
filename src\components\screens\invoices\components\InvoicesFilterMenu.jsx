import React, { useCallback, useState } from 'react';
import style from './invoicesFilterMenu.module.scss';
import { Button, Input } from 'semantic-ui-react';
import { CloseIcon } from '../../../../assets/svgs';
import CheckBoxListing from '../../../generic-components/CheckBoxListing';
import { Clock } from 'lucide-react';
import FilterBtn from '../../../ui-components/FilterBtn';
import { formatDateToDDMMYYYY } from '../../../utils/dateUtils';

const invoiceStatusOptions = [
  {
    key: 'new',
    value: '0',
    label: 'New',
    className: 'openStatus',
  },
  {
    key: 'extracted',
    value: [1, 2],
    label: 'Extracted',
    className: 'pendingStatus',
  },
  {
    key: 'validated',
    value: '3',
    label: 'Validated',
    className: 'verifiedStatus',
  },
  {
    key: 'exported',
    value: '4',
    label: 'Exported',
    className: 'closedStatus',
  },
  {
    key: 'completed',
    value: '5',
    label: 'Completed',
    className: '-',
  },
  {
    key: 'duplicate',
    value: '6',
    label: 'Duplicate',
    className: 'duplicateStatus',
  },
  {
    key: 'inProcess',
    value: '-1',
    label: 'In Process',
    className: 'inProcessStatus',
  },
  {
    key: 'failed',
    value: '-2',
    label: 'Failed',
    className: 'deletedStatus',
  },
];

const invoiceTimePeriodOptions = [
  { key: 'last7Days', value: 'last7Days', label: 'Last 7 days' },
  { key: 'last30Days', value: 'last30Days', label: 'Last 30 days' },
  { key: 'currentQuarter', value: 'currentQuarter', label: 'Current Quarter' },
  { key: 'currentYear', value: 'currentYear', label: 'Current Year' },
  { key: 'customRange', value: 'customRange', label: 'Custom Range' },
];

const today = new Date();
const todayDate = formatDateToDDMMYYYY(today);

const getDateRange = (option, tempStartDate = '', tempEndDate = '') => {
  const result = {
    startDate: '',
    endDate: todayDate,
  };

  switch (option) {
    case 'last7Days': {
      const last7Days = new Date(today);
      last7Days.setDate(today.getDate() - 7);
      result.startDate = formatDateToDDMMYYYY(last7Days);
      break;
    }
    case 'last30Days': {
      const last30Days = new Date(today);
      last30Days.setDate(today.getDate() - 30);
      result.startDate = formatDateToDDMMYYYY(last30Days);
      break;
    }
    case 'currentQuarter': {
      const quarter = Math.floor(today.getMonth() / 3);
      const startQuarter = new Date(today.getFullYear(), quarter * 3, 1);
      result.startDate = formatDateToDDMMYYYY(startQuarter);
      break;
    }
    case 'currentYear': {
      const startYear = new Date(today.getFullYear(), 0, 1);
      result.startDate = formatDateToDDMMYYYY(startYear);
      break;
    }
    case 'customRange': {
      result.startDate = tempStartDate;
      result.endDate = tempEndDate || todayDate;
      break;
    }
    default:
      break;
  }

  return result;
};

const DateRangeInputs = ({ startDate, endDate, handleStartDateChange, handleEndDateChange }) => {
  const compatibleTodayDate = todayDate.split('/').reverse().join('-');
  const compatibleStartDate = startDate ? startDate.split('/').reverse().join('-') : '';
  const compatibleEndDate = endDate ? endDate.split('/').reverse().join('-') : '';

  return (
    <div className="flex flex-col p-3 bg-white rounded-md border border-gray-200">
      <div className="text-base font-bold pb-1 border-b-2 border-gray-200 text-gray-700 mb-4">Select Date Range</div>
      <div className="flex flex-col items-start justify-between gap-3">
        <div className="flex flex-col gap-1 w-full">
          <span className="text-sm text-gray-600">Start Date</span>
          <Input
            type="date"
            value={compatibleStartDate}
            onChange={(e) => {
              const dateValue = e.target.value;
              if (dateValue) {
                const formattedDate = formatDateToDDMMYYYY(dateValue);
                handleStartDateChange({ target: { value: formattedDate } });
              } else {
                handleStartDateChange({ target: { value: '' } });
              }
            }}
            max={compatibleTodayDate}
            className="w-full"
          />
        </div>

        <div className="flex flex-col gap-1 w-full">
          <span className="text-sm text-gray-600">End Date</span>
          <Input
            type="date"
            value={compatibleEndDate}
            onChange={(e) => {
              const dateValue = e.target.value;
              if (dateValue) {
                const formattedDate = formatDateToDDMMYYYY(dateValue);
                handleEndDateChange({ target: { value: formattedDate } });
              } else {
                handleEndDateChange({ target: { value: '' } });
              }
            }}
            min={compatibleStartDate}
            max={compatibleTodayDate}
            disabled={!startDate}
            placeholder="Today (if not selected)"
            className="w-full"
          />
          {startDate && !endDate && <div className="text-xs text-gray-500 mt-1 italic">Today (if left empty)</div>}
        </div>
      </div>
    </div>
  );
};

function InvoicesFilterMenu({ onApply }) {
  const [isShowFiltersMenu, setIsShowFiltersMenu] = useState(false);
  const [isFilterApplied, setIsFilterApplied] = useState(0);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [updatedStartDate, setUpdatedStartDate] = useState('');
  const [updatedEndDate, setUpdatedEndDate] = useState('');
  const [customRangeSelected, setCustomRangeSelected] = useState(false);
  const [updatedCustomRangeSelected, setUpdatedCustomRangeSelected] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState({});

  const handleOptionChange = (key, value, isChecked) => {
    const filter = { ...selectedOptions };
    if (isChecked) {
      if (filter[key]) {
        filter[key] = [...filter[key], value];
      } else {
        filter[key] = [value];
      }
    } else {
      if (filter[key]) {
        filter[key] = filter[key].filter((filterValue) => filterValue !== value);
        if (filter[key].length === 0) delete filter[key];
      }
    }
    setSelectedOptions(filter);
  };

  const handleTimePeriodChange = (key, value, isChecked) => {
    const filter = { ...selectedOptions };

    if (isChecked) {
      filter[key] = [value];
      if (value === 'customRange') {
        setCustomRangeSelected(true);
      } else {
        setCustomRangeSelected(false);
        setStartDate('');
        setEndDate('');
        const dateRange = getDateRange(value);
        filter.invoice_start_date = dateRange.startDate;
        filter.invoice_end_date = dateRange.endDate;
      }
    } else {
      if (filter[key]) {
        delete filter[key];
        setCustomRangeSelected(false);
        delete filter.invoice_start_date;
        delete filter.invoice_end_date;
      }
    }

    setSelectedOptions(filter);
  };

  const handleUpdatedTimePeriodChange = (key, value, isChecked) => {
    const filter = { ...selectedOptions };

    if (isChecked) {
      filter[key] = [value];
      if (value === 'customRange') {
        setUpdatedCustomRangeSelected(true);
      } else {
        setUpdatedCustomRangeSelected(false);
        setUpdatedStartDate('');
        setUpdatedEndDate('');
        const dateRange = getDateRange(value);
        filter.updated_start_date = dateRange.startDate;
        filter.updated_end_date = dateRange.endDate;
      }
    } else {
      if (filter[key]) {
        delete filter[key];
        setUpdatedCustomRangeSelected(false);
        delete filter.updated_start_date;
        delete filter.updated_end_date;
      }
    }

    setSelectedOptions(filter);
  };

  const handleApply = useCallback(() => {
    let finalParams = { ...selectedOptions };
    if ('invoiceTimePeriod' in finalParams) {
      delete finalParams.invoiceTimePeriod;
    }
    if ('updatedTimePeriod' in finalParams) {
      delete finalParams.updatedTimePeriod;
    }
    onApply?.(finalParams);
    setIsFilterApplied(true);
    setIsShowFiltersMenu(false);
  }, [selectedOptions]);

  const handleInvoiceStartDateChange = useCallback(
    (e) => {
      const newStartDate = e.target.value;
      setStartDate(newStartDate);

      if (
        endDate &&
        new Date(endDate.split('/').reverse().join('-')) < new Date(newStartDate.split('/').reverse().join('-'))
      ) {
        setEndDate('');
      }

      if (newStartDate) {
        setSelectedOptions((prev) => ({
          ...prev,
          invoice_start_date: newStartDate,
          invoice_end_date: endDate || todayDate,
        }));
      } else {
        setSelectedOptions((prev) => {
          const updated = { ...prev };
          delete updated.invoice_start_date;
          delete updated.invoice_end_date;
          return updated;
        });
      }
    },
    [endDate]
  );

  const handleInvoiceEndDateChange = useCallback(
    (e) => {
      const newEndDate = e.target.value;
      setEndDate(newEndDate);

      if (startDate) {
        setSelectedOptions((prev) => ({
          ...prev,
          invoice_start_date: startDate,
          invoice_end_date: newEndDate || todayDate,
        }));
      }
    },
    [startDate]
  );

  const handleUpdatedStartDateChange = useCallback(
    (e) => {
      const newStartDate = e.target.value;
      setUpdatedStartDate(newStartDate);

      if (
        updatedEndDate &&
        new Date(updatedEndDate.split('/').reverse().join('-')) < new Date(newStartDate.split('/').reverse().join('-'))
      ) {
        setUpdatedEndDate('');
      }

      if (newStartDate) {
        setSelectedOptions((prev) => ({
          ...prev,
          updated_start_date: newStartDate,
          updated_end_date: updatedEndDate || todayDate,
        }));
      } else {
        setSelectedOptions((prev) => {
          const updated = { ...prev };
          delete updated.updated_start_date;
          delete updated.updated_end_date;
          return updated;
        });
      }
    },
    [updatedEndDate]
  );

  const handleUpdatedEndDateChange = useCallback(
    (e) => {
      const newEndDate = e.target.value;
      setUpdatedEndDate(newEndDate);

      if (updatedStartDate) {
        setSelectedOptions((prev) => ({
          ...prev,
          updated_start_date: updatedStartDate,
          updated_end_date: newEndDate || todayDate,
        }));
      }
    },
    [updatedStartDate]
  );

  const handleClearAll = useCallback(() => {
    setIsShowFiltersMenu(false);
    setStartDate('');
    setEndDate('');
    setUpdatedStartDate('');
    setUpdatedEndDate('');
    setCustomRangeSelected(false);
    setUpdatedCustomRangeSelected(false);
    setIsFilterApplied(0);
    setSelectedOptions({});
    onApply?.({});
  }, []);

  return (
    <>
      <div className="flex gap-2">
        <FilterBtn isActive={isFilterApplied} onClick={() => setIsShowFiltersMenu(!isShowFiltersMenu)} />
      </div>

      {isShowFiltersMenu && (
        <div className={style.container} onClick={() => setIsShowFiltersMenu(false)}>
          <div className={style.filtersContainer} onClick={(e) => e.stopPropagation()}>
            <div className={style.mainWrapper}>
              <div className={style.headerWrapper}>
                <h5>All Filters</h5>
                <div className={style.closeIconWrapper} onClick={() => setIsShowFiltersMenu(false)}>
                  <CloseIcon />
                </div>
              </div>
              <div className={style.filterListContainer}>
                <CheckBoxListing
                  options={invoiceTimePeriodOptions}
                  label="Filter by Invoice Date"
                  paramsKey="invoiceTimePeriod"
                  defaultLogo={Clock}
                  onSelectionChange={handleTimePeriodChange}
                  selectedValues={selectedOptions?.invoiceTimePeriod}
                  injectComponent={
                    customRangeSelected && (
                      <DateRangeInputs
                        startDate={startDate}
                        endDate={endDate}
                        handleStartDateChange={handleInvoiceStartDateChange}
                        handleEndDateChange={handleInvoiceEndDateChange}
                      />
                    )
                  }
                />

                <CheckBoxListing
                  options={invoiceTimePeriodOptions}
                  label="Filter by Last Updated"
                  paramsKey="updatedTimePeriod"
                  defaultLogo={Clock}
                  onSelectionChange={handleUpdatedTimePeriodChange}
                  selectedValues={selectedOptions?.updatedTimePeriod}
                  injectComponent={
                    updatedCustomRangeSelected && (
                      <DateRangeInputs
                        startDate={updatedStartDate}
                        endDate={updatedEndDate}
                        handleStartDateChange={handleUpdatedStartDateChange}
                        handleEndDateChange={handleUpdatedEndDateChange}
                      />
                    )
                  }
                />

                <CheckBoxListing
                  options={invoiceStatusOptions}
                  label="Status"
                  paramsKey="status"
                  onSelectionChange={handleOptionChange}
                  selectedValues={selectedOptions?.status || []}
                />
              </div>
            </div>
            <div className={style.btnWrapper}>
              <Button className={style.cancelBtn} onClick={handleClearAll}>
                Clear all
              </Button>
              <Button
                className={style.applyBtn}
                disabled={
                  Object.keys(selectedOptions).length === 0 ||
                  (customRangeSelected && !startDate) ||
                  (updatedCustomRangeSelected && !updatedStartDate)
                }
                onClick={handleApply}
              >
                Apply
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default InvoicesFilterMenu;
