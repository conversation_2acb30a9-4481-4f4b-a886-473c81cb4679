import React from 'react';
import { CalenderIcon, DropdownIcon, EmptyBox } from '../../../../../assets/svgs';
import { Popup } from 'semantic-ui-react';
import ChartSection from './ChartSection';
import DetailsSection from './DetailedSection';
import RenderDownloadModal from '../../../../utils/reportUtils/RenderDownloadModal';
import ReportHeader from '../../../../global/components/ReportHeader';
import style from '../scss/costReport.module.scss';
import DropdownContent from './DropdownContent';
import { useAuth } from '../../../../../contexts/AuthContext';
import { reverseDate } from '../../../../utils/dateUtils';

const RenderOverallContent = ({
  costDetails,
  refreshData,
  setDownloadModal,
  btnLoader,
  setOpenDropdown,
  selectedTimeline,
  handleSelectDropdown,
  activeInput,
  handleChange,
  setActiveInput,
  setChanges,
  changes,
  popupRef,
  openDropdown,
  isLoading,
  dateData,
  setDateData,
  details,
  activeDetailsPage,
  handleDetailsPagination,
  isDetailedReportLoading,
  downloadModal,
  downloadCostReport,
  trendlineData,
}) => {
  const { isMobileScreen } = useAuth();

  return (
    <>
      <ReportHeader
        title="Expense Report"
        data={costDetails}
        handleRefresh={refreshData}
        setDownloadModal={setDownloadModal}
        loading={btnLoader}
      />
      <div className={style.dropdownWrapper}>
        <Popup
          className={style.popup}
          trigger={
            <div className={style.dropDown} onClick={() => setOpenDropdown(true)}>
              <p>
                <CalenderIcon /> {selectedTimeline?.text}
              </p>
              <DropdownIcon />
            </div>
          }
          content={
            <>
              <DropdownContent
                selectedTimeline={selectedTimeline}
                handleSelectDropdown={handleSelectDropdown}
                activeInput={activeInput}
                dateData={dateData}
                setDateData={setDateData}
                handleChange={handleChange}
                setActiveInput={setActiveInput}
                setChanges={setChanges}
                changes={changes}
                setOpenDropdown={setOpenDropdown}
                popupRef={popupRef}
              />
            </>
          }
          position="bottom left"
          on="click"
          hoverable
          basic
          open={!isMobileScreen && openDropdown}
        />
      </div>
      {costDetails?.data?.length > 0 ? (
        <div className={style.contentWrapper}>
          <ChartSection
            isLoading={isLoading}
            costDetails={costDetails}
            dateData={dateData}
            selectedTimeline={selectedTimeline}
            trendlineData={trendlineData}
          />
          <DetailsSection
            costDetails={costDetails}
            details={details}
            activeDetailsPage={activeDetailsPage}
            handleDetailsPagination={handleDetailsPagination}
            isDetailedReportLoading={isDetailedReportLoading}
          />
        </div>
      ) : (
        <div className={style.emptyMsg}>
          <EmptyBox />
          <p>No data available for Cost reports at this time</p>
        </div>
      )}
      {isMobileScreen && (
        <DropdownContent
          selectedTimeline={selectedTimeline}
          handleSelectDropdown={handleSelectDropdown}
          activeInput={activeInput}
          dateData={dateData}
          setDateData={setDateData}
          handleChange={handleChange}
          setActiveInput={setActiveInput}
          setChanges={setChanges}
          changes={changes}
          setOpenDropdown={setOpenDropdown}
          popupRef={popupRef}
          openDropdown={openDropdown}
        />
      )}
      <RenderDownloadModal
        isOpen={downloadModal}
        onClose={() => setDownloadModal(false)}
        content={`Your Cost Report for the time period ${reverseDate(dateData?.startDate)} to ${reverseDate(dateData?.endDate)} is ready to download.`}
        downloadFunction={downloadCostReport}
      />
    </>
  );
};

export default RenderOverallContent;
