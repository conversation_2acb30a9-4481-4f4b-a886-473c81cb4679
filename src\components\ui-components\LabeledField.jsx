import React from 'react';
import { FormHelperText } from '@mui/material';

export default function LabeledField({ id, label, isRequired, isDisabled, error = false, helperText = '', children }) {
  const labelId = id || `field-${Math.random().toString(36).slice(2, 9)}`;

  return (
    <div className="flex flex-col gap-1">
      {label && (
        <label htmlFor={labelId} className={`text-sm font-medium ${isDisabled ? 'text-gray-400' : 'text-gray-700'}`}>
          {label}
          {isRequired && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {React.cloneElement(children, {
        id: labelId,
      })}
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </div>
  );
}
