@import '../../../assets//scss/main.scss';

.bussinessListScreen {
  height: 89vh;
  padding: 2.5em 2em;
  display: flex;
  gap: 2em;
  @include for_media(mobileScreen) {
    padding: 1em;
  }
  .navigationWrapper {
    width: 20%;
    @include for_media(mobileScreen) {
      display: none;
    }
  }
  .rightContentWrapper {
    width: 80%;
    background-color: $white;
    border-radius: 10px;
    box-shadow: 0 0 1px 1px $borderColor;
    @include for_media(mobileScreen) {
      width: 100%;
      padding: 0;
    }
  }

  .tableWrapper::-webkit-scrollbar {
    display: none; /* For Chrome, Safari, and Opera */
  }

  .tableWrapper {
    padding: 1em 0;
    max-height: 59vh;
    overflow-y: auto;
    -ms-overflow-style: none; /* For IE and Edge */
    scrollbar-width: none; /* For Firefox */
    height: 59vh;
    table {
      // border-spacing: 0 15px !important;
      min-width: 135%;
      thead {
        background-color: #f5f5f5 !important;
        height: 5em !important;
      }
      th {
        color: #717680 !important;
        // text-align: center !important;
        padding: 1em !important;
        font-size: 1rem !important;
      }
      td {
        padding: 1em !important;
        // text-align: center !important;
        color: #717680;
        p {
          font-size: 1.1em !important;
        }
      }
      .subjectheaderRow {
        text-align: start !important;
        padding: 1em !important;
      }
      .subjectRow {
        text-align: start !important;
        display: flex;
        align-items: center;
        gap: 1em;
        padding: 1em !important;
        font-weight: 900;
        font-size: 1rem !important;
        .logo {
          border: 2px solid $borderColor;
          border-radius: 10px;
          width: 45px;
          height: 45px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f5f5;
          svg {
            width: 25px;
            height: 25px;
          }
          img {
            width: 100%;
            height: 100%;
            border-radius: 10px;
          }
          svg,
          path {
            fill: #0a0d12;
          }
        }
      }
      tbody {
        tr {
          background-color: $white;
          border-radius: 20px !important;
          cursor: pointer;
        }
      }
    }
  }
}

.ticketList {
  display: flex;
  flex-wrap: wrap;
  gap: 2em;
  // justify-content: center;
  padding-bottom: 5em;
  @include for_media(mobileScreen) {
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .ticketCard {
    margin: 0;
    background-color: #fdfdfd;
    width: 30em;
    border-radius: 15px;
    // height: 18em;
    @include for_media(mobileScreen) {
      // height: 19em;
    }
    overflow: hidden;
    .leftContent {
      width: 20%;
      svg,
      img {
        width: 45px;
        height: 45px;
      }
    }
    .rightContent {
      width: 90%;
      height: 12em;
      @include for_media(mobileScreen) {
        height: auto;
      }
    }
    p {
      white-space: normal;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      @include for_media(mobileScreen) {
        width: 97%;
      }
    }
    .ticketContent {
      display: flex;
    }
  }
}

.desc {
  color: #717680;
}

.headerPart {
  padding: 2em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .btnWrapper {
    display: flex;
    gap: 1em;
    .downloadBtn {
      display: flex;
      gap: 1em;
      align-items: center;
      width: 10em;
      height: 2.5em;
      border-radius: 35px;
      border: 1px solid #d5d7da;
      justify-content: center;
      color: #293056;
      font-size: 1.4em;
      svg {
        width: 25px;
        height: 25px;
      }
    }
    .addBtn {
      display: flex;
      gap: 1em;
      align-items: center;
      width: 10em;
      height: 2.5em;
      border-radius: 35px;
      border: 1px solid #d5d7da;
      justify-content: center;
      color: $white;
      background-color: #4e5ba6;
      font-size: 1.4em;
      svg {
        width: 25px;
        height: 25px;
      }
    }
  }
}

.searchWrapper {
  padding: 1em 2em;
  display: flex;
  gap: 2em;
}

.searchInput {
  width: 35% !important;
  height: 3.5em !important;
  // border: 1px solid #E9EAEB;
  border-radius: 10px;
  box-shadow: 0 0 1px 1px #e9eaeb;
  input {
    background-color: #fdfdfd !important;
    color: #717680 !important;
    // border: 1px solid #E9EAEB;
    border-radius: 10px !important;
  }
}
.statusDropdown {
  height: 3.1em !important;
  border-radius: 10px;
  min-width: 150px;
  box-shadow: 0 0 1px 1px #e9eaeb;
  background-color: #fafafa !important;
  font-size: 1.1rem !important;
  svg {
    path {
      fill: #0a0d12;
    }
  }
  display: flex !important;
  align-items: center;
  justify-content: space-evenly;
}

.checkbox {
  label::before {
    height: 25px !important;
    width: 25px !important;
  }
  label::after {
    top: 4px !important;
    width: 25px !important;
    height: 19px !important;
    color: $white !important;
  }
}

.contactInfo {
  display: flex;
  align-items: center;
  gap: 2em;
  justify-content: space-between;
  p {
    margin: 0;
  }
  .iconWrapper {
    display: flex;
    gap: 1em;
    span {
      width: 40px;
      height: 40px;
      background-color: #eaecf5;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      svg {
        width: 22px !important;
        height: 22px !important;
        path {
          fill: #4e5ba6;
        }
      }
      cursor: pointer;
    }
  }
}

.businessName {
  p {
    font-size: 1.2rem;
    font-weight: 400 !important;
    color: #0a0d12;
  }
}

.popup {
  border-radius: 10px !important;
  width: 23em;
  max-width: 25em !important;
  .popupContent {
    text-align: center;
    p {
      margin: 0;
    }
    .label {
      color: #a4a7ae;
    }
  }
}

.businessName {
  span {
    padding: 0.25em 1em;
    border-radius: 20px;
  }
  .activeStatus {
    background-color: #e3f8d9 !important;
    color: #2e7a31;
  }
  .inActive {
    background-color: #f5f5f5 !important;
    color: #717680;
  }
}

.modalContent {
  min-height: 62vh;
  background-color: $white;
  border-radius: 25px !important;

  h5 {
    font-size: 1.3em !important;
    padding: 1em 2em;
    color: black;
  }
  .progressWrapper {
    padding: 2em;
    .progress {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.8em;
    }
    .line {
      width: 65%;
      height: 2px;
      background-color: #d5d7da;
      position: relative;
      top: 5px;
      z-index: 0;
    }
    .dot {
      position: relative;
    }
    .dot::before {
      width: 10px;
      height: 10px;
      background-color: #d5d7da;
      border-radius: 100%;
      content: '';
      position: absolute;
    }
    .enableDot::before {
      background-color: #4e5ba6;
      // position: relative;
      z-index: 20;
    }
    .enableDot::after {
      content: '';
      position: absolute;
      height: 30px;
      width: 30px;
      border-radius: 100%;
      border: 2px solid #eaecf5;
      left: -10px;
      top: -10px;
    }
    .activeDot::after {
      background-color: #eaecf5;
      z-index: 10;
    }
  }
  .labelWrapper {
    display: flex;
    justify-content: center;
    position: relative;
    padding: 1em;
    top: 1em;
    .firstLable {
      padding-left: 2em;
    }
    .label {
      display: flex;
      justify-content: space-between;
      width: 88%;
      span {
        color: black;
      }
    }
  }
  .formContainer {
    margin: 0 0 2em 0;
    margin: 0 2em 2em 2em;
    height: 450px;
    overflow-y: auto;
    padding-right: 1em;
    .formField {
      margin: 2em 0;
    }
    input,
    textarea {
      background-color: #f5f5f5 !important;
      color: $black !important;
      box-shadow: 0 0 1px 1px #e9eaeb;
      border-radius: 13px !important;
    }
    input {
      height: 3.5em;
    }
    textarea {
      height: 7em;
    }
    label {
      font-size: 0.9em !important;
      margin: 0.5em 0 !important;
      font-weight: 400 !important;
    }
  }
}

.formWrapper {
  min-height: 50vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.buttonWrapper {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 2em;
  border-top: 1px solid #717680;
  .subBtnWrapper {
    display: flex;
    gap: 1.2em;
  }
  .cancel {
    border: none !important;
    background-color: transparent !important;
    color: $black;
    font-size: 1.2em !important;
  }
  .nextBtn {
    background-color: #4e5ba6 !important;
    color: $white !important;
    padding: 1em 2em !important;
    border-radius: 35px !important;
    font-size: 1.2em !important;
  }
  .backBtn {
    background-color: #eaecf5 !important;
    border: 1px solid #9ea5d1;
    border-radius: 35px !important;
    font-size: 1.2em !important;
    padding: 1em 2em !important;
  }
}
.span_orgCOunt {
  font-size: 0.8em !important;
  height: 25px;
  width: 35px;
  background-color: #f9f5ff;
  color: #6941c6;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  margin-left: 10px;
}

.separate {
  display: flex !important;
  align-items: center !important;
  align-content: center !important;
}

.clientInfo {
  p {
    margin-bottom: 0.2em;
    color: black;
  }
  span {
    color: #717680;
  }
}

.businessInfo {
  text-align: start !important;
  display: flex;
  align-items: center;
  gap: 1em;
  padding: 1em !important;
  font-weight: 900;
  font-size: 1rem !important;
  .logo {
    border: 2px solid $borderColor;
    border-radius: 10px;
    width: 45px;
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    svg {
      width: 25px;
      height: 25px;
    }
    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
    svg,
    path {
      fill: #0a0d12;
    }
  }
}

.ticketSubject {
  text-align: start !important;
  display: flex;
  align-items: center;
  gap: 1em;
  padding: 1em !important;
  font-weight: 900;
  font-size: 1rem !important;
  svg {
    width: 50px;
    height: 50px;
  }
  .businessName {
    span {
      margin: 0;
      padding: 0;
    }
    p {
      margin-bottom: 0.2em;
    }
  }
}

.leftImage {
  img {
    width: 50px;
    width: 50px;
  }
}

.openStatus {
  background-color: #eaecf5 !important;
  color: #4e5ba6;
}

.pendingStatus {
  background-color: #fdf7dd !important;
  color: #7e6607;
}

.closedStatus {
  background-color: #f2d7d5 !important;
  color: #c0392b;
}

.verifiedStatus {
  background-color: #e3f8d9 !important;
  color: #2e7a31;
}

.deletedStatus {
  background-color: #fee4e2 !important;
  color: #b42318;
}

.status {
  border-radius: 35px;
  background-color: #d5d5d569;
  text-align: center;
  height: 2.3em;
  font-size: 1em !important;
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  white-space: nowrap;
  padding: 0 1em;
  font-size: 1.1em !important;
  margin: 0;
}

.flag {
  height: 2.3em;
  padding: 0 1em;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fee4e2;
  margin-left: 1em;
  border-radius: 15px;
  svg {
    width: 25px;
    height: 25px;
    path {
      fill: #b42318;
    }
  }
}

.category {
  padding: 0.2em 1em;
  background-color: #e9eaebb2;
  color: #535862;
  border-radius: 7px;
  white-space: nowrap;
}

.fileInfo {
  display: flex;
  gap: 1em;
  .leftImage {
    img {
      width: 50px;
      width: 50px;
    }
  }
  .businessName {
    display: flex;
    align-items: center;
    p {
      font-size: 1.2rem;
      font-weight: 400 !important;
      color: #0a0d12;
      margin: 0;
    }
  }
}

.paginationWrapper {
  padding: 1.5em 0;
}
.paginationWrapper2 {
  padding: 1em;
}

.selectedStatus {
  padding: 0.5em 1em !important;
  // background-color: gray;
  border-radius: 25px;
}

.success {
  color: #2e7a31;
  background-color: #e3f8d9;
}

.failed {
  color: #b42318;
  background-color: #fee4e2;
}

.categoryItem {
  padding: 0.4em 1em;
  background-color: #e9eaebb2;
  color: #535862;
  border-radius: 5px;
}

.filterWrapper {
  display: flex;
  justify-content: space-between;
  padding: 0 2em;
  align-items: center;
  .searchWrapper {
    width: 75%;
    padding: 1em 0;
  }
  .moreFilter {
    padding: 1em;
    display: flex;
    gap: 1em;
    align-items: center;
    border-radius: 5px;
    height: 3.5em !important;
    // border: 1px solid #E9EAEB;
    border-radius: 10px;
    box-shadow: 0 0 1px 1px #e9eaeb;
    background-color: #fafafa !important;
    cursor: pointer;

    svg {
      height: 25px;
      width: 25px;
    }
  }
}

.adminFilterConatiner {
  width: 30%;
  // padding: 2em;
  background-color: white;
  box-shadow: 0 0 2px 2px $borderColor;
  position: absolute;
  overflow-y: auto;
  z-index: 1000;
  height: 70vh;
  right: 30px;
  top: 125px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .headerWrapper {
    display: flex;
    justify-content: space-between;
    .closeIconWrapper {
      background-color: rgb(189, 189, 189);
      border-radius: 5px;
      height: 30px;
      width: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      svg {
        width: 15px;
        height: 15px;
      }
    }
  }
}

.filterListContainer {
  margin: 1em 0;
  .itemWrapper {
    padding: 1em;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    margin: 1em 0;
    box-shadow: 0 0 2px 2px $borderColor;
    .filterItem {
      display: flex;
      justify-content: space-between;
      width: 100%;
      svg path {
        fill: black;
      }
      svg {
        width: 20px;
        height: 20px;
      }
      p {
        margin: 0;
      }
    }
  }
}

.listingWrapper {
  margin: 1em 0;
  .listingItem {
    display: flex;
    gap: 1em;
    align-items: center;
    margin: 0.5em 0;
    svg {
      height: 35px;
      width: 35px;
      rect {
        fill: #eaecf5;
      }
    }
    .businessLogo {
      svg,
      img {
        height: 30px;
        width: 30px;
      }
    }
    span {
      height: 20px;
      width: 20px;
      border-radius: 100%;
    }
  }
}

.listingItemContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mainWrapper {
  padding: 2em;
}

.btnWrapper {
  display: flex;
  gap: 1em;
  padding: 1em;
  position: sticky;
  bottom: 0;
  background-color: $white;
  justify-content: center;
  button {
    width: 48%;
    height: 3.2em !important;
    border-radius: 35px !important;
  }
  .cancelBtn {
    border: 1px solid #9ea5d1 !important;
    background-color: #eaecf5 !important;
    color: #293056 !important;
  }
  .applyBtn {
    background-color: #4e5ba6 !important;
    color: $white !important;
  }
}

.adminFilterHeader {
  display: flex;
  width: 100%;
  padding: 1em 1.5em;
  justify-content: space-between;
  align-items: center;
  .heading {
    padding: 0;
  }
  .headerIconWrapper {
    display: flex;
    gap: 1em;
    .headerIconContainer {
      height: 45px;
      width: 45px;
      border-radius: 15px;
      border: 1px solid #9ea5d1;
      background-color: #eaecf5;
      display: flex;
      justify-content: center;
      align-items: center;
      svg {
        width: 20px;
        height: 20px;
      }
    }
  }
}

.dateWrapper {
  display: flex;
  justify-content: space-between;
  .dateField {
    width: 45%;
    background-color: white !important;
    input {
      background-color: white !important;
      color: #0a0d12 !important;
    }
  }
}

.rightFilterWrapper {
  display: flex;
  gap: 1em;
  width: 35%;
  justify-content: flex-end;
  .downloadlogBtn {
    cursor: pointer;
    display: flex;
    gap: 1em;
    align-items: center;
    width: 10em;
    height: 2.5em;
    border-radius: 35px;
    border: 1px solid #d5d7da;
    justify-content: center;
    color: $white;
    background-color: #4e5ba6;
    font-size: 1.4em;
    svg {
      width: 25px;
      height: 25px;
      path {
        fill: white;
      }
    }
  }
}

.loaderContainer {
  height: 45vh;
  display: flex;
  align-items: center;
}

.noMatchMsg {
  height: 45vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #717680;
}

.dateDropdown {
  p {
    font-size: 1em !important;
  }
  padding: 0 1em;
  .selectedDateContent {
    span {
      color: #717680;
    }
  }
  .dateInfo {
    cursor: pointer;
  }
}
.datePopup {
  .dateInputWrapper {
    display: flex;
    gap: 1em;
    input {
      border: none;
      padding: 0.2em;
      color: #0a0d12 !important;
      background-color: transparent !important;
      cursor: pointer;
    }
    .inputWrapper {
      display: flex;
      gap: 1em;
      label {
        font-size: 0.9em !important;
        color: #717680;
      }
    }
    .clearBtn {
      border-radius: 35px;
      border: 1px solid #9ea5d1;
      color: #293056;
      background-color: #eaecf5;
    }
  }
}

.downloadfileBtn {
  cursor: pointer;
  display: flex;
  gap: 1em;
  align-items: center;
  width: 10em;
  height: 2.5em;
  border-radius: 35px;
  border: 1px solid #d5d7da;
  justify-content: center;
  color: $white;
  background-color: #4e5ba6;
  font-size: 1.4em;
  svg {
    width: 25px;
    height: 25px;
    path {
      fill: white;
    }
  }
}
