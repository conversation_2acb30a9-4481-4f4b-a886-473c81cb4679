import { useState, useEffect, useCallback, useMemo } from 'react';
import apiClient from '../../services/apiClient';
import { Pagination } from 'semantic-ui-react';
import useUpdateEffect from './useUpdateEffect';

const usePagination = (url, pageCount = 10, initialPage = 1, shouldDoInitialFetch = true, skipFetching = false) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalItemsCount, setTotalItemsCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [extraParams, setExtraParams] = useState({});

  const fetchPageData = useCallback(
    (page, skipLoading) => {
      if (!url) return;

      !skipLoading && setLoading(true);
      setError(null);

      const filteredParams = Object.fromEntries(
        Object.entries({
          page,
          ...extraParams,
        }).filter(([, value]) => !!value)
      );

      apiClient
        .get(url, { params: filteredParams })
        .then((res) => {
          const { count, results } = res;
          if (page === 1) {
            setTotalPages(Math.ceil(count / pageCount));
          }
          setTotalItemsCount(count);
          setData(results);
        })
        .catch((err) => {
          setData([]);
          setTotalItemsCount(0);
          setTotalPages(0);
          console.error('Error fetching data:', err);
          setError(err.message);
        })
        .finally(() => !skipLoading && setLoading(false));
    },
    [url, extraParams]
  );

  useUpdateEffect(() => {
    if (!skipFetching) {
      fetchPageData(currentPage);
    }
  }, [currentPage, extraParams, skipFetching]);

  //for intial should do intial fetching or not
  useEffect(() => {
    if (shouldDoInitialFetch) {
      fetchPageData(currentPage);
    }
  }, []);

  const handlePageChange = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  return {
    data,
    totalItemsCount,
    currentPage,
    totalPages,
    loading,
    error,
    setPage: handlePageChange,
    setExtraParams,
    refetch: (skipLoading = true) => fetchPageData(currentPage, skipLoading),
    PaginationComponent: totalPages > 1 && (
      <Pagination
        activePage={currentPage}
        totalPages={totalPages}
        onPageChange={(e, { activePage }) => handlePageChange(activePage)}
      />
    ),
  };
};

export default usePagination;
