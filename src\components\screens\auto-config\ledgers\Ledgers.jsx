import React, { useMemo } from 'react';
import { Form, Formik } from 'formik';
import { Button } from '@mui/material';
import LedgerTree from '../LedgerTree';
import DownloadBtn from '../../../custom-components/DownloadBtn';
import UploadBtn from '../../../custom-components/UploadBtn';
import { autoConfigUrls } from '../../../utils/apiurls';
import { useAuth } from '../../../../contexts/AuthContext';
import { useOutletContext } from 'react-router-dom';
import Checkbox from '../../../ui-components/fields/Checkbox';
import DynamicField from '../../../custom-components/DynamicField';
import SearchAutocomplete from '../../../custom-components/SearchAutocomplete';
import { updateBusinessPreferences } from '../../../services/syncSettingsServices';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../utils/apiUtils';

function mapInitialValues(businessPreferences) {
  return {
    purchase: {
      ledger_name: businessPreferences?.purchase_ledger?.value ?? '',
      uuid_id: businessPreferences?.purchase_ledger?.uuid_id ?? '',
    },
    tcs: {
      ledger_name: businessPreferences?.tcs?.value ?? '',
      uuid_id: businessPreferences?.tcs?.uuid_id ?? '',
    },
    round_off: {
      ledger_name: businessPreferences?.round_off?.value ?? '',
      uuid_id: businessPreferences?.round_off?.uuid_id ?? '',
    },
    discount: {
      ledger_name: businessPreferences?.discount?.value ?? '',
      uuid_id: businessPreferences?.discount?.uuid_id ?? '',
    },
    freight: {
      ledger_name: businessPreferences?.freight?.value ?? '',
      uuid_id: businessPreferences?.freight?.uuid_id ?? '',
    },
    insurance: {
      ledger_name: businessPreferences?.insurance?.value ?? '',
      uuid_id: businessPreferences?.insurance?.uuid_id ?? '',
    },
    other_charges: {
      ledger_name: businessPreferences?.other_charges?.value ?? '',
      uuid_id: businessPreferences?.other_charges?.uuid_id ?? '',
    },
    default_invoice_type: businessPreferences?.purchase_ledger_mode?.value ?? '',
    manage_inventory: businessPreferences?.is_inventory_enabled ?? false,
  };
}

function Ledgers() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences } = useOutletContext();

  const url = useMemo(() => {
    return `${autoConfigUrls.getLedgersOptions}?business_id=${globSelectedBusiness?.business_id}`;
  }, [globSelectedBusiness?.business_id]);

  const initialValues = useMemo(() => mapInitialValues(businessPreferences), [businessPreferences]);

  const handleSubmit = (values, { resetForm }) => {
    const payload = Object.entries(values).reduce((acc, [key, value]) => {
      if (key === 'default_invoice_type' && initialValues.default_invoice_type !== value) {
        acc[key] = value;
      } else if (key === 'manage_inventory' && initialValues[key]?.value !== value) {
        acc[key] = value;
      } else {
        if (value?.uuid_id && initialValues[key]?.uuid_id !== value.uuid_id) acc[key] = { uuid: value.uuid_id };
      }
      return acc;
    }, {});
    updateBusinessPreferences(globSelectedBusiness?.business_id, payload)
      .then((res) => {
        const latestBusinessPreferences = res.data;
        resetForm({ values: mapInitialValues(latestBusinessPreferences) });
        toast.success('Preferences updated successfully');
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(errorMessage);
      });
  };

  return (
    <div className="flex gap-4 mx-4 items-start h-full">
      {/* Default Ledger */}
      <div className="w-1/2 p-2 bg-white rounded-lg border border-gray-bg shadow-lg">
        <h3 className="px-4 py-2 border-b border-gray-300 text-lg font-semibold text-primary-color flex items-center gap-2 h-12">
          Default Ledgers
        </h3>
        <Formik initialValues={initialValues} onSubmit={handleSubmit}>
          {({ values, setFieldValue, dirty }) => (
            <Form className="grid grid-cols-2 gap-4 p-2.5">
              <DynamicField
                Component={SearchAutocomplete}
                url={url}
                optionLabel="ledger_name"
                optionValue="uuid_id"
                searchParam="search"
                label="Purchase"
                value={values.purchase}
                onSelect={(_, option) => setFieldValue('purchase', option)}
                config={businessPreferences?.purchase_ledger}
              />

              <DynamicField
                Component={SearchAutocomplete}
                url={url}
                optionLabel="ledger_name"
                optionValue="uuid_id"
                searchParam="search"
                label="TCS"
                value={values.tcs}
                onSelect={(_, option) => setFieldValue('tcs', option)}
                config={businessPreferences?.tcs}
              />

              <DynamicField
                Component={SearchAutocomplete}
                url={url}
                optionLabel="ledger_name"
                optionValue="uuid_id"
                searchParam="search"
                label="Round off"
                value={values.round_off}
                onSelect={(_, option) => setFieldValue('round_off', option)}
                config={businessPreferences?.round_off}
              />

              <DynamicField
                Component={SearchAutocomplete}
                url={url}
                optionLabel="ledger_name"
                optionValue="uuid_id"
                searchParam="search"
                label="Discount"
                value={values.discount}
                onSelect={(_, option) => setFieldValue('discount', option)}
                config={businessPreferences?.discount}
              />

              <DynamicField
                Component={SearchAutocomplete}
                url={url}
                optionLabel="ledger_name"
                optionValue="uuid_id"
                searchParam="search"
                label="Freight"
                value={values.freight}
                onSelect={(_, option) => setFieldValue('freight', option)}
                config={businessPreferences?.freight}
              />

              <DynamicField
                Component={SearchAutocomplete}
                url={url}
                optionLabel="ledger_name"
                optionValue="uuid_id"
                searchParam="search"
                label="Insurance"
                value={values.insurance}
                onSelect={(_, option) => setFieldValue('insurance', option)}
                config={businessPreferences?.insurance}
              />

              <DynamicField
                Component={SearchAutocomplete}
                url={url}
                optionLabel="ledger_name"
                optionValue="uuid_id"
                searchParam="search"
                label="Other Charges"
                value={values.other_charges}
                onSelect={(_, option) => setFieldValue('other_charges', option)}
                config={businessPreferences?.other_charges}
              />

              <DynamicField
                Component={SearchAutocomplete}
                url={url}
                optionLabel="value"
                optionValue="value"
                searchParam="search"
                label="Default Invoice Type"
                value={values.default_invoice_type}
                onSelect={(value) => setFieldValue('default_invoice_type', value)}
                config={businessPreferences?.purchase_ledger_mode}
              />

              {businessPreferences?.is_inventory_enabled?.show_field && (
                <Checkbox
                  label="Manage Inventory"
                  checked={values.manage_inventory}
                  onChange={(checked) => setFieldValue('manage_inventory', checked)}
                  className="m-2"
                  disabled={businessPreferences?.is_inventory_enabled?.access !== 'RW'}
                />
              )}

              <Button className="w-fit col-span-2" variant="contained" color="primary" type="submit" disabled={!dirty}>
                Save
              </Button>
            </Form>
          )}
        </Formik>
      </div>

      {/* Ledger Hierarchy */}
      <div className="w-1/2 p-2 bg-white rounded-lg border border-gray-bg shadow-lg flex flex-col h-full">
        <h3 className="px-4 py-2 border-b border-gray-300 text-lg font-semibold text-primary-color flex items-center justify-between h-12 flex-shrink-0">
          <span>Ledger Hierarchy</span>
          {!businessPreferences?.enable_auto_sync_master?.value && (
            <div className="flex gap-2 flex-shrink-0">
              <DownloadBtn
                selectionOptions={[
                  {
                    label: 'Ledgers',
                    value: 'ledgers',
                    url: `${autoConfigUrls.downloadLedgers}?business_id=${globSelectedBusiness?.business_id}`,
                  },
                  {
                    label: 'Ledger Groups',
                    value: 'ledger_groups',
                    url: `${autoConfigUrls.downloadLedgerGroups}?business_id=${globSelectedBusiness?.business_id}`,
                  },
                ]}
              />
              <UploadBtn
                selectionOptions={[
                  {
                    label: 'Ledgers',
                    value: 'ledgers',
                    url: `${autoConfigUrls.uploadLedgers}?business_id=${globSelectedBusiness?.business_id}`,
                  },
                  {
                    label: 'Ledger Groups',
                    value: 'ledger_groups',
                    url: `${autoConfigUrls.uploadLedgerGroups}?business_id=${globSelectedBusiness?.business_id}`,
                  },
                ]}
              />
            </div>
          )}
        </h3>

        <div className="relative flex-1 overflow-y-auto min-h-0">
          <LedgerTree data={[]} />
        </div>
      </div>
    </div>
  );
}

export default Ledgers;
