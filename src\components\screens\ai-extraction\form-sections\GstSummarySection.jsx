import React, { useCallback, useMemo, useState } from 'react';
import { Button, Menu, MenuItem } from '@mui/material';
import { checkSameState } from '../../../../components/utils/aiUtils';
import { Plus, Trash2 } from 'lucide-react';
import { resturls } from '../../../utils/apiurls';
import { useParams } from 'react-router-dom';
import useRealTimeValidation from './hook/useRealTimeValidation';
import ZohoGstFields from './components/zoho/ZohoGstFields';
import TallyGstFields from './components/tally/TallyGstFields';

const TAX_RATES = ['28.00', '18.00', '12.00', '6.00', '5.00', '3.00', '1.00', '0.25'];

const DEFAULT_ITEM_OBJ = {
  total_gst_rate: '',
  igst_amount: '',
  igst_ledger_name: '',
  cgst_amount: '',
  cgst_ledger_name: '',
  sgst_amount: '',
  sgst_ledger_name: '',
};

const SECTION = 'gst_ledgers';

function GstSummarySection({ formData, setFormData, isReadOnly, invoiceType, formAction }) {
  const data = formData[SECTION] || [];
  const [anchorEl, setAnchorEl] = useState(null);
  const { businessId } = useParams();
  const gstLedgerMode = formData?.gst_ledger_mode;
  const gstLedgersUrl = `${resturls.getGstLedgers}?business_id=${businessId}&gst_ledger_mode=${gstLedgerMode}`;
  const isRateWiseGSt = useMemo(
    () => formData?.gst_ledger_mode === 'rate_wise' || formData?.accounting_platform?.toLowerCase() === 'zoho',
    [formData?.gst_ledger_mode, formData?.accounting_platform]
  );
  const isSameState = useMemo(() => checkSameState(formData), [formData]);
  const [handleValidate] = useRealTimeValidation({
    data,
    formAction,
    isInventoryEnabled: formData?.is_inventory_enabled,
  });

  const handleAdd = useCallback(
    (rate) => {
      const taxRate = isSameState ? rate / 2 : rate;
      const newItem = {
        ...DEFAULT_ITEM_OBJ,
        total_gst_rate: rate,
        tax_rate: taxRate,
      };
      setFormData((prevData) => ({
        ...prevData,
        [SECTION]: [...prevData[SECTION], newItem],
      }));
    },
    [isSameState]
  );

  const handleRemoveItem = useCallback((index) => {
    setFormData((prevData) => {
      const prevItems = prevData[SECTION] || [];
      const updatedItems = prevItems.filter((_, filterIndex) => filterIndex !== index);
      const mergedData = {
        [SECTION]: updatedItems,
        sales_of_product_services: prevData?.sales_of_product_services,
        invoice_summary: prevData?.invoice_summary,
      };
      handleValidate(SECTION, invoiceType, mergedData, true);
      handleValidate('invoice_summary', invoiceType, mergedData, true);
      return {
        ...prevData,
        [SECTION]: updatedItems,
      };
    });
  }, []);

  const handleOnPaste = useCallback((copyText, field, index) => {
    setFormData((prevData) => {
      const updatedItems = prevData[SECTION].map((item, i) =>
        i === index ? { ...item, [field]: copyText?.replace(/,/g, '') || '' } : item
      );
      const mergedData = {
        [SECTION]: updatedItems,
        sales_of_product_services: prevData?.sales_of_product_services,
        invoice_summary: prevData?.invoice_summary,
      };
      handleValidate(SECTION, invoiceType, mergedData, true);
      handleValidate('invoice_summary', invoiceType, mergedData, true);
      return {
        ...prevData,
        [SECTION]: updatedItems,
      };
    });
  }, []);

  const handleBlur = useCallback(() => {
    const mergedData = {
      [SECTION]: data,
      sales_of_product_services: formData?.sales_of_product_services,
      invoice_summary: formData.invoice_summary,
    };
    handleValidate(SECTION, invoiceType, mergedData, true);
    handleValidate('invoice_summary', invoiceType, mergedData, true);
  }, [formData]);

  return (
    <div className="flex flex-col gap-1">
      {data.map((item, index) => (
        <div
          key={item?.total_gst_rate || index}
          className={`${index !== data.length - 1 ? 'pb-3 border-b-[3px] border-[#D5D7DA] border-dashed' : ''}`}
        >
          {/** Delete button */}
          {isRateWiseGSt && (
            <div className="flex items-center justify-between text-[#535862] font-bold text-lg mt-0 mb-4">
              GST @{item?.total_gst_rate}%
              <Trash2
                className={`w-5 h-5 stroke-red-600 cursor-pointer ${
                  isReadOnly ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''
                }`}
                onClick={() => {
                  if (!isReadOnly) {
                    handleRemoveItem(index);
                  }
                }}
              />
            </div>
          )}

          <div className="form-grid">
            {formData?.accounting_platform?.toLowerCase() !== 'zoho' && (
              <TallyGstFields
                data={item}
                isReadOnly={isReadOnly}
                index={index}
                gstLedgersUrl={gstLedgersUrl}
                formAction={formAction}
                SECTION={SECTION}
                isSameState={isSameState}
                handleOnPaste={handleOnPaste}
                handleBlur={handleBlur}
                isRateWiseGSt={isRateWiseGSt}
              />
            )}

            {formData?.accounting_platform?.toLowerCase() === 'zoho' && (
              <ZohoGstFields
                data={item}
                isReadOnly={isReadOnly}
                index={index}
                gstLedgersUrl={gstLedgersUrl}
                formAction={formAction}
                SECTION={SECTION}
                isSameState={isSameState}
              />
            )}
          </div>
        </div>
      ))}

      {/* Add GST Summary Button */}
      {isRateWiseGSt && (
        <>
          <Button
            variant="outlined"
            startIcon={<Plus className="w-4 h-4" />}
            onClick={(event) => setAnchorEl(event.currentTarget)}
            disabled={isReadOnly}
            size="small"
            className="w-fit"
          >
            Add GST Summary
          </Button>

          <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={() => setAnchorEl(null)}>
            {TAX_RATES.map((rate) => (
              <MenuItem
                key={rate}
                onClick={() => {
                  handleAdd(rate);
                  setAnchorEl(null);
                }}
                disabled={data.some((item) => Number(item.total_gst_rate) === Number(rate))}
              >
                {rate}%
              </MenuItem>
            ))}
          </Menu>
        </>
      )}
    </div>
  );
}

export default GstSummarySection;
