import React, { useCallback, useMemo, useState } from 'react';
import { useClickOutside } from '../../../../components/global/hooks';
import { checkSameState } from '../../../../components/utils/aiUtils';
import { ChevronLeft, ChevronRight, Circle, Plus, Trash2 } from 'lucide-react';
import { useParams } from 'react-router-dom';
import useRealTimeValidation from './hook/useRealTimeValidation';
import ZohoGstFields from './components/zoho/ZohoGstFields';
import TallyGstFields from './components/tally/TallyGstFields';

const TAX_RATES = ['28.00', '18.00', '12.00', '6.00', '5.00', '3.00', '1.00', '0.25'];

const DEFAULT_ITEM_OBJ = {
  total_gst_rate: '',
  igst_amount: '',
  igst_ledger_name: '',
  cgst_amount: '',
  cgst_ledger_name: '',
  sgst_amount: '',
  sgst_ledger_name: '',
};

const SECTION = 'gst_ledgers';

function GstSummarySection({ formData, setFormData, isReadOnly, invoiceType, formAction }) {
  const data = formData[SECTION] || [];
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { elementRef } = useClickOutside(() => setIsDropdownOpen(false), true);
  const { businessId } = useParams();
  const gstLedgerMode = formData?.gst_ledger_mode;
  const gstLedgersUrl = `${resturls.getGstLedgers}?business_id=${businessId}&gst_ledger_mode=${gstLedgerMode}`;
  const isRateWiseGSt = useMemo(
    () => formData?.gst_ledger_mode === 'rate_wise' || formData?.accounting_platform?.toLowerCase() === 'zoho',
    [formData?.gst_ledger_mode, formData?.accounting_platform]
  );
  const isSameState = useMemo(() => checkSameState(formData), [formData]);
  const [handleValidate] = useRealTimeValidation({
    data,
    formAction,
    isInventoryEnabled: formData?.is_inventory_enabled,
  });

  const handleAdd = useCallback(
    (rate) => {
      const taxRate = isSameState ? rate / 2 : rate;
      const newItem = {
        ...DEFAULT_ITEM_OBJ,
        total_gst_rate: rate,
        tax_rate: taxRate,
      };
      setFormData((prevData) => ({
        ...prevData,
        [SECTION]: [...prevData[SECTION], newItem],
      }));
    },
    [isSameState]
  );

  const handleRemoveItem = useCallback((index) => {
    setFormData((prevData) => {
      const prevItems = prevData[SECTION] || [];
      const updatedItems = prevItems.filter((_, filterIndex) => filterIndex !== index);
      const mergedData = {
        [SECTION]: updatedItems,
        sales_of_product_services: prevData?.sales_of_product_services,
        invoice_summary: prevData?.invoice_summary,
      };
      handleValidate(SECTION, invoiceType, mergedData, true);
      handleValidate('invoice_summary', invoiceType, mergedData, true);
      return {
        ...prevData,
        [SECTION]: updatedItems,
      };
    });
  }, []);

  const handleOnPaste = useCallback((copyText, field, index) => {
    setFormData((prevData) => {
      const updatedItems = prevData[SECTION].map((item, i) =>
        i === index ? { ...item, [field]: copyText?.replace(/,/g, '') || '' } : item
      );
      const mergedData = {
        [SECTION]: updatedItems,
        sales_of_product_services: prevData?.sales_of_product_services,
        invoice_summary: prevData?.invoice_summary,
      };
      handleValidate(SECTION, invoiceType, mergedData, true);
      handleValidate('invoice_summary', invoiceType, mergedData, true);
      return {
        ...prevData,
        [SECTION]: updatedItems,
      };
    });
  }, []);

  const handleBlur = useCallback(() => {
    const mergedData = {
      [SECTION]: data,
      sales_of_product_services: formData?.sales_of_product_services,
      invoice_summary: formData.invoice_summary,
    };
    handleValidate(SECTION, invoiceType, mergedData, true);
    handleValidate('invoice_summary', invoiceType, mergedData, true);
  }, [formData]);

  return (
    <div className="flex flex-col gap-8">
      {data.map((item, index) => (
        <div
          key={item?.total_gst_rate || index}
          className={`${index !== data.length - 1 ? 'pb-3 border-b-[3px] border-[#D5D7DA] border-dashed' : ''}`}
        >
          {/** Delete button */}
          {isRateWiseGSt && (
            <div className="flex items-center justify-between text-[#535862] font-bold text-lg mt-0 mb-4">
              GST @{item?.total_gst_rate}%
              <Trash2
                className={`w-5 h-5 stroke-red-600 cursor-pointer ${
                  isReadOnly ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''
                }`}
                onClick={() => {
                  if (!isReadOnly) {
                    handleRemoveItem(index);
                  }
                }}
              />
            </div>
          )}

          <div className="form-grid">
            {formData?.accounting_platform?.toLowerCase() !== 'zoho' && (
              <TallyGstFields
                data={item}
                isReadOnly={isReadOnly}
                index={index}
                gstLedgersUrl={gstLedgersUrl}
                formAction={formAction}
                SECTION={SECTION}
                isSameState={isSameState}
                handleOnPaste={handleOnPaste}
                handleBlur={handleBlur}
                isRateWiseGSt={isRateWiseGSt}
              />
            )}

            {formData?.accounting_platform?.toLowerCase() === 'zoho' && (
              <ZohoGstFields
                data={item}
                isReadOnly={isReadOnly}
                index={index}
                gstLedgersUrl={gstLedgersUrl}
                formAction={formAction}
                SECTION={SECTION}
                isSameState={isSameState}
              />
            )}
          </div>
        </div>
      ))}

      {/* Add GST Summary Button */}
      {isRateWiseGSt && (
        <div className="flex gap-2" ref={elementRef}>
          <div
            className={`flex items-center w-fit h-fit rounded-full text-lg text-nowrap gap-2 py-2 px-4 border border-[#D5D7DA] text-[#7E6607] ${
              isReadOnly ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
            } select-none mb-2`}
            onClick={() => {
              if (!isReadOnly) {
                setIsDropdownOpen(!isDropdownOpen);
              }
            }}
          >
            <div className="relative w-6 h-6 transition-all duration-300 ease-in-out">
              {isDropdownOpen ? (
                <Circle className="w-6 h-6 text-[#7E6607] fill-[#7E6607] transition-all duration-300 ease-in-out" />
              ) : (
                <Plus className="w-6 h-6 transition-all duration-300 ease-in-out" />
              )}
            </div>
            Add GST Summary
            {isDropdownOpen ? <ChevronRight className="w-4 h-4 ml-1" /> : <ChevronLeft className="w-4 h-4 ml-1" />}
          </div>

          {isDropdownOpen && (
            <div className="w-fit h-fit bg-white border border-gray-200 rounded-full shadow-lg py-2 px-2">
              <div className="flex items-center justify-around flex-row flex-wrap gap-3">
                {TAX_RATES.map((rate) => (
                  <RateBtn
                    key={rate}
                    rate={rate}
                    onClick={handleAdd}
                    disabled={isReadOnly || data.some((item) => Number(item.total_gst_rate) === Number(rate))}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

function RateBtn({ rate, onClick = null, disabled = false }) {
  return (
    <div
      className={`p-1 hover:bg-gray-100 select-none text-sm border border-gray-200 rounded-full ${
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
      }`}
      onClick={() => {
        if (!disabled) {
          onClick(rate);
        }
      }}
    >
      {rate}%
    </div>
  );
}

export default GstSummarySection;
