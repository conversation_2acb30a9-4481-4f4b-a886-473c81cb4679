@import '../../assets/scss/main.scss';

.mainLayoutContainer {
  max-height: 100vh;
  max-height: 100dvh;
  overflow: hidden;
}

.layoutWrapperContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 85.5vh;
  height: 85.5dvh;
  padding: 0 1em;
  overflow: hidden;

  @include for_media(mobileScreen) {
    margin: 0;
    padding-bottom: 2em;
    overflow-y: auto;
  }
}

.leftWrapper {
  width: 250px;
  min-width: 250px;
  max-height: 80vh;
  margin-right: 2em;
}

.rightWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: calc(100% - 270px);
  min-width: calc(100% - 270px);
  padding-top: 1em;
  position: relative;
  @include hide-scrollbar;

  @include for_media(mobileScreen) {
    width: 100%;
    margin: 0 1em;
    overflow-x: hidden;
  }
}
