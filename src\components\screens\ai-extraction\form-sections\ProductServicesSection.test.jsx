import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ProductServicesSection from './ProductServicesSection';
import { sectionValidation } from '../../../services/aiServices';
import { removeObjectsFromJson } from '../../../utils/jsonUtils';

jest.mock('../../../services/aiServices', () => ({
  sectionValidation: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({
    fileId: 'mock-file-id',
    businessId: 'mock-business-id',
  }),
}));

const mockFormData = {
  sales_of_product_services: [
    {
      item_no: '0',
      item_name: 'Gold Ornanments',
      product_service_description: 'Gold Ornanments',
      ledger: '',
      hsn_sac: '711319',
      quantity: '181.23',
      unit: 'gms',
      rate: '5727.20',
      amount: '1037941.00',
      gst_rate_cgst: '1.50',
      gst_rate_sgst: '1.50',
      gst_rate_igst: '18.00',
      gst_rate_cess: null,
      item_discount: '0.00',
      exact_match: { hsn_sac: true, rate: true },
      recommended_fields: [
        {
          unit: '1037941.00',
        },
      ],
    },
    {
      item_no: '1',
      item_name: 'Hall Marking Charges',
      product_service_description: 'Hall Marking Charges',
      ledger: '',
      hsn_sac: '998346',
      quantity: '0.00',
      unit: null,
      rate: '0.00',
      amount: '720.00',
      gst_rate_cgst: '9.00',
      gst_rate_sgst: '9.00',
      gst_rate_igst: '2.00',
      gst_rate_cess: null,
      item_discount: '0.00',
      exact_match: { hsn_sac: true, rate: true },
      recommended_fields: [
        {
          unit: '720.00',
        },
      ],
    },
  ],
  invoice_summary: {
    currency: '₹',
    exchange_rate: '1.00',
  },
  bill_to_details: {
    purchase_ledger_name: 'Purchase A/c',
  },
  gst_ledgers: null,
  purchase_ledger_mode: 'invoice',
};

const FormWrapper = ({ initialFormData, ...restProps }) => {
  const [formData, setFormData] = React.useState(initialFormData);

  const formAction = React.useCallback((action, section, field, value) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case 'FIELD_CHANGE':
          updatedSection = { ...prevSection, [field]: value };
          break;

        case 'UPDATE_SECTION':
          updatedSection = { ...prevSection, ...value };
          break;

        case 'HARD_UPDATE_SECTION':
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  return (
    <ProductServicesSection
      formData={formData}
      setFormData={setFormData}
      isReadOnly={false}
      invoiceType="purchase"
      formAction={formAction}
      {...restProps}
    />
  );
};

describe('ProductServicesSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all fields with correct initial values for first item', () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    // Get all fields and check their values
    const itemNameInput = screen.getAllByLabelText('Item Name')[0];
    const descriptionInput = screen.getAllByLabelText('Description')[0];
    const hsnSacInput = screen.getAllByLabelText('HSN / SAC')[0];
    const itemDiscountInput = screen.getAllByLabelText('Item Discount')[0];
    const quantityInput = screen.getAllByLabelText('Quantity')[0];
    const unitInput = screen.getAllByLabelText('Unit')[0];
    const rateInput = screen.getAllByLabelText('Rate (pre-tax)')[0];
    const amountInput = screen.getAllByLabelText('Amount')[0];
    const igstRateInput = screen.getAllByLabelText('IGST Rate')[0];
    const cessRateInput = screen.getAllByLabelText('CESS Rate')[0];

    expect(itemNameInput).toHaveValue('Gold Ornanments');
    expect(descriptionInput).toHaveValue('Gold Ornanments');
    expect(hsnSacInput).toHaveValue('711319');
    expect(itemDiscountInput).toHaveValue('0.00');
    expect(quantityInput).toHaveValue('181.23');
    expect(unitInput).toHaveValue('gms');
    expect(rateInput).toHaveValue('5727.20');
    expect(amountInput).toHaveValue('10,37,941.00');
    expect(igstRateInput).toHaveValue('18.00');
    expect(cessRateInput).toHaveValue('');
  });

  it('renders all fields with correct initial values for second item', () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    // Get all fields and check their values
    const itemNameInput = screen.getAllByLabelText('Item Name')[1];
    const descriptionInput = screen.getAllByLabelText('Description')[1];
    const hsnSacInput = screen.getAllByLabelText('HSN / SAC')[1];
    const itemDiscountInput = screen.getAllByLabelText('Item Discount')[1];
    const quantityInput = screen.getAllByLabelText('Quantity')[1];
    const unitInput = screen.getAllByLabelText('Unit')[1];
    const rateInput = screen.getAllByLabelText('Rate (pre-tax)')[1];
    const amountInput = screen.getAllByLabelText('Amount')[1];
    const igstRateInput = screen.getAllByLabelText('IGST Rate')[1];
    const cessRateInput = screen.getAllByLabelText('CESS Rate')[1];

    expect(itemNameInput).toHaveValue('Hall Marking Charges');
    expect(descriptionInput).toHaveValue('Hall Marking Charges');
    expect(hsnSacInput).toHaveValue('998346');
    expect(itemDiscountInput).toHaveValue('0.00');
    expect(quantityInput).toHaveValue('0.00');
    expect(unitInput).toHaveValue('');
    expect(rateInput).toHaveValue('0.00');
    expect(amountInput).toHaveValue('720.00');
    expect(igstRateInput).toHaveValue('2.00');
    expect(cessRateInput).toHaveValue('');
  });

  it('shows initial suggestions when available', () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    // Check for suggestion icons
    expect(screen.getByTestId('copy-icon-unit_0')).toBeInTheDocument();
    expect(screen.getByTestId('paste-icon-unit_0')).toBeInTheDocument();
    expect(screen.getByTestId('copy-icon-unit_1')).toBeInTheDocument();
    expect(screen.getByTestId('paste-icon-unit_1')).toBeInTheDocument();
  });

  it('shows initial error messages & indicators correctly', async () => {
    const formDataWithErrors = {
      ...mockFormData,
      sales_of_product_services: [
        {
          ...mockFormData.sales_of_product_services[0],
          error: {
            item_name: {
              short_message: 'Invalid item name',
              long_message: 'Please enter a valid item name',
            },
            amount: {
              short_message: 'Invalid amount',
              long_message: 'Please enter a valid amount',
            },
          },
        },
      ],
    };

    render(<FormWrapper initialFormData={formDataWithErrors} />);

    const errorIcons = screen.getAllByTestId('errorIcon');
    expect(errorIcons).toHaveLength(2);

    const itemNameTooltip = await screen.findByTestId('tooltip-item_name_0');
    await userEvent.hover(itemNameTooltip);
    const itemNameTooltipContent = await screen.findByText('Invalid item name');
    expect(itemNameTooltipContent).toBeInTheDocument();

    const amountTooltip = await screen.findByTestId('tooltip-amount_0');
    await userEvent.hover(amountTooltip);
    const amountTooltipContent = await screen.findByText('Invalid amount');
    expect(amountTooltipContent).toBeInTheDocument();
  });

  it('shows initial warning messages & indicators correctly', async () => {
    const formDataWithWarnings = {
      ...mockFormData,
      sales_of_product_services: [
        {
          ...mockFormData.sales_of_product_services[0],
          warning: {
            item_name: {
              short_message: 'Item name might be incorrect',
              long_message: 'Please verify the item name',
            },
            amount: {
              short_message: 'Amount might be incorrect',
              long_message: 'Please verify the amount',
            },
          },
        },
      ],
    };

    render(<FormWrapper initialFormData={formDataWithWarnings} />);

    const warningIcons = screen.getAllByTestId('warningIcon');
    expect(warningIcons).toHaveLength(2);

    const itemNameTooltip = await screen.findByTestId('tooltip-item_name_0');
    await userEvent.hover(itemNameTooltip);
    const itemNameTooltipContent = await screen.findByText('Item name might be incorrect');
    expect(itemNameTooltipContent).toBeInTheDocument();

    const amountTooltip = await screen.findByTestId('tooltip-amount_0');
    await userEvent.hover(amountTooltip);
    const amountTooltipContent = await screen.findByText('Amount might be incorrect');
    expect(amountTooltipContent).toBeInTheDocument();
  });

  it('shows initial exact match indicators when present', () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    const successIcons = screen.getAllByTestId('successIcon');
    expect(successIcons.length).toBe(4);
  });

  it('handles suggestion paste functionality correctly', async () => {
    const updatedRate = '22.00';
    const mockFormDataWithSuggestion = {
      ...mockFormData,
      sales_of_product_services: [
        {
          ...mockFormData.sales_of_product_services[0],
          recommended_fields: [
            {
              rate: updatedRate,
            },
          ],
        },
        mockFormData.sales_of_product_services[1],
      ],
    };

    render(<FormWrapper initialFormData={mockFormDataWithSuggestion} />);

    // Mock the validation response
    sectionValidation.mockResolvedValue({
      sales_of_product_services: [
        {
          ...mockFormData.sales_of_product_services[0],
          rate: updatedRate,
        },
        mockFormData.sales_of_product_services[1],
      ],
    });

    const ratePasteIcon = screen.getByTestId('paste-icon-rate_0');
    await userEvent.click(ratePasteIcon);

    const salesOfProductServices = removeObjectsFromJson(mockFormData.sales_of_product_services, [
      'error',
      'warning',
      'exact_match',
      'recommended_fields',
    ]);

    // Check if API was called with correct payload
    const expectedPayload = {
      bill_to_details: mockFormData.bill_to_details,
      sales_of_product_services: [
        {
          ...salesOfProductServices[0],
          rate: updatedRate,
        },
        ...salesOfProductServices.slice(1),
      ],
    };

    expect(sectionValidation).toHaveBeenCalledWith(
      'mock-file-id',
      'mock-business-id',
      'sales_of_product_services',
      'purchase',
      expectedPayload
    );

    const rateInput = screen.getAllByLabelText('Rate (pre-tax)')[0];
    await waitFor(() => {
      expect(rateInput).toHaveValue('22.00');
    });
  });

  it('validates field on input change and blur', async () => {
    const newValue = 'New Item Name';
    render(<FormWrapper initialFormData={mockFormData} />);

    const mockResponse = {
      bill_to_details: mockFormData.bill_to_details,
      sales_of_product_services: [
        {
          ...mockFormData.sales_of_product_services[0],
          item_name: newValue,
          error: {
            item_name: {
              short_message: 'Invalid item name',
              long_message: 'Please enter a valid item name',
            },
          },
        },
        mockFormData.sales_of_product_services[1],
      ],
    };

    sectionValidation.mockResolvedValue(mockResponse);

    const itemNameInput = screen.getAllByLabelText('Item Name')[0];
    await userEvent.clear(itemNameInput);
    await userEvent.type(itemNameInput, newValue);
    await userEvent.tab();

    expect(itemNameInput).toHaveValue(newValue);

    const salesOfProductServices = removeObjectsFromJson(mockFormData.sales_of_product_services, [
      'error',
      'warning',
      'exact_match',
      'recommended_fields',
    ]);
    const expectedPayload = {
      bill_to_details: mockFormData.bill_to_details,
      sales_of_product_services: [
        {
          ...salesOfProductServices[0],
          item_name: newValue,
        },
        salesOfProductServices[1],
      ],
    };

    expect(sectionValidation).toHaveBeenCalledWith(
      'mock-file-id',
      'mock-business-id',
      'sales_of_product_services',
      'purchase',
      expectedPayload
    );

    await waitFor(() => {
      expect(screen.getByTestId('errorIcon')).toBeInTheDocument();
    });
  });

  it('handles adding new item', async () => {
    render(<FormWrapper initialFormData={mockFormData} />);

    const addItemButton = screen.getByText('Add Item');
    await userEvent.click(addItemButton);

    // Check if new item fields are rendered
    const newItemFields = screen.getAllByLabelText(
      /Item Name|Description|HSN \/ SAC|Item Discount|Quantity|Unit|Rate \(pre-tax\)|Amount|IGST Rate|CESS Rate/
    );
    expect(newItemFields.length).toBe(30); // More fields after adding new item
  });
});
