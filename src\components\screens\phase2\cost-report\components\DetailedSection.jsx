import React from 'react';
import PaginationContent from '../../../../utils/reportUtils/PaginationContent';
import style from '../scss/detailedSection.module.scss';
import { LoadingWrapper } from '../../../../global/components';
import { formatAmount, formatDateRange } from '../../../../utils/dateUtils';
import { DocumentIcon } from '../../../../../assets/svgs';

const DetailsSection = ({
  costDetails,
  details,
  activeDetailsPage,
  handleDetailsPagination,
  isDetailedReportLoading,
}) => {
  return (
    <div className={style.detailedWrapper}>
      <div className={style.overAllInfo}>
        <div className={style.leftInfo}>
          <h4>
            <DocumentIcon />
            Total Cost
          </h4>
          <p>{costDetails?.start_time && formatDateRange(costDetails?.start_time, costDetails?.end_time)}</p>
        </div>
        <div className={style.rightInfo}>
          <h4>{formatAmount(Number(costDetails?.total_cost))}</h4>
        </div>
      </div>

      <div className={style.detailsListContainer}>
        <h5>Detailed Report</h5>
        <LoadingWrapper
          loading={isDetailedReportLoading}
          isRenderEmpty={details?.data?.length === 0}
          renderEmpty={{
            title: 'Details are currently unavailable.',
          }}
          minHeight={true}
        >
          <div className={style.detailsList}>
            {details?.data?.map((info) => (
              <div key={info.label}>
                <p>{formatDateRange(info?.label, '', true)}</p>
                <p>{formatAmount(Number(info?.amount))}</p>
              </div>
            ))}

            {details?.pagination?.total_pages > 1 && (
              <div className={style.paginationWrapper}>
                <PaginationContent
                  activePage={activeDetailsPage}
                  totalPages={details?.pagination?.total_pages}
                  pageChangeFunction={handleDetailsPagination}
                />
              </div>
            )}
          </div>
        </LoadingWrapper>
      </div>
    </div>
  );
};

export default DetailsSection;
