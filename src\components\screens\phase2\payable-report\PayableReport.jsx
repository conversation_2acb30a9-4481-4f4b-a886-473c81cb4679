import React, { useState, useEffect } from 'react';
import style from '../receivable-report/scss/receivableReport.module.scss';
import { mediaBreakpoint } from '../../../global/MediaBreakPointes';
import GlobalService from '../../../services/GlobalServices';
import { resturls } from '../../../utils/apiurls';
import ls from 'local-storage';
import { useNavigate } from 'react-router-dom';
import { BackIcon, DownloadIcon } from '../../../../assets/svgs';
import RenderOverallContent from './component/RenderOverallContent';
import { LoadingWrapper } from '../../../global/components';
import ReportSortFilter from '../../../global/components/ReportSortFilter';

const PayableReport = () => {
  const [selectedTimeline, setTimeline] = useState(null);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [downloadModal, setDownloadModal] = useState(false);
  const [activePage, setActivePage] = useState(1);
  const [payableDetails, setPayableDetails] = useState({});
  const [activeTab, setActiveTab] = useState('customer');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [customerDetails, setCustomerDetails] = useState({});
  const [invoiceInfo, setInvoiceInfo] = useState({});
  const [originalList, setOriginalList] = useState({ customerData: [], invoiceList: [] });
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const [detailedLoading, setDetailedLoading] = useState({ customer: false, invoice: false });
  const navigate = useNavigate();
  const business_id = ls.get('selectedBusiness')?.business_id;

  const obtainPayableReportDetails = () => {
    const storedPayableData = ls.get('payableData');
    const storedCustomerData = ls.get('customerData');
    const storedInvoiceData = ls.get('invoiceData');

    const currentDate = new Date();

    if (!storedPayableData || !storedPayableData.expiry_at || new Date(storedPayableData.expiry_at) < currentDate) {
      ls.remove('payableData');
      ls.remove('customerData');
      ls.remove('invoiceData');

      fetchPayableReportData();
      fetchCustomerContent();
      fetchInvoiceContent();
    } else {
      setPayableDetails(storedPayableData.data);
      setInvoiceInfo(storedInvoiceData);
      setCustomerDetails(storedCustomerData);
      setOriginalList((prev) => ({
        ...prev,
        customerData: storedCustomerData?.data,
        invoiceList: storedInvoiceData?.data,
      }));
    }
  };

  const fetchPayableReportData = () => {
    setIsLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          const { expiry_at } = respdata;
          const dataToStore = {
            data: respdata,
            expiry_at,
          };
          ls.set('payableData', dataToStore);
          setPayableDetails(respdata);
          setIsLoading(false);
        }
      },
      `${resturls.obtainAccountPayable}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  const fetchCustomerContent = () => {
    setDetailedLoading({ ...detailedLoading, customer: true });
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          setDetailedLoading({ ...detailedLoading, customer: false });
          setCustomerDetails(respdata);
          setOriginalList((prev) => ({
            ...prev,
            customerData: respdata?.data, // Ensure data is correctly assigned
          }));
          ls.set('customerData', respdata); // Store under a separate key
        }
      },
      `${resturls.obtainCustomerContentPayable}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  const fetchInvoiceContent = () => {
    setDetailedLoading({ ...detailedLoading, invoice: true });
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          setDetailedLoading({ ...detailedLoading, invoice: false });
          setInvoiceInfo(respdata);
          setOriginalList((prev) => ({
            ...prev,
            invoiceList: respdata?.data, // Ensure data is correctly assigned
          }));
          ls.set('invoiceData', respdata); // Store under a separate key
        }
      },
      `${resturls.obtainInvoiceContentPayable}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  useEffect(() => {
    obtainPayableReportDetails();
  }, []);

  const handlePagination = (direction) => {
    const totalPage = payableDetails?.pagination?.total_pages;
    let page = activePage;

    if (direction === 'right') {
      if (page < totalPage) {
        page = page + 1;
      }
    } else if (direction === 'left') {
      if (page > 1) {
        page = page - 1;
      }
    }
    setActivePage(page);
  };

  const handleSelectDropdown = (selectedOption) => {
    if (selectedOption?.value === selectedTimeline?.value) {
      setOpenDropdown(false);
      return;
    }

    setTimeline(selectedOption);
  };

  const handleApplySort = () => {
    let sortedData = [];
    if (activeTab === 'customer') {
      sortedData = [...customerDetails?.data];
    } else {
      sortedData = [...invoiceInfo?.data];
    }
    const selectedOption = selectedTimeline;

    if (selectedOption.value === 'earliestToLatest') {
      if (activeTab === 'customer') {
        sortedData = sortedData.map((customer) => ({
          ...customer,
          invoices: {
            ...customer.invoices,
            data: [...customer.invoices.data].sort((a, b) => new Date(a.due_date) - new Date(b.due_date)),
          },
        }));
      } else {
        sortedData = [...sortedData].sort((a, b) => new Date(a.due_date) - new Date(b.due_date));
      }
    } else if (selectedOption.value === 'latestToEarliest') {
      if (activeTab === 'customer') {
        sortedData = sortedData.map((customer) => ({
          ...customer,
          invoices: {
            ...customer.invoices,
            data: [...customer.invoices.data].sort((a, b) => new Date(b.due_date) - new Date(a.due_date)),
          },
        }));
      } else {
        sortedData = [...sortedData].sort((a, b) => new Date(b.due_date) - new Date(a.due_date));
      }
    } else if (selectedOption.value === 'lowToHigh') {
      if (activeTab === 'customer') {
        sortedData = [...sortedData].sort((a, b) => a.amount - b.amount);
        sortedData = sortedData.map((customer) => ({
          ...customer,
          invoices: {
            ...customer.invoices,
            data: [...customer.invoices.data].sort((a, b) => a.amount - b.amount),
          },
        }));
      } else {
        sortedData = [...sortedData].sort((a, b) => a.amount - b.amount);
      }
    } else if (selectedOption.value === 'highToLow') {
      if (activeTab === 'customer') {
        sortedData = [...sortedData].sort((a, b) => b.amount - a.amount); // Fix: Change the order here
        sortedData = sortedData.map((customer) => ({
          ...customer,
          invoices: {
            ...customer.invoices,
            data: [...customer.invoices.data].sort((a, b) => b.amount - a.amount),
          },
        }));
      } else {
        sortedData = [...sortedData].sort((a, b) => b.amount - a.amount);
      }
    }
    if (activeTab === 'customer') {
      setCustomerDetails({ ...customerDetails, data: sortedData });
    } else {
      setInvoiceInfo({ ...invoiceInfo, data: sortedData });
    }

    setOpenDropdown(false);
  };

  const handleClear = () => {
    if (activeTab === 'customer') {
      setCustomerDetails({ ...customerDetails, data: originalList?.customerData });
    } else {
      setInvoiceInfo({ ...invoiceInfo, data: originalList?.invoiceList });
    }
    setTimeline(false);
    setOpenDropdown(false);
  };

  const handleDropdownList = () => {
    setOpenDropdown(true);
  };

  const handleSearch = (e) => {
    const { value } = e.target;
    setSearchTerm(value);
    if (activeTab === 'customer') {
      const filteredList = originalList?.customerData?.filter((info) => {
        return (
          info?.ledger_name?.toLowerCase().includes(value.toLowerCase()) ||
          info?.invoices?.data?.some(
            (invoice) =>
              invoice?.invoice_number?.toString().includes(value) || invoice?.amount?.toString().includes(value)
          )
        );
      });

      setCustomerDetails({ ...customerDetails, data: filteredList });
    } else {
      const filteredList = originalList?.invoiceList?.filter((info) =>
        info?.ledger_name?.toLowerCase().includes(value.toLowerCase())
      );
      setInvoiceInfo({ ...invoiceInfo, data: filteredList });
    }
  };

  const handleRefresh = () => {
    ls.remove('payableDetails');
    ls.remove('payableCustomerData');
    ls.remove('payableInvoiceData');
    fetchPayableReportData();
    fetchCustomerContent();
    fetchInvoiceContent();
  };

  const handleDownload = (transaction) => {
    GlobalService.generalSelect(
      (respdata, error) => {
        if (error || !respdata) {
          console.error('Download failed:', error);
          alert('Failed to download the report. Please check your data or try again.');
          return; // Stop execution if error occurs or data is empty
        }

        setDownloadModal(false);

        // Convert response to a Blob with 'text/csv' MIME type
        const blob = new Blob([respdata], { type: 'text/csv' });

        // Create an anchor element to trigger the download
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `Payable_Report.csv`; // Change file extension to .csv

        // Append the link to the DOM and simulate a click to start the download
        document.body.appendChild(link);
        link.click();

        // Clean up by revoking the object URL
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      },
      `${resturls.downloadPayableReport}?business_id=${business_id}${transaction ? '&transaction=true' : ''}`,
      {},
      'GET'
    );
  };

  const handleClose = () => {
    setOpenDropdown(false);
  };

  const commonProps = {
    payableDetails,
    handleRefresh,
    setDownloadModal,
    isLoading,
    handlePagination,
    activePage,
    activeTab,
    setActiveTab,
    handleDropdownList,
    selectedTimeline,
    handleSelectDropdown,
    handleClear,
    handleApplySort,
    openDropdown,
    setOpenDropdown,
    handleSearch,
    searchTerm,
    detailedLoading,
    invoiceInfo,
    customerDetails,
    downloadModal,
    handleDownload,
  };

  if (isResponsive) {
    return (
      <LoadingWrapper loading={isLoading} minHeight={true}>
        <div className={style.mobileViewContainer}>
          <div className={style.backIcon}>
            <span onClickCapture={() => navigate('/reportsMenu')}>
              <BackIcon />
            </span>
          </div>
          <div className={style.rightContentWrapper}>
            <RenderOverallContent {...commonProps} />
          </div>
          <div className={style.downloadBtnWrapper}>
            <button className={style.downloadBtn} onClick={() => setDownloadModal(true)}>
              <DownloadIcon /> Download Report
            </button>
          </div>
          <ReportSortFilter
            selectedTimeline={selectedTimeline}
            handleSelectDropdown={handleSelectDropdown}
            handleClear={handleClear}
            handleApplySort={handleApplySort}
            openDropdown={openDropdown}
            handleClose={handleClose}
          />
        </div>
      </LoadingWrapper>
    );
  }

  return (
    <>
      <LoadingWrapper loading={isLoading}>
        <RenderOverallContent {...commonProps} />
      </LoadingWrapper>
    </>
  );
};

export default PayableReport;
