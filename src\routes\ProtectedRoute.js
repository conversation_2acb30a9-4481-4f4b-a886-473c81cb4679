import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import ls from 'local-storage';
import { decryptData } from '../components/utils/cryptoUtils';

const ProtectedRoute = ({ children, requiredRole }) => {
  const { isAuthenticated, isLoading } = useAuth();

  const roleEncripted = ls.get('access_token')?.role;
  const role = roleEncripted && decryptData(roleEncripted);
  console.log(role, 'ProtectedRoute', isAuthenticated);

  if (isLoading) {
    return <div></div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (requiredRole && role !== requiredRole) {
    return <Navigate to="/unauthorized" />;
  }

  return children;
};

export default ProtectedRoute;
