@import '../../../../assets/scss/main.scss';

.emptyMsgTab {
  height: 35vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2em;
  align-items: center;
}

.invoiceTitleWrapper {
  display: flex;
  justify-content: space-between;
  padding: 0.6em 1em;

  p {
    margin: 0;
    font-size: 1em !important;
    width: 33.33%;
    font-weight: 900;
    color: #535862;

    // text-align: center;
    @include for_media(mobileScreen) {
      font-size: 0.9em !important;
    }
  }
}

.invoiceDate {
  text-align: center;

  @include for_media(mobileScreen) {
    text-align: start;
    width: auto !important;
  }
}

.invoiceAmount {
  text-align: end;
}

.invoiceContent {
  display: flex;
  justify-content: space-between;
  margin: 1em 0;
  padding: 1em;
  background-color: $white;
  border-radius: 10px;

  .idInfo,
  .invoiceDate,
  .invoiceAmount {
    width: 33%;

    @include for_media(mobileScreen) {
      width: 30%;
      font-size: 0.95em !important;
    }
  }

  .idInfo {
    span {
      color: #8d8d8d;
      font-size: 1.1em !important;
      @include for_media(mobileScreen) {
        font-size: 1em !important;
      }
    }
  }

  p {
    margin: 0;
    @include for_media(mobileScreen) {
      font-size: 1.1em !important;
    }
  }

  .invoiceAmount {
    text-align: end;
  }
}
