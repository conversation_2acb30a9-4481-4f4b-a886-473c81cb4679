import React from 'react';
import style from './scss/activeTabContent.module.scss';
import CustomAccordion from './CustomAccordion';
import InvoiceDetails from './InvoiceDetails';
import { EmptyBox } from '../../../assets/svgs';

const ActiveTabContent = ({ activeTab, customerDetails, invoiceInfo, reportName }) => {
  const entity = reportName === 'payable' ? 'Supplier' : 'Customer';
  const infoType = reportName === 'payable' ? 'Bill' : 'Invoice';

  const emptyContent1 = `${entity} Info are currently unavailable.`;
  const emptyContent2 = `${infoType} details are currently unavailable.`;

  const labelName = reportName === 'payable' ? 'Payable Amount' : 'Due Amount';

  if (activeTab === 'customer') {
    console.log(customerDetails?.data, 'customerDetails?.data');

    if (customerDetails?.data?.length === 0) {
      return (
        <div className={style.emptyMsgTab}>
          <EmptyBox />
          <p>{emptyContent1}</p>
        </div>
      );
    }
    return <CustomAccordion data={customerDetails?.data} />;
  }

  if (invoiceInfo?.data?.length === 0) {
    return (
      <div className={style.emptyMsgTab}>
        <EmptyBox />
        <p>{emptyContent2}</p>
      </div>
    );
  }

  return <InvoiceDetails invoiceInfo={invoiceInfo} labelName={labelName} />;
};

export default ActiveTabContent;
