import React from 'react';
import { useNavigate } from 'react-router-dom';
import { BackIcon } from '../../assets/svgs';

function BackBtn({ className, text = '' }) {
  const navigate = useNavigate();
  return (
    <div
      className={`flex items-center gap-2 w-fit p-1 cursor-pointer hover:bg-gray-200 transition duration-300 rounded-full ${className}`}
      onClick={() => navigate(-1)}
    >
      <BackIcon className="w-7 h-7" />
      {text && <span className="text-base font-medium tracking-wide mr-2">{text}</span>}
    </div>
  );
}

export default BackBtn;
