import { LoadingWrapper } from '../../../global/components';
import { useServiceFetch } from '../../../global/hooks';
import ticketServices from '../../../services/ticketServices';
import AccountantManagerStats from './components/AccountantManagerStats';
import style from './accountantHome.module.scss';

import React from 'react';

function AccountantHome() {
  const { data, loading, error } = useServiceFetch(ticketServices.getTickets);

  return (
    <>
      <h5 className={style.subTitle}>Overview</h5>
      <AccountantManagerStats />
    </>
  );
}

export default AccountantHome;
