import React, { useMemo } from 'react';
import { ActiveUserIcon, GoldenTicketIcon, PendingActionIcon } from '../../../../../assets/svgs';
import style from './scss/accountantManagerStats.module.scss';
import { useAuth } from '../../../../../contexts/AuthContext';
import Card from '../../../../global/components/Card';
import { useServiceFetch } from '../../../../global/hooks';
import userServices from '../../../../services/userServices';
import { LoadingWrapper } from '../../../../global/components';
import TicketsStatusPieChart from '../../../../generic-components/TicketsStatusPieChart';
import { getStatusColor } from '../../../../utils';

function AccountantManagerStats() {
  const { isMobileScreen } = useAuth();
  const { data, loading, error } = useServiceFetch(userServices.getTicketStats);

  const ticketCountInfo = useMemo(() => {
    const statusRatios = data?.data?.status_ratios ?? [];

    return Object.entries(statusRatios).map(([key, value]) => ({
      status: key,
      value: value.count,
      color: getStatusColor(key),
    }));
  }, [data?.data?.status_ratios]);

  return (
    <div className={style.dashBoardView}>
      <div className={style.leftCardContent}>
        <Card
          title="Active Tickets"
          count={`${data?.data?.status_ratios?.Open?.count || '-'}`}
          logo={<ActiveUserIcon />}
          bgColor="transparent"
        />
        <Card
          title="Pending Tickets"
          count={`${data?.data?.status_ratios?.Pending?.count || '-'}`}
          logo={<PendingActionIcon />}
          bgColor="transparent"
        />
      </div>

      {!isMobileScreen && (
        <Card
          title="Tickets by Status"
          logo={<GoldenTicketIcon />}
          bgColor="transparent"
          className={style.cardContainer}
        >
          <LoadingWrapper
            loading={loading}
            error={error}
            isRenderEmpty={ticketCountInfo.length < 1}
            renderEmpty={{ title: 'No tickets available' }}
          >
            <div className={`${style.chartWrapper} ${ticketCountInfo.length > 0 && style.emptyChart}`}>
              <TicketsStatusPieChart ticketCountInfo={ticketCountInfo} />
              <div className={style.chartDetails}>
                {ticketCountInfo.map((item) => (
                  <div key={item.color} className={style.chartStatusInfo}>
                    <div>
                      <span style={{ backgroundColor: item?.color }} />
                      <p>{item?.status}</p>
                    </div>
                    <p>{item?.value}</p>
                  </div>
                ))}
              </div>
            </div>
          </LoadingWrapper>
        </Card>
      )}
    </div>
  );
}

export default React.memo(AccountantManagerStats);
