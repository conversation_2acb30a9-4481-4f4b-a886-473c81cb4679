import React from 'react';
import { Outlet } from 'react-router-dom';
import HorizontalTabs from './HorizontalTabs';
import { SuspenseWrapper } from '../../utils/JSXUtils';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../../contexts/AuthContext';
import { getBusinessPreferences } from '../../services/syncSettingsServices';
import LoadingWrapper from '../../global/components/LoadingWrapper';
import { getErrorMessage } from '../../utils/apiUtils';

function AutoConfigScreen() {
  const { globSelectedBusiness } = useAuth();
  const {
    data: businessPreferences,
    error,
    isLoading: businessPreferencesLoading,
    refetch,
  } = useQuery({
    queryKey: ['business-preferences', globSelectedBusiness?.business_id],
    queryFn: () => getBusinessPreferences(globSelectedBusiness?.business_id),
    refetchOnMount: true,
    retry: false,
    staleTime: 30000,
    gcTime: 30000,
  });
  const isZoho = businessPreferences?.platform_details?.platform_name?.toLowerCase() === 'zoho';
  return (
    <div className="flex flex-col h-full">
      <div className="py-2">
        <div className="mb-2 px-5">
          <h1 className="text-2xl font-bold m-0">Business Configuration</h1>
          <p className="text-gray-600 m-0 ml-1.5 text-base">Configure business settings and master data</p>
        </div>
        <HorizontalTabs />
      </div>
      <div className="flex-1 overflow-auto [scrollbar-gutter:stable]">
        <SuspenseWrapper>
          <LoadingWrapper loading={businessPreferencesLoading} error={getErrorMessage(error)}>
            <Outlet context={{ businessPreferences, businessPreferencesLoading, isZoho, refetch }} />
          </LoadingWrapper>
        </SuspenseWrapper>
      </div>
    </div>
  );
}

export default AutoConfigScreen;
