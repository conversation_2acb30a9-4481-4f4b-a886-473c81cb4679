import apiClient from './apiClient';
import { zohoUrls } from '../utils/apiurls';

export const fullRefreshZoho = (businessId) => apiClient.get(`${zohoUrls.fullRefreshZoho}?business_id=${businessId}`);

export const addZohoItems = (businessId, payload) =>
  apiClient.post(`${zohoUrls.addZohoItems}?business_id=${businessId}`, payload);

export const addZohoSupplier = (businessId, payload) =>
  apiClient.post(`${zohoUrls.addZohoSupplier}?business_id=${businessId}`, payload);

export const syncZoho = (businessId, payload) =>
  apiClient.post(`${zohoUrls.syncZoho}?business_id=${businessId}`, payload);
