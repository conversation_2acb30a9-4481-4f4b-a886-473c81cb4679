@import '../../../assets//scss/main.scss';

.userListScreen {
  height: 89vh;
  padding: 2.5em 2em;
  display: flex;
  gap: 2em;
  @include for_media(mobileScreen) {
    padding: 1em;
  }
  .navigationWrapper {
    width: 20%;
    @include for_media(mobileScreen) {
      display: none;
    }
  }
  .rightContentWrapper {
    width: 80%;
    @include for_media(mobileScreen) {
      width: 100%;
    }
  }
  .listWrapper {
    display: flex;
    gap: 1em;
    flex-wrap: wrap;
    margin-top: 2em;
    .card {
      min-width: 30%;
      @include for_media(mobileScreen) {
        width: 100%;
      }
      display: flex;
      padding: 1em;
      align-items: center;
      gap: 2em;
      background-color: white;
      border-radius: 15px;
      .avatarWrapper {
        .avatar {
          height: 45px !important;
          width: 45px !important;
          border-radius: 35px;
          span {
            color: $primaryColor;
            font-size: 1.3em;
          }
        }
      }
    }
  }
  .headerContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    h4 {
      margin: 0;
    }
    .plusIcon {
      height: 3em;
      width: 3em;
      background-color: $primaryColor;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      svg {
        width: 25px;
        height: 25px;
      }
    }
  }
}
