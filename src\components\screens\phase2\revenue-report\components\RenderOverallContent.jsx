import React from 'react';
import { Popup } from 'semantic-ui-react';
import { CalenderIcon, DropdownIcon, EmptyBox } from '../../../../../assets/svgs';
import ChartSection from './ChartSection';
import DetailedReport from './DetailedReport';
import RenderDownloadModal from '../../../../utils/reportUtils/RenderDownloadModal';
import ReportHeader from '../../../../global/components/ReportHeader';
import style from '../scss/revenueReport.module.scss';
import ReportTimelineDropdown from '../../../../global/components/ReportTimelineDropdown';
import { useAuth } from '../../../../../contexts/AuthContext';

const RenderOverallContent = ({
  revenueDetails,
  handleRefresh,
  setDownloadModal,
  handleDropdownList,
  selectedTimeline,
  popupRef,
  handleSelectDropdown,
  setActiveInput,
  activeInput,
  dateInfo,
  handleDateSelection,
  handleCloseDateFilter,
  openDropdown,
  isLoading,
  handlePagination,
  activePage,
  detailedViewLoading,
  trendLineDetails,
  detailedReportData,
  handleDetailsPagination,
  activeDetailsPage,
  handleClose,
  handleApplyFilter,
  downloadModal,
  getDateRange,
  handleDownload,
  setDateInfo,
}) => {
  const { isMobileScreen } = useAuth();

  if (revenueDetails?.data?.length === 0) {
    return (
      <div className={style.emptyMsg}>
        <EmptyBox />
        <p>No data available for revenue reports at this time</p>
      </div>
    );
  }

  return (
    <>
      <ReportHeader
        title="Revenue Report"
        data={revenueDetails}
        handleRefresh={handleRefresh}
        setDownloadModal={setDownloadModal}
      />
      <div className={style.dropdownWrapper}>
        <Popup
          className={style.popup}
          trigger={
            <div className={style.dropDown} onClick={() => handleDropdownList()}>
              <p>
                <CalenderIcon />
                {selectedTimeline?.text}
              </p>
              <DropdownIcon />
            </div>
          }
          content={
            <>
              <ReportTimelineDropdown
                popupRef={popupRef}
                handleSelectDropdown={handleSelectDropdown}
                selectedTimeline={selectedTimeline}
                setActiveInput={setActiveInput}
                activeInput={activeInput}
                dateInfo={dateInfo}
                handleDateSelection={handleDateSelection}
                handleCloseDateFilter={handleCloseDateFilter}
                openDropdown={openDropdown}
                handleClose={handleClose}
                handleApplyFilter={handleApplyFilter}
                setDateInfo={setDateInfo}
              />
            </>
          }
          position="bottom left"
          on="click"
          hoverable
          basic
          open={!isMobileScreen && openDropdown}
          closeOnDocumentClick={false}
        />
      </div>
      <div className={style.contentWrapper}>
        {revenueDetails?.data?.length > 0 ? (
          <>
            <ChartSection
              isLoading={isLoading}
              revenueDetails={revenueDetails}
              trendLineDetails={trendLineDetails}
              handlePagination={handlePagination}
              activePage={activePage}
              selectedTimeline={selectedTimeline}
            />
            <DetailedReport
              detailedViewLoading={detailedViewLoading}
              detailedReportData={detailedReportData}
              handleDetailsPagination={handleDetailsPagination}
              activeDetailsPage={activeDetailsPage}
              revenueDetails={revenueDetails}
            />
          </>
        ) : (
          <div className={style.emptyMsg}>
            <EmptyBox />
            <p>No data available for revenue reports at this time</p>
          </div>
        )}
      </div>

      {isMobileScreen && (
        <ReportTimelineDropdown
          popupRef={popupRef}
          handleSelectDropdown={handleSelectDropdown}
          selectedTimeline={selectedTimeline}
          setActiveInput={setActiveInput}
          activeInput={activeInput}
          dateInfo={dateInfo}
          handleDateSelection={handleDateSelection}
          handleCloseDateFilter={handleCloseDateFilter}
          openDropdown={openDropdown}
          handleClose={handleClose}
          handleApplyFilter={handleApplyFilter}
          setDateInfo={setDateInfo}
        />
      )}
      <RenderDownloadModal
        isOpen={downloadModal}
        onClose={() => setDownloadModal(false)}
        content={`Your Revenue Report for the time period ${getDateRange(selectedTimeline?.value)?.startDate} to ${
          getDateRange(selectedTimeline?.value)?.endDate
        } is ready to download.`}
        downloadFunction={handleDownload}
      />
    </>
  );
};

export default RenderOverallContent;
