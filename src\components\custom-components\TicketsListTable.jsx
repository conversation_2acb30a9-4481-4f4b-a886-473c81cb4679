import React, { useCallback, useMemo, useState } from 'react';
import { Table } from 'semantic-ui-react';
import style from './scss/ticketsListTable.module.scss';
import {
  convertDateTimeFormat,
  formatRelativeTime,
  getPlatformIcon,
  getStatusClass,
  jsonKeyFormatLabel,
} from '../utils';
import { MsgIcon, FlagIcon } from '../../assets/svgs';

import { useNavigate } from 'react-router-dom';
import TicketsListCards from '../custom-components/TicketsListCards';
import { useAuth } from '../../contexts/AuthContext';
import SlicedText from '../generic-components/SlicedText';
import HorizontalScrollControls from './HorizontalScrollControls';
import TicketStatusChangeBtn from './TicketStatusChangeBtn';
import { Tooltip } from 'react-tooltip';
const defaultColumns = ['subject', 'category', 'sub_category', 'id', 'updated_at'];

function getFormattedValue(ticket, columnKey, handleStatusChange, roleType) {
  switch (columnKey) {
    case 'category':
      return ticket.category?.name || '--';

    case 'sub_category':
      return ticket.category?.sub_category || '--';

    case 'assignee':
      return ticket?.assign_to?.user_full_name || '--';

    case 'organization':
      return ticket?.business?.business_name || '--';

    case 'reuqested_by':
      return ticket?.requested_by?.user_full_name || '--';

    case 'status':
      return (
        <>
          <p id={`tooltip-clickable-${ticket?.id}`} className={`${style.status} ${getStatusClass(ticket?.status)}`}>
            {ticket?.status}
          </p>
          {roleType !== 'user' && (
            <Tooltip className={style.tooltip} anchorSelect={`#tooltip-clickable-${ticket?.id}`} clickable>
              <TicketStatusChangeBtn
                initialStatus={ticket?.status}
                ticketId={ticket?.id}
                onStatusChange={handleStatusChange}
              />
            </Tooltip>
          )}
        </>
      );

    case 'priority':
      return ticket?.priority === 'High priority' ? (
        <p className={style.flag}>
          <FlagIcon />
        </p>
      ) : (
        ticket?.priority || '--'
      );

    case 'date':
      return convertDateTimeFormat(ticket?.created_at);

    case 'updated_at':
      return formatRelativeTime(ticket[columnKey]);

    default:
      return ticket[columnKey] || '--';
  }
}

const TicketsListTable = ({
  data = [],
  isShowHeaderRow = true,
  isShowStickyCell = false,
  isShowViewButton = true,
  ...columnVisibility
}) => {
  const navigate = useNavigate();
  const { isMobileScreen, roleType } = useAuth();
  const [tickets, setTickets] = useState(data);

  const handleStatusChange = useCallback((ticketId, newStatus) => {
    setTickets((prevTickets) =>
      prevTickets.map((ticket) => (ticket.id === ticketId ? { ...ticket, status: newStatus } : ticket))
    );
  }, []);

  const visibleColumns = useMemo(
    () => [...defaultColumns, ...Object.keys(columnVisibility).filter((key) => columnVisibility[key])],
    [columnVisibility]
  );

  const handleRedirectComment = useCallback((ticket) => {
    const businessId = ticket?.business?.business_id;
    const ticketId = ticket?.id;
    if (businessId && ticketId) {
      navigate(`/ticket-view/${ticketId}`);
    }
  }, []);

  if (isMobileScreen) {
    return <TicketsListCards tickets={tickets} />;
  }

  return (
    <Table basic="very" className={style.ticketTable}>
      {isShowHeaderRow && (
        <Table.Header className={style.tableHeaderRow}>
          <Table.Row>
            {visibleColumns.map((columnKey) => (
              <Table.HeaderCell key={columnKey} className={columnKey}>
                {jsonKeyFormatLabel(columnKey)}
              </Table.HeaderCell>
            ))}
            <Table.HeaderCell className={style.stickyCell}>
              <HorizontalScrollControls containerId="table-container" />
            </Table.HeaderCell>
          </Table.Row>
        </Table.Header>
      )}

      <Table.Body>
        {tickets.map((ticket) => (
          <Table.Row key={ticket.id} className={style.tableRow} onClick={() => handleRedirectComment(ticket)}>
            {visibleColumns.map((columnKey) => {
              if (columnKey === 'subject') {
                return (
                  <Table.Cell key={columnKey} className={style.subject}>
                    <div className={style.subjectWrapper}>
                      {getPlatformIcon(ticket)}
                      <SlicedText text={ticket?.subject} />
                    </div>
                  </Table.Cell>
                );
              }

              return (
                <Table.Cell key={columnKey}>
                  <div
                    className={
                      columnKey === 'category'
                        ? ticket.category?.name
                          ? style.category
                          : style.empty
                        : columnKey === 'sub_category'
                          ? style.empty
                          : ''
                    }
                  >
                    {getFormattedValue(ticket, columnKey, handleStatusChange, roleType)}
                  </div>
                </Table.Cell>
              );
            })}

            <Table.Cell className={isShowStickyCell && style.stickyCell}>
              {isShowViewButton && (
                <div className={style.messageWrapper} onClick={() => handleRedirectComment(ticket)}>
                  <div className={style.msgWrapper}>
                    <MsgIcon /> View
                    {ticket.comments?.length > 0 && <span className={style.count}>{ticket.comments.length}</span>}
                  </div>
                </div>
              )}
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table>
  );
};

export default TicketsListTable;
