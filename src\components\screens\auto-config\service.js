import apiClient from '../../services/apiClient';
import { autoConfigUrls } from '../../utils/apiurls';

export const getTaxInitialValues = (businessId) => {
  return apiClient.get(`${autoConfigUrls.getTaxInitialValues}?business_id=${businessId}`);
};

export const saveTaxes = (businessId, payload) => {
  return apiClient.patch(`${autoConfigUrls.getTaxInitialValues}?business_id=${businessId}`, payload);
};
