import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useHotkeys } from 'react-hotkeys-hook';
import { toast } from 'react-toastify';
import { getInvoices } from '../../services/invoiceServices';
import { useAuth } from '../../../contexts/AuthContext';

function useAiExtractionNavigation() {
  const navigate = useNavigate();
  const { globSelectedBusiness } = useAuth();
  const { fileId, businessId, invoicePage } = useParams();
  const [page, setPage] = useState(invoicePage);
  const prevRemainCall = useRef(null);
  const [reachedStatus, setReachedStatus] = useState({
    reachedTop: true,
    reachedEnd: true,
  });
  const [didNotFound, setDidNotFound] = useState(true);
  const { data, isSuccess, isFetching } = useQuery({
    queryKey: ['invoices', page],
    queryFn: () => getInvoices(globSelectedBusiness?.business_id, page),
    enabled: !!globSelectedBusiness?.business_id && !!page,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  const currentInvoiceIndex = useMemo(() => {
    const index = data?.results?.findIndex(
      (invoice) => invoice.file_id === fileId && invoice.business_id === businessId
    );
    if (index === -1) {
      setDidNotFound(true);
      return -1;
    }
    if (didNotFound) setDidNotFound(false);
    setReachedStatus({
      reachedTop: data?.previous === null && index === 0,
      reachedEnd: data?.next === null && index === data?.results?.length - 1,
    });

    return index;
  }, [data, fileId, businessId]);

  useEffect(() => {
    if (prevRemainCall.current && isSuccess) {
      if (prevRemainCall.current === 'handlePrevious') {
        handlePrevious(null, null, data?.results?.length - 1);
      } else {
        handleNext(null, null, 0);
      }
      prevRemainCall.current = null;
    }
  }, [data, isSuccess]);

  const handlePrevious = (_, __, forceIndex = null) => {
    if (reachedStatus.reachedTop || isFetching || didNotFound) return;
    if (currentInvoiceIndex === 0) {
      setPage(Number(page) - 1);
      prevRemainCall.current = 'handlePrevious';
      return;
    }
    const index = forceIndex ?? currentInvoiceIndex - 1;
    const prevFileId = data?.results[index]?.file_id;
    const prevBusinessId = data?.results[index]?.business_id;
    if (!prevFileId || !prevBusinessId) return;
    navigate(`/ai-extraction/f/${prevFileId}/b/${prevBusinessId}/p/${page}`);
  };

  const handleNext = (_, __, forceIndex = null) => {
    if (reachedStatus.reachedEnd || isFetching || didNotFound) return;
    if (currentInvoiceIndex === data?.results?.length - 1) {
      setPage(Number(page) + 1);
      prevRemainCall.current = 'handleNext';
      return;
    }
    const index = forceIndex ?? currentInvoiceIndex + 1;
    const nextFileId = data?.results[index]?.file_id;
    const nextBusinessId = data?.results[index]?.business_id;
    if (!nextFileId || !nextBusinessId) return;
    navigate(`/ai-extraction/f/${nextFileId}/b/${nextBusinessId}/p/${page}`);
  };

  useHotkeys('alt+left', (e) => {
    e.preventDefault();
    if (reachedStatus.reachedTop || isFetching || didNotFound) {
      toast.info('You have reached the beginning of the list');
      return;
    }
    handlePrevious();
  }, { enableOnFormTags: true });

  useHotkeys('alt+right', (e) => {
    e.preventDefault();
    if (reachedStatus.reachedEnd || isFetching || didNotFound) {
      toast.info('You have reached the end of the list');
      return;
    }
    handleNext();
  }, { enableOnFormTags: true });

  return { handlePrevious, handleNext, reachedStatus, isFetching, didNotFound };
}

export default useAiExtractionNavigation;
