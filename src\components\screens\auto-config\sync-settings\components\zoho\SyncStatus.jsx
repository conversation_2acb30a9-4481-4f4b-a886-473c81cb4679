import { useMemo } from 'react';
import { formatRelativeTime } from '../../../../../utils';
import { Typography } from '@mui/material';

const getStatusDisplay = (status) => {
  switch (status) {
    case 'completed':
      return {
        statusColor: 'bg-green-600',
        showStatus: false,
      };
    case 'failed':
      return {
        statusColor: 'bg-red-600',
        showStatus: true,
        statusLabel: 'Sync Failed',
      };
    case 'pending':
      return {
        statusColor: 'bg-yellow-600',
        showStatus: true,
        statusLabel: 'Pending',
      };
    default:
      return {
        statusColor: 'bg-yellow-600',
        showStatus: false,
      };
  }
};

function SyncStatus({ syncData }) {
  const syncItems = useMemo(
    () => [
      {
        id: 'vendors',
        name: 'Vendors',
        status: syncData?.contacts?.status,
        lastSyncedAt: syncData?.contacts?.last_synced_at,
      },
      {
        id: 'chart_of_accounts',
        name: 'Chart of Accounts',
        status: syncData?.chart_of_accounts?.status,
        lastSyncedAt: syncData?.chart_of_accounts?.last_synced_at,
      },
      {
        id: 'tax',
        name: 'Tax',
        status: syncData?.tax?.status,
        lastSyncedAt: syncData?.tax?.last_synced_at,
      },
      {
        id: 'items',
        name: 'Items',
        status: syncData?.stock_item?.status,
        lastSyncedAt: syncData?.stock_item?.last_synced_at,
      },
    ],
    [syncData]
  );

  return (
    <div className="p-2 rounded-2xl shadow-lg bg-white border border-accent1-border">
      {/* Header */}
      <div className="flex items-center gap-3 p-2 border-b border-gray-100 bg-white">
        <Typography variant="h6" className="font-semibold mb-4 text-primary-color">
          Sync Status
        </Typography>
      </div>

      {/* Last Sync Info */}
      <div className="p-2.5 border-y border-accent1-border">
        <p className="text-gray-600">
          Last Sync:{' '}
          <span className="text-gray-900">{syncData?.end_time ? formatRelativeTime(syncData.end_time) : 'Never'}</span>
        </p>
      </div>

      {/* Sync Items */}
      {syncItems.map((item, index) => {
        const statusDisplay = getStatusDisplay(item.status);
        return (
          <div
            key={item.id}
            className={`flex items-center justify-between p-3 transition-colors hover:bg-blue-50/20 ${
              index < syncItems.length - 1 ? 'border-b border-gray-100' : ''
            }`}
          >
            <div className="flex flex-col">
              <Typography variant="body1">{item.name}</Typography>
              {item.lastSyncedAt && (
                <Typography
                  variant="caption"
                  sx={{
                    color: '#6b7280',
                    fontSize: '0.8rem',
                  }}
                >
                  Last synced: {formatRelativeTime(item.lastSyncedAt)}
                </Typography>
              )}
            </div>

            <div className="flex items-center gap-3 select-none">
              {statusDisplay.showStatus && (
                <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${statusDisplay.statusColor}`}>
                  {statusDisplay.statusLabel}
                </span>
              )}
              <div className={`w-2 h-2 rounded-full ${statusDisplay.statusColor}`} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

export default SyncStatus;
