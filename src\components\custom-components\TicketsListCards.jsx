import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import SlicedText from '../generic-components/SlicedText';
import { convertDateTimeFormat, getPlatformIcon, getStatusClass } from '../utils';
import { FlagIcon, MsgIcon } from '../../assets/svgs';
import style from './scss/ticketsListCards.module.scss';

const TicketsListCards = ({ tickets = [] }) => {
  const navigate = useNavigate();

  const handlePreventDefault = useCallback((e) => {
    e.stopPropagation();
    e.preventDefault();
  }, []);

  return (
    <div className={style.cardsContainer}>
      {tickets.map((ticket) => (
        <div className={style.card} key={ticket.id}>
          <div className={style.ticketContent}>
            <div className={style.rightContent}>
              <h5 onClick={handlePreventDefault}>
                <SlicedText text={ticket.subject} />
              </h5>
              <p>{ticket.id}</p>
              <p onClick={handlePreventDefault}>
                <SlicedText text={ticket.description} />
              </p>
              <p className="date">
                By {ticket.created_by} on {convertDateTimeFormat(ticket.created_at, true)}
              </p>
            </div>
            <div className={style.leftContent}>{getPlatformIcon(ticket)}</div>
          </div>
          <div className={style.bottomContent}>
            <p className={`${style.status} ${getStatusClass(ticket.status)}`}>{ticket.status}</p>
            {ticket.priority === 'High priority' && (
              <div className={style.flag}>
                <FlagIcon />
              </div>
            )}
            <div className={style.iconWrapper} onClick={() => navigate(`/ticket-view/${ticket?.id}`)}>
              <div className={style.msgIcon}>
                <MsgIcon /> <span>View </span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TicketsListCards;
