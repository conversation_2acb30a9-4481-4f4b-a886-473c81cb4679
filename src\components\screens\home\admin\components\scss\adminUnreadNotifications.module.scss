@import '../../../../scss/notification.module.scss';

.heading {
  font-weight: 900;
  padding-bottom: 0.5em;
}

.notificationContainer {
  padding: 0 1.25rem 3rem 0;
  height: 95%;
  overflow-y: auto;
  overflow-x: hidden;
  @include for_media(mobileScreen) {
    padding: 4.5rem 1.25rem 7rem;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }
}

.content {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .desc {
    color: #717680;
  }
}
