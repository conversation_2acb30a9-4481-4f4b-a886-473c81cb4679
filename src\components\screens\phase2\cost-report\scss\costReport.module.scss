@import '../../../../../assets/scss/main.scss';

.contentWrapper {
  display: flex;
  gap: 2em;
  width: 100%;
  max-height: 100%;
  min-height: 80%;

  .chartWrapper {
    width: 50%;
    // height: 85%;
    padding: 1em;

    background-color: $white;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2.5em;

    canvas {
      max-width: 100% !important;
    }
  }
}

.dropdownWrapper {
  display: flex;
  align-items: center;
  padding-bottom: 1.5em;

  .dropDown {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 16em;
    padding: 0.5em 1em;
    border: 1px solid #9ea5d1;
    background-color: #eaecf5;
    border-radius: 10px;
    cursor: pointer;
    p {
      display: flex;
      align-items: center;
      gap: 0.8em;
      margin: 0;
    }

    svg {
      height: 25px;
      width: 25px;

      path {
        fill: $black;
      }
    }
  }
}

.mobileViewContainer::-webkit-scrollbar {
  display: none;
  /* For Chrome, Safari, and Opera */
}

.mobileViewContainer {
  background-color: #f6f8fa;
  position: absolute;
  z-index: 1000;
  height: 100vh;
  width: 100%;
  padding: 1em;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;

  .rightContentWrapper {
    width: 100%;

    .contentWrapper {
      flex-direction: column;
      padding: 1em 0;
    }

    .chartWrapper {
      width: 100%;
      background-color: unset;
      padding: 0;
    }
  }

  .backIcon {
    padding: 1em 0;

    svg {
      height: 35px;
      width: 35px;
    }
  }

  .dropDown {
    width: auto !important;

    p {
      svg {
        display: none;
      }
    }
  }

  .downloadBtnWrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1em;
    background-color: $white;
    z-index: 100;
  }

  .downloadBtn {
    background-color: #f6d659;
    color: #7e6607;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3.1em;
    gap: 1em;
    border-radius: 35px !important;
    width: 100%;
    cursor: pointer;
    -webkit-text-stroke: 0.25px;
    font-size: 1.2em !important;

    svg {
      path {
        fill: #7e6607;
      }
    }
  }
}

.emptyMsg {
  height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2em;
  align-items: center;

  @include for_media(mobileScreen) {
    text-align: center;
    height: 45vh;
  }
}

.paginationWrapper {
  display: flex;
  justify-content: center !important;
  padding: 0em !important;
}

.paginationWrapper div {
  max-width: 100% !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  scrollbar-width: none; /* Hides scrollbar in Firefox */
  -ms-overflow-style: none; /* Hides scrollbar in IE/Edge */
}

.paginationWrapper div::-webkit-scrollbar {
  display: none; /* Hides scrollbar in Chrome, Safari */
}
