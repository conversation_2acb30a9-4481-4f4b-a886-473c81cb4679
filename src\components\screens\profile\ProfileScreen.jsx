import React, { memo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { BackIcon } from '../../../assets/svgs';
import OrganizationList from './components/OrganizationList';
import OrganizationDetails from './components/OrganizationDetails';
import UserProfile from './components/UserProfile';
import style from './ProfileScreen.module.scss';
import { useAuth } from '../../../contexts/AuthContext';
import useServiceFetch from '../../global/hooks/useServiceFetch';
import userServices from '../../services/userServices';
import { resturls } from '../../utils/apiurls';
import usePagination from '../../global/hooks/usePagination';

const ProfileScreen = () => {
  const navigate = useNavigate();
  const [organization, setOrganisation] = useState(false);
  const [userList, setUserList] = useState([]);
  const { role, userInfo } = useAuth();

  const {
    data: userData,
    error: userFetchError,
    loading: userFetchLaoding,
  } = useServiceFetch(() => userServices.getProfile(role, userInfo));

  const {
    data: organisationList,
    totalItemsCount,
    currentPage,
    totalPages,
    loading,
    setPage,
  } = usePagination(role !== 'superuser' && resturls.createBusiness);

  const handleBack = () => {
    if (organization) {
      setOrganisation(false);
    } else {
      navigate(-1);
    }
  };

  const handleSwitchingScreen = (data) => {
    setOrganisation(data);
    const newUserList = [data?.business_superuser, ...(data?.business_users || [])];
    setUserList(newUserList);
  };

  return (
    <div className={style.profileScreen}>
      <div className={style.header}>
        <BackIcon onClickCapture={handleBack} />
        <p>Your Profile</p>
      </div>
      <div className={style.contentWrapper}>
        {organization ? (
          <OrganizationDetails organization={organization} userList={userList} />
        ) : (
          <>
            <UserProfile userData={userData} userInfo={userInfo} loading={userFetchLaoding} error={userFetchError} />
            {role !== 'superuser' && (
              <OrganizationList
                organisationList={organisationList}
                totalItemsCount={totalItemsCount}
                currentPage={currentPage}
                totalPages={totalPages}
                setPage={setPage}
                loading={loading}
                handleSwitchingScreen={handleSwitchingScreen}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default memo(ProfileScreen);
