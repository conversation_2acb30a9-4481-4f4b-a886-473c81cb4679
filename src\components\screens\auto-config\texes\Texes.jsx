import React from 'react';
import { Form, Formik, useFormikContext } from 'formik';
import { Button, Typography } from '@mui/material';
import DynamicField from '../../../custom-components/DynamicField';
import SearchAutocomplete from '../../../custom-components/SearchAutocomplete';
import { useOutletContext } from 'react-router-dom';
import { autoConfigUrls } from '../../../utils/apiurls';
import { useAuth } from '../../../../contexts/AuthContext';
import { useQuery } from '@tanstack/react-query';
import { getTaxInitialValues, saveTaxes } from '../service';
import { getErrorMessage } from '../../../utils/apiUtils';
import { toast } from 'react-toastify';
import LoadingWrapper from '../../../global/components/LoadingWrapper';
import { updateBusinessPreferences } from '../../../services/syncSettingsServices';

const GST_LEDGERS = [0, 5, 12, 18, 28];

export default function Texes() {
  const { isZoho, businessPreferences } = useOutletContext();
  const gstLedgerMode = businessPreferences?.gst_ledger_mode;
  const { globSelectedBusiness } = useAuth();
  const {
    data: initialConfigValues,
    isLoading,
    refetch,
    isRefetching,
  } = useQuery({
    queryKey: ['initialConsolidatedValues'],
    queryFn: () => getTaxInitialValues(globSelectedBusiness?.business_id),
  });

  const mappedInitialValues = initialConfigValues?.data?.reduce((acc, curr) => {
    if ((!curr.tax_type || !curr.tax_rate) && GST_LEDGERS.includes(curr.tax_rate)) return acc;
    acc[`${curr.tax_type.toLowerCase()}_${curr.tax_rate}`] = {
      uuid_id: curr.uuid ?? '',
      tax_name: curr.name ?? '',
    };
    return acc;
  }, {});

  const initialValues = {
    gst_ledger_mode: gstLedgerMode?.value ?? '',
    cess_ledger: '',
    ...mappedInitialValues,
  };

  const handleSubmit = (values) => {
    let payload = { data: [] };

    updateBusinessPreferences(globSelectedBusiness?.business_id, { gst_ledger_mode: values.gst_ledger_mode })
      .then(() => {
        Object.entries(values).forEach(([key, value]) => {
          const [gstType, gstRate] = key.split('_');
          const isNoChange = value?.uuid_id === mappedInitialValues[key]?.uuid_id;
          if (isNoChange || key === 'cess_ledger' || key === 'gst_ledger_mode' || !value?.uuid_id) return;
          payload.data.push({
            uuid: value?.uuid_id,
            tax_type: gstType,
            tax_rate: gstRate,
          });
        });
        return saveTaxes(globSelectedBusiness?.business_id, payload);
      })
      .then(() => {
        refetch();
        toast.success('Taxes saved successfully');
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(errorMessage);
      });
  };
  return (
    <LoadingWrapper loading={isLoading}>
      <Formik key={isRefetching ? 'refetching' : 'stable'} initialValues={initialValues} onSubmit={handleSubmit}>
        {({ values, setFieldValue, dirty }) => (
          <Form className="flex flex-col gap-2 px-5 max-w-4xl ml-10">
            <Typography variant="h6" fontWeight="bold" className="mb-5">
              Tax Configuration
            </Typography>

            <DynamicField
              Component={SearchAutocomplete}
              label="GST Ledger Mode (for Purchases)"
              onSelect={(value) => setFieldValue('gst_ledger_mode', value)}
              value={values.gst_ledger_mode}
              optionLabel="value"
              optionValue="value"
              config={gstLedgerMode}
            />
            {!isZoho && (
              <DynamicField
                Component={SearchAutocomplete}
                label="Cess Ledger"
                onSelect={(value) => setFieldValue('cess_ledger', value)}
                value={values.cess_ledger}
                optionValue="uuid_id"
                optionLabel="tax_name"
              />
            )}

            <div className={`grid gap-2 ${isZoho ? 'grid-cols-2' : 'grid-cols-3'}`}>
              {isZoho ? <ZohoTaxes /> : <TallyTaxes key={values.gst_ledger_mode} />}
            </div>

            <Button variant="contained" className="w-fit" type="submit" disabled={!dirty}>
              Save
            </Button>
          </Form>
        )}
      </Formik>
    </LoadingWrapper>
  );
}

function TallyTaxes() {
  const { values, setFieldValue } = useFormikContext();
  const { globSelectedBusiness } = useAuth();
  const businessId = globSelectedBusiness?.business_id;

  const isConsolidated = values.gst_ledger_mode === 'consolidated';
  const handleConsolidatedChange = (gstType, value, gstRate) => {
    if (isConsolidated && gstRate === 0) {
      GST_LEDGERS.forEach((rate) => {
        setFieldValue(`${gstType}_${rate}`, value);
      });
    } else {
      setFieldValue(`${gstType}_${gstRate}`, value);
    }
  };

  return (
    <>
      {/* IGST */}
      <div className="flex flex-col">
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          IGST
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <DynamicField
            key={`igst_${gst}`}
            Component={SearchAutocomplete}
            label={`${gst}%`}
            onSelect={(value, option) => handleConsolidatedChange('igst', option, gst)}
            value={values[`igst_${gst}`]}
            disabled={isConsolidated && gst !== 0}
            optionValue="uuid_id"
            optionLabel="tax_name"
            url={`${autoConfigUrls.getTaxes}?business_id=${businessId}`}
          />
        ))}
      </div>

      {/* CGST */}
      <div className="flex flex-col">
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          CGST
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <DynamicField
            key={`cgst_${gst}`}
            Component={SearchAutocomplete}
            label={`${gst}%`}
            onSelect={(value, option) => handleConsolidatedChange('cgst', option, gst)}
            value={values[`cgst_${gst}`]}
            disabled={isConsolidated && gst !== 0}
            optionValue="uuid_id"
            optionLabel="tax_name"
            url={`${autoConfigUrls.getTaxes}?business_id=${businessId}`}
          />
        ))}
      </div>

      <div className="flex flex-col">
        {/* SGST */}
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          SGST
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <DynamicField
            key={`sgst_${gst}`}
            Component={SearchAutocomplete}
            label={`${gst}%`}
            onSelect={(value, option) => handleConsolidatedChange('sgst', option, gst)}
            value={values[`sgst_${gst}`]}
            disabled={isConsolidated && gst !== 0}
            optionValue="uuid_id"
            optionLabel="tax_name"
            url={`${autoConfigUrls.getTaxes}?business_id=${businessId}`}
          />
        ))}
      </div>
    </>
  );
}

function ZohoTaxes() {
  const { globSelectedBusiness } = useAuth();
  const businessId = globSelectedBusiness?.business_id;
  const { values, setFieldValue } = useFormikContext();
  return (
    <>
      {/* Inter */}
      <div className="flex flex-col gap-1">
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          Inter State
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <DynamicField
            key={`inter_${gst}`}
            Component={SearchAutocomplete}
            label={`${gst}%`}
            onSelect={(value, option) => setFieldValue(`gst_${gst}`, option)}
            value={values[`gst_${gst}`]}
            optionValue="uuid_id"
            optionLabel="tax_name"
            url={`${autoConfigUrls.getTaxes}?business_id=${businessId}`}
          />
        ))}
      </div>

      {/* Intra */}
      <div className="flex flex-col gap-1">
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          Intra State
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <DynamicField
            key={`intra_${gst}`}
            Component={SearchAutocomplete}
            label={`${gst}%`}
            onSelect={(value, option) => setFieldValue(`igst_${gst}`, option)}
            value={values[`igst_${gst}`]}
            optionValue="uuid_id"
            optionLabel="tax_name"
            url={`${autoConfigUrls.getTaxes}?business_id=${businessId}`}
          />
        ))}
      </div>
    </>
  );
}
