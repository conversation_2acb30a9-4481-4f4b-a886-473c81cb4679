import React, { useEffect, useMemo, useRef } from 'react';
import InvoicePreview from './components/InvoicePreview';
import AiForm from './components/AiForm';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { getExtractedInvoice } from '../../services/aiServices';
import useServiceFetch from '../../global/hooks/useServiceFetch';
import LoadingWrapper from '../../global/components/LoadingWrapper';
import { ArrowLeft } from 'lucide-react';
import ticketServices from '../../services/ticketServices';
import useAiExtractionNavigation from './useAiExtractionNavigation';
import useUpdateEffect from '../../global/hooks/useUpdateEffect';

function AiExtractionScreen() {
  const navigate = useNavigate();
  const { fileId, businessId } = useParams();
  const isFirstRender = useRef(true);

  const {
    handlePrevious,
    handleNext,
    reachedStatus,
    isFetching: fetchingInvoices,
    didNotFound,
  } = useAiExtractionNavigation();

  const { data, loading: categoryLoading } = useServiceFetch(ticketServices.getCategories);
  const { categoryOptions } = useMemo(() => {
    return (
      data?.results?.reduce(
        (acc, item) => {
          if (item?.name?.toLowerCase() === 'purchase' || item?.name?.toLowerCase() === 'expense') {
            acc.categoryOptions.push({
              key: item?.id,
              value: item?.id,
              label: item?.name,
            });

            const invoiceSubCat = item?.subcaregories?.filter((item) => item?.name?.toLowerCase() === 'invoice');
            acc.subCategoryOptions.push({
              key: invoiceSubCat?.id,
              value: invoiceSubCat?.id,
              label: invoiceSubCat?.name,
            });
          }
          return acc;
        },
        {
          categoryOptions: [],
          subCategoryOptions: [],
        }
      ) || { categoryOptions: [], subCategoryOptions: [] }
    );
  }, [data]);
  const {
    data: extractedData,
    loading,
    error,
    refetch,
  } = useServiceFetch(() => getExtractedInvoice(fileId, businessId));

  // const [extractedData, setExtractedData] = useState(null);
  // const [loading, setLoading] = useState(true);
  // const [error, setError] = useState(null);
  // useEffect(() => {
  //   const staticJson = "/extracted-data.json";
  //   fetch(staticJson)
  //     .then((response) => response.json())
  //     .then((res) => {
  //       setExtractedData(res);
  //     })
  //     .catch((err) => {
  //       setError(err);
  //     })
  //     .finally(() => {
  //       setLoading(false);
  //     });
  // }, []);

  useEffect(() => {
    if (isFirstRender.current && extractedData?.ticket_id) {
      isFirstRender.current = false;
    }
  }, [extractedData?.ticket_id]);

  useUpdateEffect(() => {
    refetch(false);
  }, [fileId, businessId]);

  if (window.innerWidth < 1024) {
    return (
      <div className="flex flex-col text-center items-center justify-center h-screen">
        <p>For an optimal comparison experience, please use a large-screen device such as a laptop.</p>
        <p
          className="group flex items-center hover:text-blue-500 hover:underline cursor-pointer select-none"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="group-hover:fill-blue-500 cursor-pointer" /> Go back{' '}
        </p>
      </div>
    );
  }
  return (
    <div className="absolute inset-0 top-20 flex items-start justify-between gap-2 overflow-hidden">
      {/* Left Side */}
      <div className="w-[30%] min-w-[30%] max-w-[30%] p-2 flex flex-col h-full overflow-hidden">
        <div className="relative bg-white rounded-lg p-4 shadow-lg flex-1 overflow-hidden">
          <InvoicePreview
            loading={(loading || categoryLoading || fetchingInvoices) && isFirstRender.current}
            fileUrl={extractedData?.file_url}
          />
        </div>
      </div>

      {/* Right Side */}
      <div id="ai-extraction-form-container" className="w-full flex-1 overflow-y-auto h-full">
        <LoadingWrapper useBlankSkeleton={true} loading={loading || categoryLoading || fetchingInvoices} error={error}>
          <AiForm
            extractedData={extractedData}
            refetch={refetch}
            categoryOptions={categoryOptions}
            isLoading={loading || categoryLoading || fetchingInvoices}
            handlePrevious={handlePrevious}
            handleNext={handleNext}
            reachedStatus={reachedStatus}
            didNotFound={didNotFound}
          />
        </LoadingWrapper>
      </div>
    </div>
  );
}

export default AiExtractionScreen;
