/* Primary Font */

@font-face {
  font-family: 'Montserrat';
  src:
    local('Montserrat'),
    url('../fonts/Montserrat.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}

@font-face {
  font-family: 'KyivType Sans';
  src:
    local('KyivType_Sans'),
    url('../fonts/KyivType_Sans.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src:
    local('Inter'),
    url('../fonts/Inter.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: 'Orbitron';
  src:
    local('Orbitron'),
    url('../fonts/Orbitron-Regular.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: 'Poppins';
  src:
    local('Poppins'),
    url('../fonts/Poppins-Regular.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: 'agencyfb';
  src:
    local('agencyfb'),
    url('../fonts/agencyfb_reg.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: 'Manrope';
  src:
    local('Manrope'),
    url('../fonts/Manrope-Regular.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: 'DMSans';
  src:
    local('DMSans'),
    url('../fonts/DMSans-Regular.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}
