import { useCallback } from 'react';
import ShieldOutlinedIcon from '@mui/icons-material/ShieldOutlined';
import { MenuItem, Select } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { useOutletContext } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useAuth } from '../../../../../contexts/AuthContext';
import { getErrorMessage } from '../../../../utils/apiUtils';
import { getAccountingPlatforms, updateBusinessPreferences } from '../../../../services/syncSettingsServices';
import ZohoSyncButton from './zoho/ZohoSyncBtn';

function AccountingPlatform({
  selectedPlatform,
  setSelectedPlatform,
  isConnected,
  refetchSyncStatus,
  syncProgress,
  didSyncFailed,
}) {
  const { globSelectedBusiness } = useAuth();
  const { refetch } = useOutletContext();
  const { data: platforms, isLoading } = useQuery({
    queryKey: ['accounting-platforms'],
    queryFn: getAccountingPlatforms,
  });

  const handleChangeAccountingPlatform = useCallback(
    (e) => {
      const selectedPlatformId = e.target.value;
      const selectedPlatformName = platforms?.results?.find(
        (platform) => platform.id === selectedPlatformId
      )?.platform_name;
      updateBusinessPreferences(globSelectedBusiness?.business_id, {
        platform: selectedPlatformId,
      })
        .then(() => {
          setSelectedPlatform({
            id: selectedPlatformId,
            platform_name: selectedPlatformName,
          });
          refetch();
          toast.success(`Accounting platform changed to ${selectedPlatformName}`);
        })
        .catch((error) => {
          toast.error(getErrorMessage(error));
        });
    },
    [globSelectedBusiness?.business_id, platforms?.results, refetch, setSelectedPlatform]
  );

  return (
    <div className="mb-4 p-6 rounded-2xl shadow-lg bg-white border border-accent1-border">
      <div className="flex items-center gap-2">
        <div className="flex items-center justify-center w-6 h-6">
          <ShieldOutlinedIcon className="text-accent1" />
        </div>
        <label className="text-xl font-bold leading-none text-primary-color">Accounting Platform</label>
      </div>
      <div className="flex items-end justify-between h-20">
        <Select
          value={selectedPlatform?.id || ''}
          onChange={handleChangeAccountingPlatform}
          className="!rounded-lg w-64"
          disabled={isLoading}
          size="small"
          displayEmpty
          renderValue={(value) => {
            if (!value) return 'Select Platform';
            return selectedPlatform?.platform_name || 'Select Platform';
          }}
        >
          {Array.isArray(platforms?.results) &&
            platforms?.results?.length > 0 &&
            platforms?.results?.map((platform) => (
              <MenuItem key={platform.id} value={platform.id}>
                {platform.platform_name}
              </MenuItem>
            ))}
        </Select>
        {selectedPlatform?.platform_name?.toLowerCase() === 'zoho' && (
          <ZohoSyncButton
            isConnected={isConnected}
            refetchSyncStatus={refetchSyncStatus}
            syncProgress={syncProgress}
            didSyncFailed={didSyncFailed}
          />
        )}
      </div>
    </div>
  );
}

export default AccountingPlatform;
