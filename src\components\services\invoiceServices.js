import { resturls } from '../utils/apiurls';
import apiClient from './apiClient';

export const downloadInvoices = (businessId, invoiceIds) => {
  const url = `${resturls.downloadInvoices}?business_id=${businessId}`;
  return apiClient.post(url, { file_ids: invoiceIds });
};

export const getInvoices = (businessId, page = 1) => {
  const url = `${resturls.extractedInvoiceRootDomain}?business_id=${businessId}&page=${page}`;
  return apiClient.get(url);
};

export const getInvoiceStatuses = (businessId, fileIds) => {
  const url = `${resturls.getInvoiceStatuses}?business_id=${businessId}`;
  return apiClient.post(url, { file_ids: fileIds });
};
