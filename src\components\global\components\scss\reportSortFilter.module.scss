@import '../../../../assets/scss/main.scss';

.popupContainer {
  padding: 0 0.5em !important;
  // width: 20em !important;
  border-radius: 15px !important;

  .dropDownItem {
    margin: 1em 0;
    cursor: pointer;

    .option {
      display: flex;
      align-items: center;
      gap: 1em;
    }
  }

  .customRadio {
    height: 22px;
    width: 22px;
    margin: 0;
    border-radius: 100%;
    border: 1px solid #4e5ba6;
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      height: 100%;
      width: 100%;
      display: none;
    }
  }

  .selectedItem {
    .customRadio {
      border: none;

      svg {
        display: block;
      }
    }
  }

  .dateContainer {
    display: flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 0;

    .dateInput {
      width: 50%;

      input {
        color: $black !important;

        svg {
          display: none !important;
        }
      }
    }
  }
}

.filterBtnWrapper {
  display: flex;
  gap: 0.5em;

  @include for_media(mobileScreen) {
    padding: 2em 1em 0em 1em;
  }

  button {
    width: 7.5em !important;
    height: 3em !important;
    border-radius: 35px !important;

    @include for_media(mobileScreen) {
      width: 48% !important;
      height: 3.3em !important;
      font-size: 1.15em !important;
    }
  }

  .clearBtn {
    border: 1px solid #f9e699;
    background-color: #fdf7dd !important;
    color: #7e6607 !important;
  }

  .applybtn {
    background-color: #f6d659 !important;
    color: #7e6607 !important;
  }

  .applybtn:hover {
    background-color: #e7c746 !important;
  }
}

.applyBtn {
  display: flex;
  justify-content: center;

  button {
    background-color: #f6d659;
    color: #7e6607;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3.1em;
    gap: 1em;
    margin-top: 10px;
    border-radius: 35px !important;
    cursor: pointer;
    -webkit-text-stroke: 0.25px;
    font-size: 1.2em !important;

    svg {
      path {
        fill: #7e6607;
      }
    }
  }
}

.logListPopup {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  width: 100%;
  gap: 1em;
  box-shadow: 0 0 1px 1px #d7d7d7;
  transform: translateY(150%);
  transition: all 1s ease;
  padding: 1em 0 2em 0;
  position: fixed;
  bottom: 0;
  left: 0;
  border-radius: 30px 30px 0 0;
  z-index: 100;

  p {
    margin: 0;
  }

  h4 {
    font-size: 1.5em !important;
  }
}

.openDropdown {
  // height: 50dvh;
  transform: translateY(0%);
  z-index: 1000;
  // visibility: visible;
}

.logItem {
  display: flex;
  align-items: center;
  gap: 2em;
}

.listCloseIcon {
  position: absolute;
  background-color: white;
  padding: 1.3em;
  border-radius: 35px;
  right: -23px;
  top: -97px;

  svg path {
    fill: $black !important;
  }

  @include for_media(mobileScreen) {
    right: 10px;
    top: -77px;
  }
}

@include for_media(mobileScreen) {
  .modalContent {
    border-radius: 20px !important;
  }

  .popupContainer {
    padding: 0 1em !important;
  }

  .applyBtn {
    padding: 1em 1em 0 1em !important;
  }

  .modalContent {
    h4 {
      padding: 0.5em 1em;
    }
  }
}
