import React, { useState, useEffect, useCallback } from 'react';
import { AnimatePresence, motion } from 'motion/react';
import { Mail } from 'lucide-react';
import { Tooltip } from 'react-tooltip';
import useServiceFetch from '../hooks/useServiceFetch';
import { syncMail } from '../../services/ticketServices';
import { useAuth } from '../../../contexts/AuthContext';
import { toast } from 'react-toastify';

function SyncMailBtn() {
  const { globSelectedBusiness } = useAuth();
  const isDisabled = !globSelectedBusiness;
  const [showSuccess, setShowSuccess] = useState(false);

  const { loading, execute } = useServiceFetch(
    () => syncMail(globSelectedBusiness?.business_id),
    false,
    'Failed to sync mail',
    false
  );

  useEffect(() => {
    let timer;
    if (showSuccess || loading) {
      timer = setTimeout(() => {
        setShowSuccess(false);
      }, 2000);
    }
    return () => clearTimeout(timer);
  }, [showSuccess, loading]);

  const handleClick = useCallback(async () => {
    if (isDisabled || loading || showSuccess) return;

    try {
      await execute();
      setShowSuccess(true);
    } catch (error) {
      toast.error('Failed to start mail syncing. Please try again.');
    }
  }, [isDisabled, loading, showSuccess]);

  return (
    <>
      <div
        id="sync-mail-btn"
        className={`flex justify-center items-center h-10 mx-8 text-base bg-accent2 text-white font-black rounded-md cursor-pointer px-8 text-nowrap transition-all duration-300 ${
          isDisabled
            ? 'bg-gray-300 border-2 border-gray-400 text-gray-500 cursor-not-allowed opacity-70'
            : 'hover:bg-accent2-hover'
        }
        `}
        onClick={handleClick}
      >
        <AnimatePresence mode="wait" initial={false}>
          <motion.span
            key={showSuccess}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1, ease: 'easeOut' }}
            className="flex items-center justify-center select-none"
          >
            <Mail size={16} className="mr-2" />
            {showSuccess ? 'Syncing started...' : 'Sync Mail'}
          </motion.span>
        </AnimatePresence>
      </div>

      {isDisabled && (
        <Tooltip
          anchorSelect={`#sync-mail-btn`}
          place="bottom"
          style={{
            padding: '8px',
            maxWidth: '80%',
          }}
        >
          Please select business from Invoices page
        </Tooltip>
      )}
    </>
  );
}

export default SyncMailBtn;
