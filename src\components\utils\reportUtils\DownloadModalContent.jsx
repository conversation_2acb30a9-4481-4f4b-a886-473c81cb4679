import React, { useState } from 'react';
import { Checkbox } from 'semantic-ui-react';
import style from './scss/DownloadModal.module.scss';
import { DownloadReportIcon } from '../../../assets/svgs';

/**
 * Download Modal Content Component
 *
 * This function returns a reusable modal content for downloading reports.
 *
 * @param {string} content - The text content to display in the modal.
 * @param {Function} downloadFunction - The function to be called when clicking the "Save Download" button.
 *
 * @returns {JSX.Element} - A JSX element representing the download modal.
 */

const DownloadModalContent = ({ content, downloadFunction, checkBoxDisable }) => {
  const [transaction, setTransaction] = useState(false);

  return (
    <div className={`${style.downloadModal} ${style.modalContent}`}>
      <div className={style.fileContent}>
        <DownloadReportIcon />
        <h4>Download Report</h4>
        <p>{content}</p>
      </div>
      {!checkBoxDisable && (
        <div className={style.checkBoxWrapper} onClickCapture={() => setTransaction((prev) => !prev)}>
          <Checkbox checked={transaction} onClick={() => setTransaction((prev) => !prev)} />
          <p>Download with Transactions</p>
        </div>
      )}
      <hr />
      <div className={style.applyBtn}>
        <button className={style.downloadBtn} onClick={() => downloadFunction(transaction)}>
          Save Download
        </button>
      </div>
    </div>
  );
};

export default DownloadModalContent;
