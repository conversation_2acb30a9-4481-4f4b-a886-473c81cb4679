import React from 'react';
import { Pagination } from 'semantic-ui-react';

const PaginationContent = ({ activePage, totalPages, pageChangeFunction }) => {
  if (totalPages <= 1) return;
  return (
    <Pagination
      activePage={activePage}
      firstItem={null}
      lastItem={null}
      siblingRange={0}
      totalPages={totalPages}
      style={{ height: '3.5em' }}
      defaultActivePage={activePage}
      onPageChange={(e, { activePage }) => pageChangeFunction(activePage)}
    />
  );
};

export default PaginationContent;
