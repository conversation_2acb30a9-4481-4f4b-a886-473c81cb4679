import { useAuth } from '../../../../../contexts/AuthContext';
import BarChart from '../../../../global/Charts/BarChart';
import { LoadingWrapper } from '../../../../global/components';
import { isToday } from '../../../../utils/dateUtils';
import style from '../scss/revenueReport.module.scss';

const ChartSection = ({
  isLoading,
  revenueDetails,
  selectedTimeline,
  trendLineDetails,
  handlePagination,
  activePage,
}) => {
  const today = new Date();
  const chartKey = JSON.stringify(revenueDetails);

  const granularity = revenueDetails?.metadata?.granularity;

  const labels = revenueDetails?.data
    ?.sort((a, b) => new Date(a.label) - new Date(b.label)) // Sort from newest to oldest
    ?.map((item) => {
      const label = item?.label;
      const parsedDate = new Date(label);
      const today = new Date();

      // Check if it's a valid date
      if (isNaN(parsedDate)) {
        return label; // Return as is if not a valid date
      }

      const day = parsedDate.getDate();
      const month = parsedDate.toLocaleString('en-US', { month: 'short' });
      const year = parsedDate.getFullYear();
      // const isCurrentYear = year === today.getFullYear();
      // const isCustomDate = selectedTimeline?.value === "customDate";

      if (selectedTimeline?.value === 'yearToDate' || granularity === 'month') {
        return `${month} ${year}`;
      }

      if (parsedDate.toDateString() === today.toDateString()) {
        return 'Today';
      }

      return `${day} ${month}`;
    });

  const trendlineLabels = (trendLineDetails?.data ?? [])
    .sort((a, b) => new Date(a.label) - new Date(b.label))
    ?.map((item) => {
      const label = item?.label;
      if (!label) return '';

      const parsedDate = new Date(label);
      if (isNaN(parsedDate.getTime())) return label;

      const day = parsedDate.getDate();
      const month = parsedDate.toLocaleString('en-US', { month: 'short' });
      const year = parsedDate.getFullYear();

      return year === today.getFullYear() ? `${day} ${month}` : `${day} ${month} ${year}`;
    });

  const trendlineData = trendLineDetails?.data
    ?.sort((a, b) => new Date(a.label) - new Date(b.label)) // Sort from current (newest) to old
    ?.map((item) => item.amount);

  const barData = revenueDetails?.data
    ?.sort((a, b) => new Date(a.label) - new Date(b.label)) // Sort from current (newest) to old
    ?.map((item) => item.amount);

  const barColors = revenueDetails?.data?.map(
    (info) =>
      isToday(info?.label)
        ? ['#7683CE', '#0D1752'] // Mobile gradient for today
        : ['#C5CDED', '#C5CDED'] // Mobile gradient for other days
  );
  return (
    <LoadingWrapper loading={isLoading} minHeight={true}>
      <div className={style.chartWrapper}>
        <BarChart
          labels={labels}
          key={chartKey}
          barData={barData}
          trendlineData={trendlineData}
          trendlineLabels={trendlineLabels}
          pagination={revenueDetails?.pagination}
          handlePagination={handlePagination}
          activePage={activePage}
          barColors={barColors}
          barObj={{
            gradientAddColorStart: 'rgba(38, 51, 126, 1)',
            gradientAddColorStop: 'rgba(78, 91, 166, 1)',
            currency: '₹',
            barThickness: 40,
            borderRadius: { topLeft: 4, topRight: 4 },
            order: 2,
          }}
          disableTrendline={selectedTimeline?.value === 'customDate'}
          trendlineObj={{
            label: 'Trendline',
            borderColor: 'rgba(247, 144, 9, 1)',
          }}
        />
      </div>
    </LoadingWrapper>
  );
};

export default ChartSection;
