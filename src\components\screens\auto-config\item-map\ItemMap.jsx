import React, { useState } from 'react';
import DataTable from '../../../custom-components/DataTable';
import { useAuth } from '../../../../contexts/AuthContext';
import DownloadBtn from '../../../custom-components/DownloadBtn';
import UploadBtn from '../../../custom-components/UploadBtn';
import { useOutletContext } from 'react-router-dom';
import { FormControlLabel, Radio, RadioGroup, Typography } from '@mui/material';
import { autoConfigUrls } from '../../../utils/apiurls';

const COLUMNS = [
  {
    field: 'item_name',
    headerName: 'Item Name',
  },
  {
    field: 'vendor_name',
    headerName: 'Vendor',
  },
  {
    field: 'ledger_name',
    headerName: 'Ledger Name',
  },
  {
    field: 'hsn_sac',
    headerName: 'HSN / SAC',
  },
  {
    field: 'gst_rate',
    headerName: 'GST Rate',
  },
  {
    field: 'gst_eligibility',
    headerName: 'GST Eligibility',
  },
];

function ItemMap() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences } = useOutletContext();
  const [viewType, setViewType] = useState('stock-items');

  const handleViewTypeChange = (event) => {
    setViewType(event.target.value);
  };
  const isStockItem = viewType === 'stock-items';
  const getItemMapUrl = `${isStockItem ? autoConfigUrls.getStockItemMapping : autoConfigUrls.getNonStockItems}?business_id=${globSelectedBusiness?.business_id}`;
  const downloadUrl = `${isStockItem ? autoConfigUrls.downloadStockItemMapping : autoConfigUrls.downloadNonStockItems}?business_id=${globSelectedBusiness?.business_id}`;
  const uploadUrl = `${isStockItem ? autoConfigUrls.uploadStockItemMapping : autoConfigUrls.uploadNonStockItems}?business_id=${globSelectedBusiness?.business_id}`;
  return (
    <DataTable
      title={isStockItem ? 'Mapping of Items to Ledgers' : 'Mapping of Non Stock Items to Ledgers'}
      url={getItemMapUrl}
      columns={COLUMNS}
    >
      <DataTable.BeforeSearch>
        <div className="flex items-center gap-2">
          <Typography variant="p">Select View Type: </Typography>
          <RadioGroup row name="view-type" value={viewType} onChange={handleViewTypeChange}>
            <FormControlLabel value="stock-items" control={<Radio />} label="Stock Items" />
            <FormControlLabel value="non-stock" control={<Radio />} label="Non Stock" />
          </RadioGroup>
        </div>
      </DataTable.BeforeSearch>
      {!businessPreferences?.enable_auto_sync_master?.value && (
        <>
          <DownloadBtn className="!rounded-md" downloadUrl={downloadUrl} />
          <UploadBtn
            className="!rounded-md"
            uploadUrl={uploadUrl}
            title={`Upload ${isStockItem ? '' : 'Non Stock'} Item Mapping`}
          />
        </>
      )}
    </DataTable>
  );
}

export default ItemMap;
