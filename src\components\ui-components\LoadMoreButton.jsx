import React from 'react';
import { Button } from 'semantic-ui-react';
import PropTypes from 'prop-types';
import style from './scss/laodMoreButton.module.scss';

const LoadMoreButton = ({ onClick, loading, disabled }) => {
  return (
    <div className={style.loadMoreBtnWrapper}>
      <Button
        type="button"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onClick();
        }}
        loading={loading}
        disabled={disabled}
      >
        Load more
      </Button>
    </div>
  );
};

LoadMoreButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
  disabled: PropTypes.bool.isRequired,
};

export default LoadMoreButton;
