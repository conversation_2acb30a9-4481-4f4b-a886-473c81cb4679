import React, { useState } from 'react';
import { Modal, Icon } from 'semantic-ui-react';
import { toast } from 'react-toastify';
import style from './scss/resetPasswordModal.module.scss';
import userServices from '../../services/userServices';

const ResetPasswordModal = ({ open, onClose }) => {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (newPassword !== confirmPassword) {
      return setError('Passwords do not match');
    }

    if (newPassword.length < 6) {
      return setError('Password should be at least 6 characters');
    }
    setLoading(true);

    userServices
      .resetPassword({
        old_password: currentPassword,
        new_password: newPassword,
      })
      .then(() => {
        setLoading(false);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
        onClose();
        toast.success('Password reset successfully');
      })
      .catch((err) => {
        setError(err?.message || 'Failed to reset password');
        setLoading(false);
      });
  };

  return (
    <Modal open={open} onClose={onClose} size="tiny" className={style.resetPasswordModal} dimmer="blurring">
      <Modal.Header>Reset Password</Modal.Header>
      <Modal.Content>
        <form onSubmit={handleSubmit}>
          {error && <div className={style.error}>{error}</div>}
          <div className={style.inputGroup}>
            <label>Current Password</label>
            <div className={style.passwordInput}>
              <input
                type={showCurrentPassword ? 'text' : 'password'}
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                required
              />
              <Icon
                name={showCurrentPassword ? 'eye slash' : 'eye'}
                className={style.eyeIcon}
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              />
            </div>
          </div>
          <div className={style.inputGroup}>
            <label>New Password</label>
            <div className={style.passwordInput}>
              <input
                type={showNewPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
              />
              <Icon
                name={showNewPassword ? 'eye slash' : 'eye'}
                className={style.eyeIcon}
                onClick={() => setShowNewPassword(!showNewPassword)}
              />
            </div>
          </div>
          <div className={style.inputGroup}>
            <label>Confirm New Password</label>
            <div className={style.passwordInput}>
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
              <Icon
                name={showConfirmPassword ? 'eye slash' : 'eye'}
                className={style.eyeIcon}
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              />
            </div>
          </div>
          <div className={style.buttons}>
            <button
              type="button"
              onClick={() => {
                setCurrentPassword('');
                setNewPassword('');
                setConfirmPassword('');
                setError('');
                onClose();
              }}
              className={style.cancelBtn}
            >
              Cancel
            </button>
            <button type="submit" className={style.submitBtn} disabled={loading}>
              {loading ? 'Resetting...' : 'Reset Password'}
            </button>
          </div>
        </form>
      </Modal.Content>
    </Modal>
  );
};

export default ResetPasswordModal;
