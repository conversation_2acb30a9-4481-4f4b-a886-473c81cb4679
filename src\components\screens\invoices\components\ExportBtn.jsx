import React from 'react';
import { Icon } from 'semantic-ui-react';
import { Tooltip } from 'react-tooltip';
import { motion, AnimatePresence } from 'motion/react';

const ExportBtn = ({
  onClick = null,
  count = 0,
  hasFilters = false,
  className = '',
  isDisabled = false,
  disableMessage = 'Export is disabled',
}) => {
  const buttonId = `export-btn-${Math.random().toString(36).substring(2, 11)}`;

  const handleClick = (e) => {
    if (isDisabled) return;
    if (onClick) onClick(e);
  };

  return (
    <>
      <div
        id={isDisabled ? buttonId : undefined}
        className={`
          flex items-center justify-center flex-shrink-0 py-[0.9em] px-[0.8em] rounded-[1em]
          whitespace-nowrap select-none transition-colors duration-200
          ${
            isDisabled
              ? 'bg-gray-300 border-2 border-gray-400 text-gray-500 cursor-not-allowed opacity-70'
              : hasFilters
                ? 'bg-[#011638] border-2 border-white text-white cursor-pointer'
                : 'common-btn-schema'
          } 
          ${className}
        `}
        onClick={handleClick}
      >
        <Icon
          name="download"
          className={`${isDisabled ? 'text-gray-500' : hasFilters ? 'text-white' : ''} mr-2 text-[14px]`}
        />
        <span className="flex flex-shrink-0 items-center">
          Export (
          <span className="inline-block relative h-[1.2em] overflow-hidden min-w-[1em] text-center">
            <AnimatePresence mode="wait" initial={false}>
              <motion.span
                key={count}
                initial={{ y: -15, opacity: 0.5 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: 15, opacity: 0.5 }}
                transition={{ duration: 0.15, ease: 'easeOut' }}
                className="inline-block"
              >
                {count}
              </motion.span>
            </AnimatePresence>
          </span>
          )
        </span>
      </div>

      {isDisabled && disableMessage && (
        <Tooltip
          anchorSelect={`#${buttonId}`}
          place="bottom"
          style={{
            backgroundColor: '#011638',
            borderRadius: '15px',
            zIndex: 1000,
            fontSize: '14px',
            fontWeight: 500,
            padding: '8px 12px',
          }}
        >
          {disableMessage}
        </Tooltip>
      )}
    </>
  );
};

export default ExportBtn;
