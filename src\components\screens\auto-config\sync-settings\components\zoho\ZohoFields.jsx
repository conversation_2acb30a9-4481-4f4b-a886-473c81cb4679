import { useState } from 'react';
import { Typo<PERSON>, Switch, IconButton, Select, TextField, InputAdornment } from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../../../utils/apiUtils';
import { useAuth } from '../../../../../../contexts/AuthContext';
import { updateBusinessPreferences } from '../../../../../services/syncSettingsServices';
import { useOutletContext } from 'react-router-dom';
import DynamicField from '../../../../../custom-components/DynamicField';
import ZohoAuthBtn from './ZohoAuthBtn';

const regions = ['in'];

const syncTimes = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
];

const getSettingsJsonKey = (key) => {
  switch (key) {
    case 'autoSyncMasters':
      return 'enable_auto_sync_master';
    case 'autoSyncValidation':
      return 'enable_auto_sync_invoice';
    case 'dailySyncTime':
      return 'sync_time';
    default:
      return key;
  }
};

function ZohoFields({ setIsConnected, isConnected, zohoConfigs, syncData }) {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences, refetch } = useOutletContext();

  // Authentication state
  const [zohoRegion, setZohoRegion] = useState(zohoConfigs?.region || 'in');
  const [organizationId, setOrganizationId] = useState(zohoConfigs?.organization_id || '');
  const [copied, setCopied] = useState(false);

  // Sync settings state
  const [MISettings, setMISettings] = useState({
    autoSyncMasters: businessPreferences?.enable_auto_sync_master?.value,
    autoSyncValidation: businessPreferences?.enable_auto_sync_invoice?.value,
    dailySyncTime: businessPreferences?.preferred_sync_time?.value,
  });

  const handleChangeMISettings = (e, key) => {
    const jsonKey = getSettingsJsonKey(key);
    const value = key === 'dailySyncTime' ? e.target.value : e.target.checked;
    updateBusinessPreferences(globSelectedBusiness?.business_id, {
      [jsonKey]: value,
    })
      .then(() => {
        setMISettings((prev) => ({
          ...prev,
          [key]: value,
        }));
        refetch();
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(`Failed to update settings: ${errorMessage}`);
      });
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(organizationId);
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  return (
    <div className="flex justify-between min-w-[55%] gap-16 p-5 rounded-2xl shadow-lg bg-white border border-accent1-border">
      <div className="flex flex-col gap-4">
        <Typography variant="h6" className="font-semibold mb-4 text-primary-color border-b pb-2">
          Synchronization Settings
        </Typography>
        <div className="flex flex-col gap-4">
          <DynamicField
            config={businessPreferences?.enable_auto_sync_master}
            control={
              <Switch
                checked={MISettings.autoSyncMasters}
                onChange={(e) => handleChangeMISettings(e, 'autoSyncMasters')}
                color="primary"
                disabled={!isConnected}
              />
            }
            label="Auto Sync Master"
            disabled={!isConnected}
            required={false}
          />

          <DynamicField
            control={
              <Switch
                checked={MISettings.autoSyncValidation}
                onChange={(e) => handleChangeMISettings(e, 'autoSyncValidation')}
                color="primary"
                disabled={!isConnected}
              />
            }
            config={businessPreferences?.enable_auto_sync_invoice}
            label="Auto Sync On Validation"
            disabled={!isConnected}
            required={false}
          />

          <DynamicField
            Component={Select}
            options={syncTimes.map((time) => ({ label: time, value: time }))}
            label="Daily Sync Time"
            disabled={!isConnected}
            value={MISettings.dailySyncTime}
            onChange={(e) => handleChangeMISettings(e, 'dailySyncTime')}
            size="small"
            config={businessPreferences?.preferred_sync_time}
            required={false}
          />
        </div>
      </div>

      <div className="flex flex-col gap-4 flex-1">
        <Typography variant="h6" className="font-semibold mb-4 text-primary-color border-b pb-2">
          Authentication
        </Typography>

        <div className="flex flex-col gap-4">
          <DynamicField
            Component={TextField}
            label="Organization ID"
            value={organizationId}
            onChange={(e) => setOrganizationId(e.target.value)}
            error={!organizationId}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton disabled={!organizationId} onClick={handleCopy}>
                      {copied ? <CheckIcon color="success" /> : <ContentCopyIcon className={`${!organizationId ? 'text-red-700' : ''}`} />}
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />

          <DynamicField
            Component={Select}
            options={regions.map((region) => ({ label: region, value: region }))}
            label="Zoho Region"
            disabled={!isConnected}
            value={zohoRegion}
            onChange={(e) => setZohoRegion(e.target.value)}
          />

          <ZohoAuthBtn
            setIsConnected={setIsConnected}
            isConnected={isConnected}
            organizationId={organizationId}
            region={zohoRegion}
          />
        </div>
      </div>
    </div>
  );
}

export default ZohoFields;
