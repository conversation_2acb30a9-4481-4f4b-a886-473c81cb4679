import {
  DialogActions,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableContainer,
  DialogContent,
  Typography,
  DialogTitle,
  Dialog,
  Table,
  Paper,
} from '@mui/material';
import { Button } from 'semantic-ui-react';
import KeyboardAltOutlinedIcon from '@mui/icons-material/KeyboardAltOutlined';

const shortcuts = [
  { key: 'Alt + →', description: 'Next invoice' },
  { key: 'Alt + ←', description: 'Previous invoice' },
];

export default function ShortcutKeysModal({ open, onClose }) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      slotProps={{
        paper: {
          sx: {
            width: '600px',
            maxWidth: '90vw',
          },
        },
      }}
    >
      <DialogTitle>
        <div className="flex items-center">
          <KeyboardAltOutlinedIcon className="mr-2" />
          <Typography variant="h6">Keyboard Shortcuts</Typography>
        </div>
      </DialogTitle>
      <DialogContent>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <strong>Shortcut</strong>
                </TableCell>
                <TableCell>
                  <strong>Description</strong>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {shortcuts.map((shortcut, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <code className="bg-gray-100 px-2 py-1 rounded">{shortcut.key}</code>
                  </TableCell>
                  <TableCell>{shortcut.description}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}
