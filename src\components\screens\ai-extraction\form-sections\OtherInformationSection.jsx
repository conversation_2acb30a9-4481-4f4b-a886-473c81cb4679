import React from 'react';
import <PERSON><PERSON>ield from './components/AiField';
import { getFieldAlertObject } from '../../../../components/utils/aiUtils';

import CustomDatePicker from '../../../ui-components/fields/CustomDatePicker';

const SECTION = 'other_information';

function OtherInformationSection({ formData, isReadOnly, formAction }) {
  const data = formData[SECTION] || {};

  return (
    <div className="grid grid-cols-3 gap-4">
      {/* Payment Terms */}
      <AiField
        label="Payment Terms (In days)"
        isExactMatch={data?.exact_match?.payment_terms}
        alertObject={getFieldAlertObject(data, 'payment_terms')}
        type="text"
        name="payment_terms"
        id="payment_terms"
        value={data?.payment_terms ?? ''}
        onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'payment_terms', e.target.value)}
        disabled={isReadOnly}
      />

      {/* E-Way Bill Details */}
      <AiField
        label="E-Way Bill Details"
        isExactMatch={data?.exact_match?.e_way_bill_details}
        alertObject={getFieldAlertObject(data, 'e_way_bill_details')}
        type="text"
        name="e_way_bill_details"
        id="e_way_bill_details"
        value={data?.e_way_bill_details ?? ''}
        onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'e_way_bill_details', e.target.value)}
        disabled={isReadOnly}
      />

      {/* E-Way Bill Date */}
      <AiField
        label="E-Way Bill Date (dd/mm/yyyy)"
        isExactMatch={data?.exact_match?.e_way_bill_date}
        alertObject={getFieldAlertObject(data, 'e_way_bill_date')}
        id="e_way_bill_date"
        renderCustomField={() => (
          <CustomDatePicker
            selected={data?.e_way_bill_date ?? ''}
            onDateChange={(date) => {
              formAction('FIELD_CHANGE', SECTION, 'e_way_bill_date', date);
            }}
            name="e_way_bill_date"
            id="e_way_bill_date"
            className="input-field"
            disabled={isReadOnly}
            portalId={'date-picker-portal'}
          />
        )}
      />
    </div>
  );
}

export default OtherInformationSection;
