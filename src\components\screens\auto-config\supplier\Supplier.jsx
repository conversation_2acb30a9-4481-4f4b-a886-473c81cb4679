import React, { useState } from 'react';
import DataTable from '../../../custom-components/DataTable';
import { useAuth } from '../../../../contexts/AuthContext';
import { useOutletContext } from 'react-router-dom';
import AddSupplierForm from '../../ai-extraction/form-sections/components/AddSupplierForm';
import { Button } from '@mui/material';
import { autoConfigUrls } from '../../../utils/apiurls';

const COLUMNS = [
  {
    field: 'ledger_name',
    headerName: 'Supplier Name',
  },
  {
    field: 'gst_type',
    headerName: 'GST Type',
  },
  {
    field: 'gst_no',
    headerName: 'GST Number',
  },
  {
    field: 'state_name',
    headerName: 'State',
    renderCell: (params) => {
      const stateCode = params?.state_code ?? '';
      const stateName = params?.state_name ?? '';
      return stateCode && stateName ? `${stateCode} - ${stateName}` : stateName || stateCode || '--';
    },
  },
  {
    field: 'address',
    headerName: 'Address',
  },
];

function Supplier() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences, isZoho } = useOutletContext();
  const [open, setOpen] = useState(false);
  const isAutoSyncMaster = businessPreferences?.enable_auto_sync_master?.value;
  return (
    <DataTable
      title="List of Suppliers"
      url={`${autoConfigUrls.getSuppliers}?business_id=${globSelectedBusiness?.business_id}`}
      columns={COLUMNS}
    >
      {isZoho && isAutoSyncMaster && (
        <>
          <Button variant="contained" className="!rounded-md" onClick={() => setOpen(true)}>
            Add Supplier
          </Button>
          <AddSupplierForm open={open} onClose={() => setOpen(false)} />
        </>
      )}
    </DataTable>
  );
}

export default Supplier;
