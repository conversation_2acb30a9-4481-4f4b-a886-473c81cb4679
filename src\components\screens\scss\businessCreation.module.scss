@import '../../../assets//scss/main.scss';

.bussinessCreateScreen {
  height: 89vh;
  padding: 2.5em 2em;
  display: flex;
  gap: 2em;
  @include for_media(mobileScreen) {
    padding: 1em;
  }
  .navigationWrapper {
    width: 20%;
    @include for_media(mobileScreen) {
      display: none;
    }
  }
  .rightContentWrapper {
    width: 80%;
    @include for_media(mobileScreen) {
      width: 100%;
    }
  }
  .formWrapper {
    padding: 2em;
    border-radius: 15px;
    background-color: $white;
    @include for_media(mobileScreen) {
      padding: 1em;
      padding-bottom: 6em;
    }
    .formContainer {
      display: flex;
      flex-wrap: wrap;
      gap: 2em;
      .formField {
        width: 48%;
      }
      @include for_media(mobileScreen) {
        gap: 1em;
        .formField {
          width: 100%;
        }
      }
      input,
      textarea {
        background-color: #f5f5f5 !important;
        color: $black !important;
        border: 1px solid #e9eaeb;
        border-radius: 10px;
      }
      input {
        height: 3.5em;
      }
      textarea {
        height: 7em;
      }
      label {
        font-size: 1.2em !important;
        margin: 1em 0 !important;
      }
    }
    .submitBtn {
      margin: 1em 0;
      display: flex;
      justify-content: flex-end;
      button {
        font-size: 1.2em;
        width: 10em;
        border-radius: 35px !important;
        background-color: $yellowColor;
        color: #7e6607;
        @include for_media(mobileScreen) {
          width: 100%;
          margin-top: 1em;
        }
      }
    }
  }
}
