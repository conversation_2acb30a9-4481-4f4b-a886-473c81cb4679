@import '../../../../../../assets/scss/main.scss';
.businessCardsWrapper {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2em;
  width: 100%;
  margin: 0;
  padding: 2em 0;

  @include for_media(mobileScreen) {
    padding: 2em 2em 3em 2em;
  }

  .card {
    width: calc(45% - 1em) !important;
    background-color: $white !important;
    border-radius: 15px !important;
    margin: 0 !important;
    -webkit-user-select: none;
    user-select: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    @include for_media(mobileScreen) {
      width: 100% !important;
    }

    @include for_media(tabletScreen) {
      width: calc(90% - 1em) !important;
    }

    @include for_media(bigTabletScreen) {
      width: calc(45% - 1em) !important;
    }

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
      transform: translateY(-2px);
    }

    .cardContent {
      display: flex !important;
      padding: 1.5em !important;
      gap: 2em;

      img {
        width: 100px;
        height: 100px;
        object-fit: cover;
        flex-shrink: 0;

        @include for_media(mobileScreen) {
          width: 80px;
          height: 80px;
        }
      }

      svg {
        width: 4em;
        height: 4em;
        fill: #363f72;
        flex-shrink: 0;
      }

      .info {
        width: 100%;
        display: flex;
        justify-content: center;
        flex-direction: column;
        position: relative;

        h5 {
          font-size: 1.2em;
          font-weight: 600 !important;
          margin: 0;
        }
      }
    }
  }

  .selectedCard {
    box-shadow:
      0 0 0 2px $primaryColor,
      0 4px 12px rgba(0, 0, 0, 0.1) !important;

    span {
      display: flex;
      align-items: center;
      color: $greenVarient2;
      font-size: 0.9em;
      margin-top: 0.5em;

      &:before {
        content: '';
        display: inline-block;
        width: 8px;
        height: 8px;
        background-color: $greenVarient2;
        border-radius: 50%;
        margin-right: 0.5em;
      }
    }
  }
}
