@import '../../../../../assets/scss/main.scss';

.accountTypeDropdown {
  min-width: 23em !important;

  .customInput {
    width: 100% !important;
  }
}

.popupContainer {
  padding: 0 0.5em !important;
  // width: 20em !important;
  border-radius: 15px !important;

  .dropDownItem {
    margin: 1em 0;
    cursor: pointer;

    .option {
      display: flex;
      align-items: center;
      gap: 1em;

      p {
        width: 85%;
      }
    }
  }

  .customRadio {
    height: 22px;
    width: 22px;
    margin: 0;
    border-radius: 100%;
    border: 1px solid #4e5ba6;
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      height: 100%;
      width: 100%;
      display: none;
    }
  }

  .customCheckBox {
    height: 25px;
    width: 25px;
    margin: 0;

    svg {
      height: 100%;
      width: 100%;
    }
  }

  .selectedItem {
    .customRadio {
      border: none;

      svg {
        display: block;
      }
    }

    .customCheckBox {
      svg {
        rect {
          fill: #4e5ba6;
        }
      }
    }
  }
}

.activeApplyBtn {
  background-color: #f9e699 !important;
  color: #7e6607 !important;
}

.customInput {
  display: flex;
  align-items: center;
  gap: 1em;
  width: 80%;
  height: 3.7em;
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 13px;
  border: 1px solid #d5d7da;

  svg {
    width: 25px;
    height: 25px;
  }

  input {
    width: 85%;
    border: none !important;
    height: 2.5em;
    color: black !important;
    background-color: unset !important;
  }

  input:focus {
    outline: none !important;
    border: none !important;
  }

  input::placeholder {
    font-size: 1.2em !important;
  }

  @include for_media(mobileScreen) {
    width: 100%;
  }
}

.filterPagination {
  display: flex;
  justify-content: center;
  padding: 1em 0;
}

.btnSection {
  display: flex;
  width: 100%;
  gap: 1em;
  justify-content: space-between;
  padding-top: 1em;
  border-top: 1px solid rgb(233, 233, 233);

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5em;
    width: 48%;
    border-radius: 35px;
    font-size: 1.2em !important;
    cursor: pointer;
  }

  .clrBtn {
    background-color: #fdf7dd;
    border: 1px solid #f9e699;
    color: #7e6607;
  }

  .applyBtn2 {
    background-color: #d5d7da;
    color: $black;
  }
}

.emptyFilterSearch {
  padding: 1em;
  text-align: center;
  font-size: 1.15em !important;
  color: #717680;
}

.logListPopup {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  width: 100%;
  min-height: 81vh;
  gap: 1em;
  box-shadow: 0 0 1px 1px #d7d7d7;
  transform: translateY(150%);
  transition: all 1s ease;
  padding: 1em 0 2em 0;
  position: fixed;
  bottom: 0;
  left: 0;
  border-radius: 30px 30px 0 0;

  p {
    margin: 0;
  }

  h4 {
    font-size: 1.5em !important;
  }
}

.openDropdown {
  // height: 50dvh;
  transform: translateY(0%);
  z-index: 1000;
  // visibility: visible;
}

.logItem {
  display: flex;
  align-items: center;
  gap: 2em;
}

.listCloseIcon {
  position: absolute;
  background-color: white;
  padding: 1.3em;
  border-radius: 35px;
  right: -23px;
  top: -97px;

  svg path {
    fill: $black !important;
  }

  @include for_media(mobileScreen) {
    right: 10px;
    top: -77px;
  }
}

@include for_media(mobileScreen) {
  .modalContent {
    border-radius: 20px !important;
  }

  .popupContainer {
    padding: 1em !important;
    height: 71vh;
    overflow-y: auto;
  }

  .applyBtn {
    padding: 1em 1em 0 1em !important;
  }

  .modalContent {
    h4 {
      padding: 0.5em 1em;
    }
  }
}
