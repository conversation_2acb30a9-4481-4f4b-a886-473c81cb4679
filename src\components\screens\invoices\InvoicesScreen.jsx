import React, { useEffect, useState, useCallback } from 'react';
import style from './invoicesScreen.module.scss';
import ExportBtn from './components/ExportBtn';
import { resturls } from '../../utils/apiurls';
import LoadingWrapper from '../../global/components/LoadingWrapper';
import { CircleCheck } from 'lucide-react';
import InputSearch from '../../ui-components/InputSearch';
import { useAuth } from '../../../contexts/AuthContext';
import apiClient from '../../services/apiClient';
import { toast } from 'react-toastify';
import { extractFilenameFromHeaders, getErrorMessage } from '../../utils/apiUtils';
import InvoiceTable from './components/InvoiceTable';
import InvoicesFilterMenu from './components/InvoicesFilterMenu';
import usePaginationWithSearch from '../../global/hooks/usePaginationWithSearch';
import { AnimatePresence, motion } from 'motion/react';
import axios from 'axios';
import ls from 'local-storage';
import { restbaseurl } from '../../utils/constants';

function InvoicesScreen() {
  const { isMobileScreen, globSelectedBusiness } = useAuth();
  const [selectedInvoices, setSelectedInvoices] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [keepLoading, setKeepLoading] = useState(true);
  const [invoiceDateSort, setInvoiceDateSort] = useState('');

  const {
    data: invoices,
    setData: setInvoices,
    loading,
    error,
    PaginationComponent,
    refetch,
    query,
    setQuery,
    setExtraParams,
  } = usePaginationWithSearch({
    url: `${resturls.extractedInvoiceRootDomain}?business_id=${globSelectedBusiness?.business_id}&invoice_date_sort=${invoiceDateSort}`,
    pageCount: 50,
    shouldDoInitialFetch: false,
    queryParam: 'search',
  });

  // Handle status updates from polling and update local invoices
  const handleStatusUpdate = useCallback((newInvoiceMap) => {
    setInvoices((prev) => {
      if (!Array.isArray(prev)) return prev;

      return prev.map((invoice) => {
        const newInvoice = newInvoiceMap[invoice.file_id];
        return newInvoice !== undefined ? { ...invoice, ...newInvoice } : invoice;
      });
    });
  }, []);

  useEffect(() => {
    if (globSelectedBusiness?.business_id) {
      refetch(false);
    }
  }, [globSelectedBusiness?.business_id]);

  useEffect(() => {
    if (loading) {
      setKeepLoading(false);
    }
  }, [loading]);

  const handleExport = () => {
    if (!selectedInvoices?.length) return;
    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${ls.get('access_token')?.data}`,
    };
    const body = {
      file_ids: selectedInvoices,
    };
    axios
      .post(`${restbaseurl}${resturls.exportInvoice}?business_id=${globSelectedBusiness?.business_id}`, body, {
        headers,
        responseType: 'blob',
      })
      .then((res) => {
        const blob = new Blob([res?.data], {
          type: 'application/octet-stream',
        });
        const filename = extractFilenameFromHeaders(res.headers) || 'invoices_export';
        const downloadLink = document.createElement('a');
        const blobUrl = URL.createObjectURL(blob);
        downloadLink.href = blobUrl;
        downloadLink.download = filename;
        downloadLink.click();
        downloadLink.remove();
        URL.revokeObjectURL(blobUrl);
        refetch();
      })
      .catch((err) => {
        toast.error(getErrorMessage(err));
      });
  };

  const handleMarkAsCompleted = () => {
    apiClient
      .patch(`${resturls.changeInvoiceStatus}?business_id=${globSelectedBusiness?.business_id}`, {
        file_ids: selectedInvoices,
        status: 5,
      })
      .then(() => {
        setSelectedInvoices([]);
        refetch();
      })
      .catch((err) => {
        toast.error(getErrorMessage(err));
      });
  };

  return (
    <div className="p-4 pb-0 bg-[#f6f8fa]">
      <div className={style.actionBtns}>
        <div className="flex flex-wrap md:flex-nowrap gap-2">
          <InvoicesFilterMenu
            setSelectedOptions={setSelectedOptions}
            selectedOptions={selectedOptions}
            onApply={(params) => setExtraParams(params)}
          />
          <InputSearch
            wrapperClasses={style.searchWrapper}
            inputClasses={style.searchInputField}
            placeholder="Search by Invoice Number"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
        </div>
        {!isMobileScreen && (
          <div className="flex flex-wrap flex-shrink-0 items-center gap-2">
            <AnimatePresence>
              {selectedInvoices.length > 0 && (
                <motion.div
                  key="mark-as-completed"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  onClick={handleMarkAsCompleted}
                >
                  <div className="flex items-center gap-2 flex-shrink-0 rounded-[0.7em] p-[0.6em] font-normal shadow-none w-fit cursor-pointer common-btn-schema">
                    <CircleCheck />
                    <span className="text-nowrap select-none">Mark as Completed</span>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            <ExportBtn
              onClick={handleExport}
              count={selectedInvoices?.length ?? 0}
              isDisabled={!globSelectedBusiness}
              disableMessage="Please select business"
            />
          </div>
        )}
      </div>
      <LoadingWrapper loading={loading || keepLoading} error={error}>
        {globSelectedBusiness ? (
          <div className={style.tableContainer} id="invoices-table-container">
            <InvoiceTable
              data={invoices}
              selectedInvoices={selectedInvoices}
              setSelectedInvoices={setSelectedInvoices}
              invoiceDateSort={invoiceDateSort}
              setInvoiceDateSort={setInvoiceDateSort}
              onStatusUpdate={handleStatusUpdate}
            />
          </div>
        ) : (
          <div className="text-lg absolute top-1/3 left-1/2 -translate-x-1/2 -translate-y-1/2 text-red-600">
            Kindly select a business from the dropdown
          </div>
        )}
        <div className={style.paginationWrapper}>{PaginationComponent}</div>
      </LoadingWrapper>
    </div>
  );
}

export default InvoicesScreen;
