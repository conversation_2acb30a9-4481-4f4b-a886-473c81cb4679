import React from 'react';
import style from './scss/ticketInfo.module.scss';
import TicketStatusChangeBtn from '../../../custom-components/TicketStatusChangeBtn';
import TicketPriorityChangeBtn from '../../../custom-components/TicketPriorityChangeBtn';
import { useAuth } from '../../../../contexts/AuthContext';

function TicketInfo({ ticket }) {
  const { roleType } = useAuth();
  return (
    <div className={style.ticketInfoWrapper}>
      <div className={style.ticketInfoTitle}>
        <h4>{ticket?.subject}</h4>
        <TicketStatusChangeBtn initialStatus={ticket?.status} ticketId={ticket?.id} />
      </div>
      <div className={style.commentTicketInfo}>
        <p>#{ticket?.id}</p>
        {ticket?.category && roleType === 'user' && <p className={style.category}>{ticket?.category?.name}</p>}
        {roleType === 'user' && <TicketPriorityChangeBtn ticketId={ticket?.id} initialPriority={ticket?.priority} />}
      </div>
    </div>
  );
}

export default React.memo(TicketInfo);
