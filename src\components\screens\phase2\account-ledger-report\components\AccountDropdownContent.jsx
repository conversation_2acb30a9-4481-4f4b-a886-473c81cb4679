import React, { useEffect, useState } from 'react';
import { CheckBoxIcon, CloseIcon, SearchIcon } from '../../../../../assets/svgs';
import style from '../scss/accountDropdownContent.module.scss';
import PaginationContent from '../../../../utils/reportUtils/PaginationContent';
import { useAuth } from '../../../../../contexts/AuthContext';

const AccountDropdownContent = ({
  accountPopupRef,
  handleSearch,
  searchTerm,
  accountTypeOptions,
  selectedAccountType,
  handleAccountDropdown,
  setAccountTypeDropdown,
  handleAccountDropdownApply,
  activeFilterPage,
  handleClearAccountFilter,
  accountTypeDropdown,
  handleAccountFilterPagination,
}) => {
  const { isMobileScreen } = useAuth();

  const [previousList, setPreviosList] = useState([]);
  const isDifferent = JSON.stringify(previousList) !== JSON.stringify(selectedAccountType);

  const handleApply = () => {
    if (isDifferent) {
      handleAccountDropdownApply();
      setPreviosList(selectedAccountType);
    }
  };

  useEffect(() => {
    setPreviosList(selectedAccountType);
  }, []);

  const renderContent = () => (
    <div
      className={`${style.accountTypeDropdown} ${style.popupContainer}`}
      ref={accountPopupRef}
      onClick={(e) => e.stopPropagation()}
    >
      <div className={style.customInput}>
        <SearchIcon />
        <input type="text" placeholder="Search Ledger Name..." onChange={handleSearch} value={searchTerm} />
      </div>
      {accountTypeOptions?.data?.length > 0 ? (
        <>
          {accountTypeOptions?.data?.map((list) => {
            const isSelected = selectedAccountType.some((item) => item.value === list.value);
            const selectAllValue = `all${activeFilterPage}`;

            if (list.value === selectAllValue) {
              // Ensure "Select All" is checked only if all items are selected
              const currentPageItems = accountTypeOptions?.data?.slice(1, 11) || [];
              const isAllChecked = currentPageItems.every((item) =>
                selectedAccountType.some((selected) => selected.value === item.value)
              );

              return (
                <div
                  key={list.value}
                  className={`${style.dropDownItem} ${isAllChecked ? style.selectedItem : ''}`}
                  onClickCapture={() => handleAccountDropdown(list)}
                >
                  <div className={style.option}>
                    <span className={`${style.customCheckBox}`}>
                      <CheckBoxIcon />
                    </span>
                    <p>{list?.text}</p>
                  </div>
                </div>
              );
            }

            return (
              <div
                key={list.value}
                className={`${style.dropDownItem} ${isSelected ? style.selectedItem : ''}`}
                onClickCapture={() => handleAccountDropdown(list)}
              >
                <div className={style.option}>
                  <span className={`${style.customCheckBox}`}>
                    <CheckBoxIcon />
                  </span>
                  <p>{list?.text}</p>
                </div>
              </div>
            );
          })}

          <div className={style.filterPagination}>
            <PaginationContent
              activePage={activeFilterPage}
              totalPages={accountTypeOptions?.pagination?.total_pages}
              pageChangeFunction={handleAccountFilterPagination}
            />
          </div>
          <div className={style.btnSection}>
            <button className={style.clrBtn} onClick={handleClearAccountFilter}>
              Clear all
            </button>
            <button className={`${isDifferent ? style.activeApplyBtn : ''} ${style.applyBtn2}`} onClick={handleApply}>
              Apply
            </button>
          </div>
        </>
      ) : (
        <div className={style.emptyFilterSearch}>No matches found</div>
      )}
    </div>
  );

  if (isMobileScreen) {
    return (
      <div className={isMobileScreen && accountTypeDropdown ? 'dimmer' : ''}>
        <div className={`${accountTypeDropdown ? style.openDropdown : ''} ${style.logListPopup}`}>
          <div className={style.listCloseIcon} onClickCapture={() => setAccountTypeDropdown(false)}>
            <CloseIcon />
          </div>
          <div className={style.modalContent}>
            <div>
              <h4>Select Account/Ledger</h4>
            </div>
            <hr />
            {renderContent()}
          </div>
        </div>
      </div>
    );
  }
  return renderContent();
};

export default AccountDropdownContent;
