import React from 'react';
import { Image } from 'semantic-ui-react';
import Avatar from 'react-avatar';
import { OrganizationIcon, MapIcon } from '../../../../assets/svgs';
import style from './scss/OrganizationDetails.module.scss';

const OrganizationDetails = ({ organization, userList }) => {
  return (
    <>
      <div className={style.detailWrapper}>
        <div className={style.detailsContainer}>
          <div className={style.profileWrapper}>
            <div className={style.orgLogo}>
              {organization?.image ? <Image src={organization?.image} /> : <OrganizationIcon />}
            </div>
            <h4>{organization?.name}</h4>
          </div>
          <hr />
          <div className={style.details}>
            <ItemWrapper label="Address" className={style.fullWidth}>
              {organization?.address}
              <div className={style.mapIcon}>
                <a target="_blank" rel="noreferrer noopener" href={organization?.google_map_link}>
                  <MapIcon />
                </a>
              </div>
            </ItemWrapper>
            <ItemWrapper label="Industry Type">{organization?.business_type?.name || '-'}</ItemWrapper>
            <ItemWrapper label="GST Number">{organization?.gst}</ItemWrapper>
            <ItemWrapper label="Account manager">{organization?.account_manager?.user_full_name}</ItemWrapper>
            <ItemWrapper label="Accountant email">{organization?.account_manager?.user_email}</ItemWrapper>
            <ItemWrapper label="Whatsapp">{organization?.account_manager?.whatsapp || '-'}</ItemWrapper>
          </div>
        </div>
      </div>
      <div className={style.userList}>
        <p>Organization Members{` (${userList?.length})`}</p>
        {userList?.length > 0 &&
          userList?.map((user) => (
            <div className={style.userItem} key={user.user_id}>
              <Avatar className={style.avatarProfile} color="#EAECF5" name={user?.user_full_name} />
              <div className={style.info}>
                <p>{user?.user_full_name}</p>
                <p>#{user?.user_id}</p>
              </div>
              {user?.user_role === 'business_superuser' && (
                <div className={style.superUser}>
                  <p>Superuser</p>
                </div>
              )}
            </div>
          ))}
      </div>
    </>
  );
};

const ItemWrapper = React.memo(({ label, className, children }) => {
  return (
    <div className={`${style.item} ${className}`}>
      <div className={style.label}>
        <span>{label}</span>
      </div>
      <div className={style.value}>{children}</div>
    </div>
  );
});

export default OrganizationDetails;
