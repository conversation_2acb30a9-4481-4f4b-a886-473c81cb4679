@import '../../../../../assets/scss/main.scss';
@import './UserProfile.module.scss';

.orgLogo {
  padding: 1em;
  box-shadow: 0 0 1px 2px #9ea5d1;
  border-radius: 100%;
  background-color: #eaecf5;
  margin-bottom: 0.5em;
  img,
  svg {
    width: 40px;
    height: 40px;
  }
}

.userList {
  display: flex;
  flex-direction: column;
  gap: 1em;
  margin: 1em;
  p {
    margin: 0;
  }
}

.userItem {
  display: flex;
  padding: 1em;
  gap: 1em;
  align-items: center;
  background-color: $white;
  border-radius: 10px;
  .avatarProfile {
    height: 3.5em !important;
    width: 3.5em !important;
    span {
      font-size: 1em !important;
    }
  }
  .info {
    p {
      margin: 0;
      display: inline-block;
    }
  }
}

.superUser {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  p {
    padding: 0.5em;
    background-color: #363f72;
    border-radius: 25px;
    color: $white;
    width: 6em;
    font-size: 1em !important;
  }
}

.details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.1em;
  width: 100%;
}

.item {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
  .label {
    font-size: 1.3em;
    font-weight: 900;
  }
  .value {
    display: flex;
    align-items: center;
    font-size: 1.2em;
    gap: 1em;
  }
}

.item.fullWidth {
  grid-column: 1 / -1;
}

.mapIcon {
  padding: 0.6em 0.3em 0.3em 0.3em;
  border-radius: 10px;
  box-shadow: 0 0 1px 2px $borderColor;
  svg {
    width: 30px;
    height: 30px;
  }
}
