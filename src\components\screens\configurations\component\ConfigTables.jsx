import { Search } from 'lucide-react';
import { capitalizeWords } from '../../../utils';

/**
 * ConfigTable component
 * @param {Array<{ header: string, accessor: string|function }>} columns - Column definitions
 * @param {Array<Object>} data - Array of data objects
 * @param {string} rowKey - Property of data to use as key for rows (default 'id')
 */
const ConfigTable = ({ columns, data, rowKey = 'id' }) => {
  return (
    <div className="overflow-x-auto max-h-[calc(100vh-300px)]">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((col) => (
              <th
                key={col.header}
                scope="col"
                className="px-6 py-3 text-left text-base font-bold text-black tracking-wider"
              >
                {capitalizeWords(col.header)}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data && data.length > 0 ? (
            data.map((row, rowIndex) => (
              <tr key={row[rowKey]} className="hover:bg-gray-50">
                {columns.map((col, colIndex) => {
                  const value = typeof col.accessor === 'function' ? col.accessor(row, rowIndex) : row[col.accessor];
                  return (
                    <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-base text-gray-900">
                      {value}
                    </td>
                  );
                })}
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={columns.length} className="py-10">
                <div className="flex flex-col items-center justify-center text-gray-900">
                  <p className="text-lg font-medium">No items found</p>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default ConfigTable;
