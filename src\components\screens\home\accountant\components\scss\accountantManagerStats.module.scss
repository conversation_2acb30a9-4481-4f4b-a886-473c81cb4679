@import '../../../../../../assets/scss/main.scss';

.dashBoardView {
  display: flex;
  padding: 1.5em;
  gap: 1em;
  .leftCardContent {
    width: 35%;
    display: flex;
    flex-direction: column;
    gap: 1em;
  }
  .rightCardContent {
    width: 65%;
    padding: 1em;
    border-radius: 10px;
    background-color: white;
    height: 400px;
  }
}

.cardContainer {
  width: 60%;
  height: 100%;
}

.emptyChart {
  display: flex;
  width: 100% !important;
}

.chartWrapper {
  width: 100%;
  max-width: 65%;
  height: 100%;
  display: flex;
  min-height: 20em;
  align-items: center;
}

.chartDetails {
  width: 100%;
  .chartStatusInfo {
    display: flex;
    justify-content: space-between;
    width: 10em;
    align-items: center;
    margin: 1em 0;
    div {
      display: flex;
      gap: 1em;
    }
    span {
      height: 25px;
      width: 25px;
      display: inline-block;
      border-radius: 5px;
    }
  }
}
