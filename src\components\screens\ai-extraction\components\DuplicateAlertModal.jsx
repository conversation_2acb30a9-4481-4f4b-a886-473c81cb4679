import React, { useEffect, useState } from 'react';
import { Modal } from 'semantic-ui-react';
import { AlertTriangle } from 'lucide-react';
import MinimumCharCount from '../../../ui-components/MinimumCharCount';
import DuplicateInfoTable from './DuplicateInfoTable';

function DuplicateAlertModal({
  isOpen = false,
  duplicateData = [],
  duplicateType = null,
  onClose = () => {},
  onMarkAsDuplicate = () => {},
  onMarkAsNotDuplicate = () => {},
}) {
  const [comment, setComment] = useState('');
  useEffect(() => setComment(''), [isOpen]);
  return (
    <Modal open={isOpen} onClose={onClose} closeOnDimmerClick={false} size="small" dimmer="blurring">
      <Modal.Content className="p-0 rounded-lg overflow-hidden">
        {/* Header */}
        <div className="p-4 pt-0 border-b border-gray-200">
          <div className="flex items-center justify-center gap-3">
            <div
              className={`p-2 rounded-full flex-shrink-0 ${
                duplicateType === 'confirmed' ? 'bg-red-100' : 'bg-yellow-100'
              }`}
            >
              <AlertTriangle
                className={`h-7 w-7 ${duplicateType === 'confirmed' ? 'text-red-500' : 'text-yellow-600'}`}
                strokeWidth={2}
              />
            </div>
            <h2 className="text-lg font-semibold m-0">Duplicate Invoice Warning</h2>
          </div>
        </div>

        <div className="max-h-[300px] overflow-y-auto">
          <DuplicateInfoTable data={duplicateData} />
        </div>

        {/* Comment Area */}
        <div className="px-4 py-3 border-t border-gray-200">
          <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-1">
            Comment
          </label>
          <div className="pb-5 relative">
            <textarea
              id="comment"
              rows={4}
              className="w-full px-3 py-2 border resize-none border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-gray-200"
              placeholder="Write your comment here..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
            />
            <div className="absolute -bottom-2 right-0">
              <MinimumCharCount minChars={20} charCount={comment.length} />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="px-4 py-4 flex justify-between border-t border-gray-200">
          <button
            className="w-fit py-2 px-4 text-nowrap bg-gray-200 text-gray-800 rounded-full hover:bg-gray-300 transition-colors"
            onClick={onClose}
          >
            Cancel
          </button>

          <div className="flex gap-2">
            <button
              className="w-fit py-2 px-4 text-nowrap rounded-full transition-colors bg-[#F6D659] hover:bg-yellow-500 text-[#7E6607] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              onClick={() => onMarkAsNotDuplicate(comment)}
              disabled={comment.length < 20}
            >
              Mark as Not Duplicate
            </button>
            <button
              className="w-fit py-2 px-4 text-nowrap rounded-full transition-colors bg-[#F6D659] hover:bg-yellow-500 text-[#7E6607] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              onClick={() => onMarkAsDuplicate(comment)}
            >
              Mark as Duplicate
            </button>
          </div>
        </div>
      </Modal.Content>
    </Modal>
  );
}

export default DuplicateAlertModal;
