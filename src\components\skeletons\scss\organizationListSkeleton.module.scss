@import '../../../assets/scss/main.scss';
@import '../../screens/profile/components/scss/OrganizationList.module.scss';

.skeletonContainer {
  display: flex;
  flex-direction: column;
}

.skeletonBox {
  width: 30px;
  height: 30px;
  background-color: #e0e0e0;
  border-radius: 50%;
}

.skeletonText {
  width: 100px;
  height: 14px;
  background-color: #e0e0e0;
  border-radius: 4px;
  margin-top: 5px;
}

.skeletonButton {
  width: 80px;
  height: 14px;
  background-color: #e0e0e0;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

.skeletonContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeletonBox,
.skeletonText,
.skeletonButton {
  background: linear-gradient(90deg, #e0e0e0 25%, #f0f0f0 50%, #e0e0e0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}
