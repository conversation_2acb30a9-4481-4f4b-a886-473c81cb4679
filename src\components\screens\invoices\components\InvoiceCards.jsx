import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import style from './invoiceCards.module.scss';
import SlicedText from '../../../generic-components/SlicedText';
import { Calendar, FileText, IndianRupee, Tag, Ticket } from 'lucide-react';
import { convertDateTimeFormat, getStatusClass } from '../../../utils';
import { formatAmount } from '../../../utils/dateUtils';
import { mapAiInvoiceStatus } from '../../../utils/aiUtils';
import { Link } from 'react-router-dom';

const InvoiceCards = ({ invoices = [] }) => {
  const navigate = useNavigate();

  const handlePreventDefault = useCallback((e) => {
    e.stopPropagation();
    e.preventDefault();
  }, []);

  const handleViewInvoice = useCallback(
    (invoice) => {
      navigate(`/ai-extraction/${invoice.file_id || invoice.id}`, {
        state: {
          fileUrl: invoice.file_url || invoice.url,
          fileName: invoice.file_name || invoice.document_name,
          businessId: invoice?.business_id,
        },
      });
    },
    [navigate]
  );

  return (
    <div className={style.cardsContainer}>
      {invoices.map((invoice) => {
        const status = mapAiInvoiceStatus(invoice?.status);
        const fileName = invoice?.file_name || invoice?.documentName || 'No name';
        const ticketId = invoice?.ticket_id || invoice?.ticketId;
        const invoiceDate = invoice?.invoice_date || invoice?.date;
        const invoiceAmount = invoice?.invoice_amount;
        const supplierName = invoice?.supplier_name;
        const invoiceNumber = invoice?.invoice_number;

        return (
          <div className={style.card} key={invoice.id || invoice.file_id}>
            <div className={style.cardHeader}>
              <div className={style.fileInfo} onClick={handlePreventDefault}>
                <FileText className={style.fileIcon} />
                <SlicedText text={fileName} sliceTill={30} />
              </div>
              <div className={`${style.status} ${getStatusClass(status)}`}>{status}</div>
            </div>

            <div className={style.cardBody}>
              {ticketId && (
                <div className={style.infoRow}>
                  <div className={style.infoLabel}>
                    <Ticket size={16} />
                    <span>Ticket ID:</span>
                  </div>
                  <div className={style.infoValue}>
                    {ticketId && (
                      <Link
                        to={`/ticket-view/${ticketId}`}
                        onClick={(e) => e.stopPropagation()}
                        className={style.ticketLink}
                      >
                        {ticketId}
                      </Link>
                    )}
                  </div>
                </div>
              )}

              {invoiceNumber && (
                <div className={style.infoRow}>
                  <div className={style.infoLabel}>
                    <Tag size={16} />
                    <span>Invoice #:</span>
                  </div>
                  <div className={style.infoValue}>
                    <SlicedText text={invoiceNumber} sliceTill={20} />
                  </div>
                </div>
              )}

              {supplierName && (
                <div className={style.infoRow}>
                  <div className={style.infoLabel}>
                    <Tag size={16} />
                    <span>Supplier:</span>
                  </div>
                  <div className={style.infoValue}>
                    <SlicedText text={supplierName} sliceTill={20} />
                  </div>
                </div>
              )}

              {invoiceDate && (
                <div className={style.infoRow}>
                  <div className={style.infoLabel}>
                    <Calendar size={16} />
                    <span>Date:</span>
                  </div>
                  <div className={style.infoValue}>{convertDateTimeFormat(invoiceDate, false)}</div>
                </div>
              )}

              {invoiceAmount && (
                <div className={style.infoRow}>
                  <div className={style.infoLabel}>
                    <IndianRupee size={16} />
                    <span>Amount:</span>
                  </div>
                  <div className={style.infoValue}>{formatAmount(invoiceAmount)}</div>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default InvoiceCards;
