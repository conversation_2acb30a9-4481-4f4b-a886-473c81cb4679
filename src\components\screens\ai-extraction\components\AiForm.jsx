import React, { useState, useCallback, useEffect, useMemo } from 'react';
import './AiForm.scss';
import { X, AlertCircle } from 'lucide-react';
import { debounce } from 'lodash';
import { validateRequiredFields } from './validation';
import {
  SupplierDetailsSection,
  BillToDetailsSection,
  ProductServicesSection,
  InvoiceSummarySection,
  AdditionalInformationSection,
  GstSummarySection,
} from '../form-sections';
import { getSectionsWithErrors } from '../../../utils/aiUtils';
import DuplicateWarning from './DuplicateWarning';
import ExpandableSection from './ExpandableSection';
import AiFormHeader from './AiFormHeader';

const SECTIONS = [
  {
    title: 'Supplier Details',
    component: SupplierDetailsSection,
    jsonKey: 'supplier_details',
  },
  {
    title: 'Bill To Details',
    component: BillToDetailsSection,
    jsonKey: 'bill_to_details',
  },
  {
    title: 'Sales of Product / Services details',
    component: ProductServicesSection,
    jsonKey: 'sales_of_product_services',
  },
  {
    title: 'GST Summary',
    component: GstSummarySection,
    jsonKey: 'gst_ledgers',
  },
  {
    title: 'Invoice Summary',
    component: InvoiceSummarySection,
    jsonKey: 'invoice_summary',
  },
  {
    title: 'Additional Information',
    component: AdditionalInformationSection,
    jsonKey: 'additional_information',
  },
];

const sections_json_keys = SECTIONS.map((section) => section.jsonKey);

function AiForm({
  isLoading,
  categoryOptions,
  extractedData,
  refetch,
  handlePrevious,
  handleNext,
  reachedStatus,
  didNotFound,
}) {
  const isReadOnlyInitial = useMemo(
    () =>
      extractedData?.status === '3' ||
      extractedData?.status === '4' ||
      extractedData?.status === '5' ||
      extractedData?.status === '6' ||
      extractedData?.status === '7' ||
      extractedData?.status === '8',
    [extractedData]
  );
  const [formData, setFormData] = useState(extractedData || {});
  const [expandedSections, setExpandedSections] = useState(new Set(sections_json_keys));
  const [invoiceType, setInvoiceType] = useState(extractedData?.invoice_type || null);
  const [isReadOnly, setIsReadOnly] = useState(isReadOnlyInitial);
  const [showOriginal, setShowOriginal] = useState(false);
  const [originalInvoice, setOriginalInvoice] = useState(null);
  const [sectionsWithErrors, setSectionsWithErrors] = useState([]);
  const [sectionsWithMissingFields, setSectionsWithMissingFields] = useState(
    [null] // adding null to avoid Validate button from being enabled for the first time
  );

  useEffect(() => {
    setIsReadOnly(isReadOnlyInitial);
  }, [isReadOnlyInitial]);

  useEffect(() => {
    if (invoiceType) {
      setExpandedSections(new Set(sections_json_keys));
    }
  }, [invoiceType]);

  const debouncedValidation = useCallback(
    debounce((newFormData) => {
      const errorSections = getSectionsWithErrors(newFormData, ['duplicate_validation']);
      setSectionsWithErrors(errorSections);

      const missingSections = validateRequiredFields(newFormData);
      setSectionsWithMissingFields(missingSections);
    }, 400),
    []
  );

  // Update validation when formData changes
  useEffect(() => {
    if (formData) {
      debouncedValidation(formData);
    }
  }, [formData, debouncedValidation]);

  /**
   * This function handles various form actions based on the action type
   * @param {string} action - The action type to perform (e.g., UPDATE_SECTION, HARD_UPDATE_SECTION)
   * @param {string} section - The form section key
   * @param {string} field - The form section field's key
   * @param {string|number|object} value - New value or update object
   */
  const formAction = useCallback((action, section, field, value, index) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case 'FIELD_CHANGE':
          updatedSection = { ...prevSection, [field]: value };
          break;

        case 'INDEX_FIELD_CHANGE':
          updatedSection = [...prevSection];
          updatedSection[index][field] = value;
          break;

        case 'UPDATE_SECTION':
          updatedSection = { ...prevSection, ...value };
          break;

        case 'INDEX_UPDATE_SECTION':
          updatedSection = [...prevSection];
          updatedSection[index] = { ...prevSection[index], ...value };
          break;

        case 'HARD_UPDATE_SECTION':
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  const countSectionAlert = (section, key) => {
    const dataToUse = showOriginal ? originalInvoice : formData;
    const sectionData = dataToUse?.[section];
    if (!sectionData) return 0;
    if (Array.isArray(sectionData)) {
      return sectionData.reduce((count, item) => {
        const alerts = item?.[key];
        return count + (alerts ? Object.keys(alerts).length : 0);
      }, 0);
    }
    const alerts = sectionData?.[key];
    return alerts ? Object.keys(alerts).length : 0;
  };

  const handleInvoiceTypeChange = useCallback((newInvoiceType) => {
    setExpandedSections(new Set(sections_json_keys));
  }, []);

  const handleShowOriginalChange = useCallback((newShowOriginal, originalData = null) => {
    setShowOriginal(newShowOriginal);
    if (originalData) {
      setOriginalInvoice(originalData);
    }
  }, []);

  return (
    <div id="extraction-form" className="pt-4 relative w-full bg-white">
      <AiFormHeader
        isLoading={isLoading}
        categoryOptions={categoryOptions}
        extractedData={extractedData}
        refetch={refetch}
        handlePrevious={handlePrevious}
        handleNext={handleNext}
        reachedStatus={reachedStatus}
        didNotFound={didNotFound}
        formData={formData}
        setFormData={setFormData}
        invoiceType={invoiceType}
        setInvoiceType={setInvoiceType}
        isReadOnly={isReadOnly}
        setIsReadOnly={setIsReadOnly}
        sectionsWithErrors={sectionsWithErrors}
        setSectionsWithErrors={setSectionsWithErrors}
        sectionsWithMissingFields={sectionsWithMissingFields}
        onShowOriginalChange={handleShowOriginalChange}
        onInvoiceTypeChange={handleInvoiceTypeChange}
        showOriginal={showOriginal}
        setShowOriginal={setShowOriginal}
      />

      {showOriginal && <ViewOriginalBanner showOriginal={showOriginal} setShowOriginal={setShowOriginal} />}

      {/* Duplicate Validation Warning */}
      {extractedData?.duplicate_validation && (
        <DuplicateWarning
          duplicateData={extractedData?.duplicate_validation?.duplicate_invoices || []}
          duplicateType={extractedData?.duplicate_validation?.type || 'possible'}
        />
      )}

      {SECTIONS.map(({ title, component: Component, jsonKey }) => {
        const errorCount = countSectionAlert(jsonKey, 'error');
        return (
          <div key={jsonKey}>
            <ExpandableSection
              key={jsonKey}
              title={title}
              expandedSections={expandedSections}
              setExpandedSections={setExpandedSections}
              sectionId={jsonKey}
              errorCount={errorCount}
              warningCount={countSectionAlert(jsonKey, 'warning')}
              hasMissingFields={sectionsWithMissingFields.includes(jsonKey)}
              invoiceType={true}
            >
              <Component
                formData={showOriginal ? originalInvoice : formData}
                errorCount={errorCount}
                isReadOnly={isReadOnly || showOriginal}
                invoiceType={invoiceType}
                setFormData={setFormData}
                formAction={formAction}
              />
            </ExpandableSection>
          </div>
        );
      })}
    </div>
  );
}

function ViewOriginalBanner({ showOriginal, setShowOriginal }) {
  return (
    <div className="sticky top-2 left-0 w-[calc(100%-2em)] bg-white border border-accent1-border px-4 py-2 mx-4 mb-4 flex items-center justify-between z-10 rounded-lg shadow-sm font-semibold">
      <div className="flex items-center gap-2">
        <AlertCircle size={20} />
        <span>You are viewing the original data. Form is in read-only mode.</span>
      </div>
      <button
        className="cursor-pointer bg-transparent border-none text-[#0B1A30] p-1 rounded-full hover:bg-black/5"
        onClick={() => setShowOriginal(!showOriginal)}
      >
        <X size={18} />
      </button>
    </div>
  );
}

export default AiForm;
