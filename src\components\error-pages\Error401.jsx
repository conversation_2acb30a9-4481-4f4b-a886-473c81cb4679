import React from 'react';
import error401 from '../../assets/Images/error/401.png';
import Header from '../global/Header';
import style from '../scss/login.module.scss';

const Error401 = () => {
  return (
    <div className={style.mainContainer}>
      <Header withOutUser={true} />
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: 'calc(100vh - 64px)',
          padding: '1rem',
          maxWidth: '1000px',
          margin: '0 auto',
        }}
      >
        <img
          src={error401}
          alt="Error 401"
          style={{
            maxWidth: '500px',
            width: '100%',
            height: 'auto',
            objectFit: 'contain',
            marginBottom: '2rem',
          }}
        />
      </div>
    </div>
  );
};

export default Error401;
