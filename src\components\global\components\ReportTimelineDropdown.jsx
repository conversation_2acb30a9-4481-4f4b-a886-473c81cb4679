import React from 'react';
import { Input } from 'semantic-ui-react';
import { CloseIcon, DropdownIcon, RadioSelectedIcon } from '../../../assets/svgs';
import style from './scss/reportTimelineDropdown.module.scss';
import { useAuth } from '../../../contexts/AuthContext';
import { timelineDropdownOptions } from '../../utils/constants';

const ReportTimelineDropdown = ({
  handleSelectDropdown,
  setActiveInput,
  handleDateSelection,
  dateInfo,
  activeInput,
  setDateInfo,
  popupRef,
  selectedTimeline,
  handleCloseDateFilter,
  handleClose,
  openDropdown,
  handleApplyFilter,
}) => {
  const { isMobileScreen } = useAuth();
  const today = new Date().toISOString().split('T')[0];

  const handleClrDates = () => {
    setDateInfo({ startDate: '', endDate: '' });
  };

  const renderContent = () => (
    <div className={style.popupContainer} ref={popupRef} onClick={(e) => e.stopPropagation()}>
      {timelineDropdownOptions?.map((list) => (
        <div
          className={`${selectedTimeline?.value === list?.value ? style.selectedItem : ''} ${style.dropDownItem}`}
          onClickCapture={() => handleSelectDropdown(list)}
        >
          <div className={style.option}>
            <span className={`${style.customRadio}`}>
              <RadioSelectedIcon />
            </span>
            <p>{list?.text}</p>
          </div>
          {selectedTimeline?.value === 'customDate' && selectedTimeline?.value === list?.value && (
            <>
              <div className={style.dateContainer}>
                {/* Start Date */}
                <div className={style.dateWrapper}>
                  {/* {activeInput.startDate || dateInfo?.startDate ? ( */}
                  <label>Start Date</label>
                  <Input
                    className={style.dateInput}
                    type="date"
                    value={dateInfo?.startDate}
                    onChange={(e) => handleDateSelection('startDate', e.target.value)}
                    onFocus={() => setActiveInput({ ...activeInput, startDate: true })}
                    onBlur={() => setActiveInput((prev) => ({ ...prev, startDate: !!dateInfo?.startDate }))}
                    max={today}
                  />
                  {/* ) : (
                                            <div className={style.emptyInput} onClick={() => setActiveInput({ ...activeInput, startDate: true })}>
                                                <p>Start Date</p> <span><DropdownIcon /></span>
                                            </div>
                                        )} */}
                </div>
                <p>-</p>
                {/* End Date */}
                <div className={style.dateWrapper}>
                  {/* {activeInput.endDate || dateInfo?.endDate ? ( */}
                  <label>End Date</label>
                  <Input
                    className={style.dateInput}
                    type="date"
                    value={dateInfo?.endDate}
                    onChange={(e) => handleDateSelection('endDate', e.target.value)}
                    onFocus={() => setActiveInput({ ...activeInput, endDate: true })}
                    onBlur={() => setActiveInput((prev) => ({ ...prev, endDate: !!dateInfo?.endDate }))}
                    min={dateInfo?.startDate}
                    max={today}
                  />
                  {/* ) : (
                                             <div className={style.emptyInput} onClick={() => setActiveInput({ ...activeInput, endDate: true })}>
                                                 <p>End Date</p> <span><DropdownIcon /></span>
                                             </div>
                                         )} */}
                </div>
              </div>
              {(dateInfo?.startDate || dateInfo?.endDate) && !isMobileScreen && (
                <div className={style.btnWrapper}>
                  <div className={style.clearBtn}>
                    <button disabled={!dateInfo?.startDate || !dateInfo?.endDate} onClick={handleClrDates}>
                      Clear
                    </button>
                  </div>
                  <div className={style.dateApplyBtn}>
                    <button
                      className={!dateInfo?.startDate || !dateInfo?.endDate ? style.disableBtn : ''}
                      disabled={!dateInfo?.startDate || !dateInfo?.endDate}
                      onClick={handleCloseDateFilter}
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      ))}
    </div>
  );

  if (isMobileScreen) {
    return (
      <div className={isMobileScreen && openDropdown ? 'dimmer' : ''} onClick={handleClose}>
        <div
          className={`${openDropdown ? style.openDropdown : ''} ${style.logListPopup}`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className={style.listCloseIcon} onClick={handleClose}>
            <CloseIcon />
          </div>
          <div className={style.modalContent}>
            <div>
              <h4>Time period</h4>
            </div>
            <hr />
            {renderContent()}
            <hr />
            <div className={style.applyBtn}>
              <button className={style.downloadBtn} onClick={handleApplyFilter}>
                Apply
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return renderContent();
};

export default ReportTimelineDropdown;
