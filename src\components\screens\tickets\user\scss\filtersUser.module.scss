@import '../../../../../assets/scss/main.scss';

.filtersWrapper {
  display: flex;
  gap: 1em;
  @include for_media(mobileScreen) {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    justify-content: center;
    gap: 1em;
  }
}

.logFilterWrapper {
  display: flex;
  gap: 1em;
  justify-content: space-between;
  width: 100%;
  @include for_media(mobileScreen) {
    width: 100%;
    display: block;
  }

  .logDropdown {
    display: flex;
    background-color: #ffffff !important;
    align-items: center;
    padding: 0 1em;
    justify-content: space-between;
    border: 1px solid $borderColor;
    border-radius: 12px;
    height: 3.5em;
    input {
      height: 2.5em;
      background-color: #ffffff !important;
      font-size: 1em !important;
    }
  }
  .searchIconWrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 3.5em;
    height: 3.5em;
    background-color: #ffffff;
    border: 1px solid $borderColor;
    border-radius: 12px;
  }
}

.statusListContainer {
  display: flex;
  justify-content: flex-end;
  gap: 1em;
  padding: 0.5em 0;
  height: 100%;
  width: 100%;
  @include for_media(mobileScreen) {
    justify-content: center;
  }
  -webkit-user-select: none;
  user-select: none;
  span {
    padding: 0.5em 1em;
    font-size: 1.1em;
    box-shadow: 0 0 1px 1px rgb(201, 201, 201);
    border-radius: 35px;
    cursor: pointer;
    border: 1px solid $accentBorder1;
    background-color: $accentBgColor1;
    @include for_media(mobileScreen) {
      font-size: 1em;
    }
  }
  .activeStatus {
    background-color: $accentColor2;
    color: $white;
  }
}
