import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../utils/apiUtils';

function useServiceFetch(serviceFn, isShowErrorToast = false, errorMsg = null, autoFetch = true) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const execute = useCallback(
    async (skipLoadingState = false) => {
      if (!skipLoadingState) {
        setLoading(true);
      }
      setError(null);

      try {
        const response = await serviceFn();
        setData(response);
      } catch (err) {
        const errorMessage = getErrorMessage(err);
        if (isShowErrorToast) {
          toast.error(`${errorMsg || ''} (${errorMessage})`);
        }
        setError(errorMessage);
      } finally {
        if (!skipLoadingState) {
          setLoading(false);
        }
      }
    },
    [serviceFn]
  );

  useEffect(() => {
    if (autoFetch) {
      execute();
    }
  }, [autoFetch]);

  const refetch = useCallback(
    (skipLoadingState = true) => {
      execute(skipLoadingState);
    },
    [serviceFn]
  );

  return { data, setData, error, loading, execute, refetch };
}

export default useServiceFetch;
