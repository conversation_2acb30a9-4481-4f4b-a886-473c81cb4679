import React from 'react';
import MainContent from './MainContent';
import RenderDownloadModal from '../../../../utils/reportUtils/RenderDownloadModal';
import ReportHeader from '../../../../global/components/ReportHeader';
import ReportSortFilter from '../../../../global/components/ReportSortFilter';

const RenderOverallContent = ({
  payableDetails,
  handleRefresh,
  setDownloadModal,
  isLoading,
  handlePagination,
  activePage,
  activeTab,
  setActiveTab,
  handleDropdownList,
  selectedTimeline,
  handleSelectDropdown,
  handleClear,
  handleApplySort,
  openDropdown,
  setOpenDropdown,
  handleSearch,
  searchTerm,
  detailedLoading,
  invoiceInfo,
  customerDetails,
  downloadModal,
  handleDownload,
}) => {
  return (
    <>
      <ReportHeader
        title="Total Payable"
        data={payableDetails}
        handleRefresh={handleRefresh}
        setDownloadModal={setDownloadModal}
      />
      <MainContent
        isLoading={isLoading}
        payableDetails={payableDetails}
        handlePagination={handlePagination}
        activePage={activePage}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        handleDropdownList={handleDropdownList}
        renderPopupContent={
          <ReportSortFilter
            selectedTimeline={selectedTimeline}
            handleSelectDropdown={handleSelectDropdown}
            handleClear={handleClear}
            handleApplySort={handleApplySort}
          />
        }
        openDropdown={openDropdown}
        setOpenDropdown={setOpenDropdown}
        handleSearch={handleSearch}
        searchTerm={searchTerm}
        detailedLoading={detailedLoading}
        invoiceInfo={invoiceInfo}
        customerDetails={customerDetails}
      />

      <RenderDownloadModal
        isOpen={downloadModal}
        onClose={() => setDownloadModal(false)}
        content={`Your Payable Report is prepared and ready for download. You can save it for further review`}
        downloadFunction={handleDownload}
        checkBoxDisable={true}
      />
    </>
  );
};

export default RenderOverallContent;
