import React from 'react';
import style from './scss/businessCardsList.module.scss';
import ls from 'local-storage';
import { Card } from 'semantic-ui-react';
import { useAuth } from '../../../../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { renderOrganizationLogo } from '../../../../utils';

function BusinessCardsList({ businesses }) {
  const selectedBusiness = ls.get('selectedBusiness');
  const { setLogo } = useAuth();
  const navigate = useNavigate();

  const handleBusinessSelection = (data) => {
    setLogo(data?.business_image);
    ls.set('selectedBusiness', data);
    navigate(`/ticketList/${data?.business_id}`);
  };

  return (
    <div className={style.businessCardsWrapper}>
      {businesses?.map((data) => (
        <Card
          key={data?.business_id}
          className={`${selectedBusiness?.business_id === data?.business_id && style.selectedCard} ${style.card}`}
          onClickCapture={() => handleBusinessSelection(data)}
        >
          <Card.Content className={style.cardContent}>
            {renderOrganizationLogo(data?.business_image)}
            <div className={style.info}>
              <h5>{data?.business_name}</h5>
              {selectedBusiness?.business_id === data?.business_id && <span>Selected</span>}
            </div>
          </Card.Content>
        </Card>
      ))}
    </div>
  );
}

export default BusinessCardsList;
