import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import style from './scss/businessList.module.scss';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import { useAuth } from '../../contexts/AuthContext';
// import Avatar from 'react-avatar';
// import ls from "local-storage";
// import { processLogout } from '../utils';
import { Image, Table, Card, Pagination, Dropdown, Modal, Checkbox, Button, Loader, Input } from 'semantic-ui-react';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import {
  closeIcon,
  defaultLogo,
  downloadIcon,
  dropdownIcon,
  DropdownIcon,
  filterIcon,
  mailIcon,
  minusIcon,
  plusIcon,
  ticketIcon,
  ticketLogIcon,
} from '../global/Icons';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import debounce from 'lodash/debounce';

const SystemActivityPage = () => {
  // const navigate = useNavigate();
  // const { TicketList } = useAuth();
  const [TicketList, setTicketList] = useState();
  const [logList, setLogList] = useState([]);
  const [BuisnesCount, setBuisnessCount] = useState(0);
  // const [searchQuery, setSearchQuery] = useState('')
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  // const navigate = useNavigate();
  const [paginationInfo, setPaginationInfo] = useState();
  const [activePage, setActivePage] = useState(1);
  const [downloadLink, setDownloadLink] = useState('');
  const [loadingDownload, setloadingDownload] = useState('');
  const [userList, setUserList] = useState([]);
  const [categoryList, setCategoryList] = useState([]);
  const [businessList, setBusinessList] = useState(0);
  const [activeAdminFilter, setActiveAdminFilter] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [logDowloadLoad, setlogDowloadLoad] = useState(false);
  const [logDownloadLink, setLogDownloadLink] = useState(false);

  const [adminFilter, setActiveItem] = useState({
    date: false,
    role: false,
    status: false,
    // ticket: false,
  });
  const [tempFilters, setTempFilters] = useState({
    selectedDate: { start: '', end: '' },
    selectedStatus: [],
    selectedRole: [],
    searchText: '',
  });
  const [filters, setFilters] = useState({
    searchText: '',
    selectedPeople: '',
    selectedOrganisation: '',
    selectedCategory: '',
  });

  useEffect(() => {
    setloadingDownload(true);
    GlobalService.generalSelect(
      (respdata) => {
        setDownloadLink(respdata?.download_link);
        setloadingDownload(false);
      },
      `${resturls.entryLogDownload}`,
      {},
      'GET'
    );
  }, []);

  const OrganisationSearch = (isActiveStatus) => {
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setTicketList([]);
        setBuisnessCount(respdata.count); // Update the business count
        setTicketList(results); // Update the business list
      },
      `${resturls.getBusinesses}?business_name=${isActiveStatus}`,
      {},
      'GET'
    );
  };
  const handleSearch = useCallback(
    debounce((query) => {
      handleFilterChange('searchText', query);
    }, 0),
    []
  );

  const handleInputChange = (e) => {
    const query = e.target.value;
    handleSearch(query);
  };

  useEffect(() => {
    setLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setLogList(results);
        setPaginationInfo(respdata);
        setLoading(false);
      },
      `${resturls.logEntry}/`,
      {},
      'GET'
    );
  }, []);

  const obtainUsersList = () => {
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.data) {
          const { data } = respdata;
          const list = data?.map((user) => ({
            key: user.user_id,
            value: user.user_id,
            text: user.full_name,
            role: user?.role,
          }));
          const updatedList = [{ key: 'all_users', value: '', text: 'All users' }, ...list];
          setUserList(updatedList);
        }
      },
      `${resturls.obtainCategoryWiseUser}?user_type=all`,
      {},
      'GET'
    );
  };

  const [nextPageOrg, setnextPageOrg] = useState(1);
  const [isFetchingCategories, setIsFetchingCategories] = useState(false);
  const [isFetchingBusinesses, setIsFetchingBusinesses] = useState(false);
  const [nextPage, setNextPage] = useState(null);
  // const [isFetching, setIsFetching] = useState(false);

  useEffect(() => {
    obtainUsersList();
    obtainBusinessList(1);
    obtainCategoryList(1); // Fetch first page of businesses
  }, []);

  const obtainCategoryList = (page = 1) => {
    setIsFetchingCategories(true);
    const url = `${resturls.obtainCategortList}?page=${page}`;

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.results) {
          const { results, next } = respdata;
          const all = { key: 'all-category', value: '', text: 'All category' };
          const list = results.map((category) => ({
            key: category.id,
            value: category.name,
            text: category.name,
          }));

          const updatedList = page === 1 ? [all, ...list] : [...categoryList, ...list];
          setCategoryList(updatedList);

          if (next) {
            setNextPage(page + 1);
          } else {
            setNextPage(null);
          }

          setIsFetchingCategories(false);
        }
      },
      url,
      {},
      'GET'
    );
  };

  const obtainBusinessList = (page = 1) => {
    setIsFetchingBusinesses(true);
    const url = `${resturls.getBusinesses}?page=${page}`;

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.results) {
          const { results, next } = respdata;
          const optionList = results.map((info) => ({
            key: info?.business_id,
            value: info?.business_id,
            text: info?.business_name,
          }));

          const updatedList =
            page === 1
              ? [{ key: 'all_organisation', value: '', text: 'All organization' }, ...optionList]
              : [...businessList, ...optionList];

          setBusinessList(updatedList);

          if (next) {
            setnextPageOrg(page + 1);
          } else {
            setnextPageOrg(null);
          }

          setIsFetchingBusinesses(false);
        }
      },
      url,
      {},
      'GET'
    );
  };

  const organisationList = () => {
    if (businessList?.length > 0) {
      return businessList;
    }
    return [];
  };

  //   const { userInfo } = useAuth();
  useEffect(() => {
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setBuisnessCount(respdata.count);
        setTicketList(results);
      },
      `${resturls.ticketList}`,
      {},
      'GET'
    );
  }, []);

  const orgIcon = () => (
    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37322 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37322 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
        fill="#717680"
      />
    </svg>
  );

  const renderLogo = (logo) => {
    if (logo) {
      return <Image src={logo} />;
    }
    return orgIcon();
  };

  const formatDate = (isoDateString) => {
    const date = new Date(isoDateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const renderList = () => {
    const itemsPerPage = 10;
    const totalPages = Math.ceil(paginationInfo?.count / itemsPerPage);
    const handlePaginationChange = (e, { activePage }) => {
      setActivePage(activePage);
      // const selected = paginationInfo?.next || paginationInfo?.previous;
      // const bisnsId = selected?.split("api/get-tickets/")[1]?.split("/")[0]?.split("?")[0];
      const queryParams = new URLSearchParams();

      if (tempFilters.selectedRole?.length) {
        tempFilters.selectedRole.forEach((role) => queryParams.append('role', role));
      }

      const capitalizeFirstLetter = (str) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

      if (tempFilters.selectedStatus?.length) {
        tempFilters.selectedStatus.forEach((status) => queryParams.append('status', capitalizeFirstLetter(status)));
      }

      if (filters.selectedPeople?.length > 0) queryParams.append('user_id', filters.selectedPeople);
      if (filters.selectedOrganisation.length > 0) queryParams.append('business_id', filters.selectedOrganisation);
      if (filters.selectedCategory.length > 0) queryParams.append('category', filters.selectedCategory);

      if (tempFilters.searchText.length > 0) {
        queryParams.append('search_query', tempFilters.searchText);
      }

      if (tempFilters.selectedDate.start?.length > 0) {
        const formattedStart = formatDateToDMY(tempFilters.selectedDate.start);
        queryParams.append('from_date', formattedStart);
      }

      if (tempFilters.selectedDate.end.length > 0) {
        const formattedEnd = formatDateToDMY(tempFilters.selectedDate.end);
        queryParams.append('to_date', formattedEnd);
      }

      queryParams.append('page', activePage);
      const endpoint = `${resturls.logEntry}/?${queryParams}`;
      setLoading(true);
      GlobalService.generalSelect(
        (respdata) => {
          console.log(respdata, 'handlePaginationChange');
          if (respdata && respdata.results) {
            setLogList(respdata.results);
            setPaginationInfo(respdata);
            setLoading(false);
          } else {
            console.warn('No results found in response:', respdata);
            setLogList([]);
          }
        },
        endpoint,
        {},
        'GET'
      );
    };

    if (logList?.length === 0) {
      return (
        <div className={style.tableWrapper}>
          <div className={style.noMatchMsg}>
            <p>No matches found</p>
          </div>
          {activeAdminFilter && renderAdminFilter()}
        </div>
      );
    }

    return (
      <>
        <div className={style.tableWrapper}>
          <Table basic="very">
            <Table.Header>
              <Table.Row>
                {/* <Table.HeaderCell><div className='customCheckBox '><Checkbox indeterminate className={`${style.checkbox}`}/></div></Table.HeaderCell> */}
                <Table.HeaderCell className={style.subjectheaderRow}>Details</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Action Type</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Category/Module</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Organization</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>User and Role</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Timestamp</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Status</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {logList?.map((data) => (
                <Table.Row>
                  {/* <Table.Cell><div className='customCheckBox'><Checkbox className={`${style.checkbox}`} /></div></Table.Cell> */}
                  <Table.Cell>
                    <div className={style.ticketSubject}>
                      <div className={style.businessName}>
                        <p>{data?.details}</p>
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>{data?.action_flag_name}</Table.Cell>
                  <Table.Cell>
                    {data?.category ? <p className={style.categoryItem}>{data?.category}</p> : '-'}
                  </Table.Cell>

                  <Table.Cell>
                    <div className={style.businessInfo}>
                      <div className={style.logo}>{renderLogo(data?.business_image)}</div>
                      {data?.business_name || '-'}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <p>{data?.user?.full_name || '-'}</p>
                    {data?.user?.role || '-'}
                  </Table.Cell>
                  <Table.Cell>{(data?.action_time && formatDate(data?.action_time)) || '-'}</Table.Cell>
                  <Table.Cell>
                    {data?.status ? (
                      <span
                        className={`${style.selectedStatus} ${data?.status === 'Successful' ? style.success : style.failed}`}
                      >
                        {data?.status}
                      </span>
                    ) : (
                      '-'
                    )}
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
          {activeAdminFilter && renderAdminFilter()}
        </div>
        <div className={style.paginationWrapper}>
          {paginationInfo?.count > 10 && logList?.length > 0 && (
            <Pagination activePage={activePage} totalPages={totalPages} onPageChange={handlePaginationChange} />
          )}
        </div>
      </>
    );
  };

  const renderCardList = () => {
    return (
      <div className={style.tableWrapper}>
        <div className={style.ticketList}>
          {TicketList?.map((data) => (
            <Card className={`${style.ticketCard}`}>
              <Card.Content className={style.ticketContent}>
                <div className={style.rightContent}>
                  <h5>{data?.business_name}</h5>
                  <p>{data?.business_id}</p>
                  {data?.accountant_name && (
                    <p>
                      {data?.accountant_name || '-'}
                      {`  (Accountant)`}
                    </p>
                  )}
                </div>
                <div className={style.leftContent}>{renderLogo(data?.business_image)}</div>
              </Card.Content>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const handleFilterChange = (key, value) => {
    console.log(key, value, 'key, value');
    setLoading(true);
    setFilters((prev) => {
      const updatedFilters = { ...prev, [key]: value };
      const queryParams = new URLSearchParams();

      if (updatedFilters.searchText) queryParams.append('search_query', updatedFilters.searchText);
      if (updatedFilters.selectedPeople) queryParams.append('user_id', updatedFilters.selectedPeople);
      if (updatedFilters.selectedOrganisation) queryParams.append('business_id', updatedFilters.selectedOrganisation);
      if (updatedFilters.selectedCategory) queryParams.append('category', updatedFilters.selectedCategory);

      // Make the API call with the constructed query string
      GlobalService.generalSelect(
        (respdata) => {
          console.log(respdata, 'Filtered Data +++++++++++++');
          const { results } = respdata;
          setLogList(results); // Update the business list
          setPaginationInfo(respdata);
          setLoading(false);
        },
        `${resturls.logEntry}/?${queryParams.toString()}`,
        {},
        'GET'
      );

      return updatedFilters;
    });
  };

  const handleEmptyFilter = () => {
    setTempFilters({
      selectedStatus: [],
      selectedDate: { start: '', end: '' },
      selectedRole: [],
      // selectedTicket: [],
    });
    setActiveAdminFilter(false);
    const clear = true;
    applyFilters({ clear });
  };

  const roleList = [
    {
      text: 'Business super user',
      value: 'business_superuser',
      key: 'business_superuser',
    },
    {
      text: 'Business user',
      value: 'business_user',
      key: 'business_user',
    },
    {
      text: 'Accountant',
      value: 'accountant',
      key: 'accountant',
    },
    {
      text: 'Manager',
      value: 'manager',
      key: 'manager',
    },
    {
      text: 'Admin',
      value: 'superuser',
      key: 'superuser',
    },
  ];

  const statusOptions = [
    { key: 'success', value: 'Successful', text: 'Successfull', className: style.verifiedStatus },
    { key: 'failed', value: 'Fcailed', text: 'Failed', className: style.closedStatus },
  ];

  const renderAdminFilter = () => {
    return (
      <div className={style.adminFilterConatiner}>
        <div className={style.mainWrapper}>
          <div className={style.headerWrapper}>
            <h5>More Filters</h5>
            <div className={style.closeIconWrapper} onClickCapture={() => setActiveAdminFilter(false)}>
              {closeIcon()}
            </div>
          </div>
          <div className={style.filterListContainer}>
            <div className={style.itemWrapper}>
              <div className={style.filterItem} onClickCapture={() => handleActionForAdminFilter('date')}>
                <p>By Date</p>
                {adminFilter?.date ? minusIcon() : plusIcon()}
              </div>
              {adminFilter?.date && (
                <>
                  <hr />
                  <div className={style.listingWrapper}>
                    <div className={style.dateWrapper}>
                      <Input
                        type="date"
                        name="startDate"
                        value={tempFilters.selectedDate.start}
                        className={style.dateField}
                        onChange={(e) => handleTempFilterChange('selectedDate', { start: e.target.value })}
                      />
                      <Input
                        type="date"
                        name="endDate"
                        className={style.dateField}
                        value={tempFilters.selectedDate.end}
                        onChange={(e) => handleTempFilterChange('selectedDate', { end: e.target.value })}
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className={style.itemWrapper}>
              <div className={style.filterItem} onClickCapture={() => handleActionForAdminFilter('role')}>
                <p>By Role</p>
                {adminFilter?.role ? minusIcon() : plusIcon()}
              </div>
              {adminFilter?.role && (
                <>
                  <hr />
                  <div className={style.listingWrapper}>
                    {roleList?.map((data) => (
                      <div className={style.listingItemContainer}>
                        <div className={style.listingItem}>
                          <span />
                          <p>{data?.text}</p>
                        </div>
                        <Checkbox
                          checked={tempFilters.selectedRole?.includes(data.value)}
                          onChange={(e, { checked }) => handleTempFilterChange('selectedRole', data.value, checked)}
                        />
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
            <div className={style.itemWrapper}>
              <div className={style.filterItem} onClickCapture={() => handleActionForAdminFilter('status')}>
                <p>By Status</p>
                {adminFilter?.status ? minusIcon() : plusIcon()}
              </div>
              {adminFilter?.status && (
                <>
                  <hr />
                  <div className={style.listingWrapper}>
                    {statusOptions?.map((data) => (
                      <div className={style.listingItemContainer}>
                        <div className={style.listingItem}>
                          <span className={data?.className} />
                          <p>{data?.text}</p>
                        </div>
                        <Checkbox
                          checked={tempFilters.selectedStatus?.includes(data.value)}
                          onChange={(e, { checked }) => handleTempFilterChange('selectedStatus', data.value, checked)}
                        />
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
            {/* <div className={style.itemWrapper} >
            <div className={style.filterItem}  onClickCapture={() => handleActionForAdminFilter('ticket')}><p>By Ticket Number</p>{plusIcon()}</div>
            {
              adminFilter?.ticket && (
              <>
               <hr />
                <div className={style.listingWrapper}>
                  
                </div>
              </>
              )
            }
          </div> */}
          </div>
        </div>
        <div className={style.btnWrapper}>
          <Button className={style.cancelBtn} onClick={handleEmptyFilter}>
            Clear all
          </Button>
          <Button className={style.applyBtn} onClick={applyFilters}>
            Apply
          </Button>
        </div>
      </div>
    );
  };

  const handleTempFilterChange = (key, value, isChecked) => {
    setTempFilters((prev) => {
      let updatedOptions;

      if (key === 'searchText') {
        updatedOptions = value;
      } else if (key === 'selectedDate') {
        updatedOptions = { ...prev.selectedDate, ...value }; // Merge new date values
      } else {
        updatedOptions = isChecked
          ? [...(prev[key] || []), value] // Add value if checked
          : Array.isArray(prev[key])
            ? prev[key].filter((item) => item !== value) // Filter value if unchecked
            : [];
      }

      console.log('handleTempFilterChange', key, updatedOptions);
      return { ...prev, [key]: updatedOptions };
    });
  };

  useEffect(() => {
    if (tempFilters.searchText) {
      applyFilters();
    }
  }, [tempFilters.searchText]);

  // useEffect(() => {
  //     applyFilters();
  // }, [tempFilters.priority]);

  const formatDateToDMY = (date) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = String(d.getFullYear()); // Get last 2 digits of the year
    return `${day}-${month}-${year}`;
  };

  const applyFilters = ({ clear }) => {
    const queryParams = new URLSearchParams();

    if (tempFilters.selectedRole?.length) {
      tempFilters.selectedRole.forEach((role) => queryParams.append('role', role));
    }

    const capitalizeFirstLetter = (str) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

    if (tempFilters.selectedStatus?.length) {
      tempFilters.selectedStatus.forEach((status) => queryParams.append('status', capitalizeFirstLetter(status)));
    }

    if (tempFilters.searchText) {
      queryParams.append('search_query', tempFilters.searchText);
    }

    if (tempFilters.selectedDate.start) {
      const formattedStart = formatDateToDMY(tempFilters.selectedDate.start);
      queryParams.append('from_date', formattedStart);
    }

    if (tempFilters.selectedDate.end) {
      const formattedEnd = formatDateToDMY(tempFilters.selectedDate.end);
      queryParams.append('to_date', formattedEnd);
    }

    setLoading(true);

    const path = clear ? '' : queryParams.toString();
    console.log(path, 'path', clear);

    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setLogList(results);
        setPaginationInfo(respdata);
        setLoading(false);
        setActiveAdminFilter(false);
        obtainLogDownloadLink(queryParams);
      },
      `${resturls.logEntry}/?${path}`,
      {},
      'GET'
    );
  };

  const obtainLogDownloadLink = (queryParams) => {
    setlogDowloadLoad(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { download_link } = respdata;
        console.log(respdata);
        setLogDownloadLink(download_link);
        setlogDowloadLoad(false);
      },
      `${resturls.entryLogDownload}?${queryParams.toString()}`,
      {},
      'GET'
    );
  };

  const handleActionForAdminFilter = (name) => {
    setActiveItem({ ...adminFilter, [name]: !adminFilter[name] });
  };

  console.log(userList, filters, 'userList=========>');
  const isFiltersEmpty = () => {
    const { selectedDate, selectedStatus, selectedRole, searchText } = tempFilters;
    return (
      selectedDate.start?.length === 0 &&
      selectedDate.end?.length === 0 &&
      selectedStatus.length === 0 &&
      selectedRole.length === 0 &&
      searchText === ''
    );
  };

  const handleAdminFilter = () => {
    setActiveAdminFilter(true);
  };

  return (
    <>
      <Header />
      <div className={style.bussinessListScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>
          <div className={style.headerPart}>
            <div>
              <h4 className={style.separate}>
                System Activity <span className={style.span_orgCOunt}>{paginationInfo?.count}</span>
              </h4>
              <p className={style.desc}>Monitor all system-level actions and updates</p>
            </div>
            <div className={style.btnWrapper}>
              <a href={downloadLink}>
                <div className={style.downloadBtn}>
                  {' '}
                  {loadingDownload ? (
                    <Loader active inline="centered" size="small" />
                  ) : (
                    <>{downloadIcon()} Download All</>
                  )}
                </div>
              </a>
            </div>
          </div>
          <div className={style.filterWrapper}>
            <div className={style.searchWrapper}>
              <Input
                className={style.searchInput}
                icon="search"
                placeholder="Search"
                iconPosition="left"
                value={filters?.searchText}
                onChange={handleInputChange}
              />
              <Dropdown
                placeholder="People"
                className={`customDropdown3 ${style.statusDropdown}`}
                icon={<DropdownIcon />}
                options={userList}
                onChange={(e, { value }) => handleFilterChange('selectedPeople', value)}
              />

              <Dropdown
                placeholder="Organization"
                className={`customDropdown3 ${style.statusDropdown}`}
                icon={<DropdownIcon />}
                options={[
                  ...organisationList(), // Include the organization list
                  ...(nextPageOrg
                    ? [
                        {
                          key: 'load-more',
                          text: (
                            <Button
                              type="button"
                              onClick={() => obtainBusinessList(nextPageOrg)}
                              disabled={isFetchingBusinesses}
                              loading={isFetchingBusinesses}
                              className={style.loadMoreButton}
                            >
                              Load More
                            </Button>
                          ),
                          disabled: true, // Prevent actual selection
                        },
                      ]
                    : []),
                ]}
                onChange={(e, { value }) => handleFilterChange('selectedOrganisation', value)}
              />

              <Dropdown
                placeholder="Category"
                className={`customDropdown3 ${style.statusDropdown}`}
                icon={<DropdownIcon />}
                options={[
                  ...categoryList,
                  ...(nextPage
                    ? [
                        {
                          key: 'load-more',
                          text: (
                            <Button
                              type="button"
                              onClick={() => obtainCategoryList(nextPage)}
                              disabled={isFetchingCategories}
                              loading={isFetchingCategories}
                              className={style.loadMoreButton}
                            >
                              Load More
                            </Button>
                          ),
                          disabled: true, // Prevent actual selection
                        },
                      ]
                    : []),
                ]}
                onChange={(e, { value }) => handleFilterChange('selectedCategory', value)}
              />
            </div>
            <div className={style.rightFilterWrapper}>
              {!isFiltersEmpty() && (
                <div>
                  <a href={logDownloadLink}>
                    <p className={style.downloadlogBtn}>
                      {logDowloadLoad ? (
                        <Loader inverted active inline="centered" size="small" />
                      ) : (
                        <>Dowload {downloadIcon()}</>
                      )}
                    </p>
                  </a>
                </div>
              )}
              <div className={style.moreFilter} onClickCapture={handleAdminFilter}>
                {filterIcon()} <span>More Filters</span>
              </div>
            </div>
          </div>
          <div>
            {isLoading ? (
              <div className={style.loaderContainer}>
                <Loader active inline="centered" size="medium" />
              </div>
            ) : isResponsive ? (
              renderCardList()
            ) : (
              renderList()
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default SystemActivityPage;
