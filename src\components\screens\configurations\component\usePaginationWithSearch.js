import { useState, useEffect, useCallback } from 'react';
import debounce from 'lodash/debounce';
import apiClient from '../../../services/apiClient';
import { Pagination } from 'semantic-ui-react';
import useUpdateEffect from '../../../global/hooks/useUpdateEffect';
import { useSearchParams } from 'react-router-dom';

const usePaginationWithSearch = ({
  url,
  queryParam = 'search',
  initialPage = 1,
  debounceDelay = 600,
  pageCount = 10,
  shouldDoInitialFetch = true,
  skipFetching = false,
  includeNameParam = false,
}) => {
  const [query, setQuery] = useState('');
  const [searchParams, setSearchParams] = useSearchParams();
  const currentPage = Number(searchParams.get('page')) || initialPage;
  const [loading, setLoading] = useState(!!url && shouldDoInitialFetch);
  const [error, setError] = useState(null);
  const [extraParams, setExtraParams] = useState({});
  const [response, setResponse] = useState(null);

  const fetchData = useCallback(
    debounce(async (page, searchTerm, otherParams, signal, skipLoading = false) => {
      if (!url) return;
      if (!skipLoading) setLoading(true);
      setError(null);

      try {
        const baseParams = {
          page,
          ...otherParams,
        };

        if (searchTerm && queryParam) {
          baseParams[queryParam] = searchTerm;
          if (includeNameParam && queryParam !== 'name') {
            baseParams.name = searchTerm;
          }
        }

        const filteredParams = Object.fromEntries(Object.entries(baseParams).filter(([, value]) => !!value));

        const response = await apiClient.get(url, {
          params: { search: searchTerm },
          signal,
        });
        setResponse(response);
        return response;
      } catch (err) {
        if (!signal.aborted) {
          setError(err.message || 'Failed to fetch data');
        }
      } finally {
        if (!skipLoading) setLoading(false);
      }
    }, debounceDelay),
    [url, pageCount, queryParam, includeNameParam]
  );

  useUpdateEffect(() => {
    const page = query ? 1 : currentPage;
    if (!skipFetching) {
      const controller = new AbortController();

      fetchData(page, query, extraParams, controller.signal);
      return () => controller.abort();
    }
  }, [query, extraParams, currentPage, url, skipFetching, fetchData]);

  useEffect(() => {
    if (shouldDoInitialFetch && !skipFetching) {
      const controller = new AbortController();
      fetchData(currentPage, query, extraParams, controller.signal);
      return () => controller.abort();
    }
  }, []);

  const handlePageChange = (page) => {
    if (page < 1) return;
    setSearchParams({ page: String(page) });
  };

  const results = response?.results || [];
  const count = response?.count || 0;
  const totalPages = Math.ceil(count / pageCount);

  return {
    results,
    count,
    totalPages,
    currentPage,
    loading,
    error,
    setPage: handlePageChange,
    query,
    setQuery,
    setExtraParams,
    refetch: (skipLoading = true) => {
      const controller = new AbortController();
      fetchData(currentPage, query, extraParams, controller.signal, skipLoading);
      return () => controller.abort();
    },
    PaginationComponent: totalPages > 1 && (
      <Pagination
        activePage={currentPage}
        totalPages={totalPages}
        onPageChange={(_, { activePage }) => handlePageChange(activePage)}
      />
    ),
    rawResponse: response,
  };
};

export default usePaginationWithSearch;
