@import '../../../../../assets/scss/main.scss';

.contentWrapper {
  width: 100%;
  max-height: 100%;
  min-height: 80%;
}

.overViewContent {
  display: flex;
  justify-content: space-between;
  padding: 1em;
  border-radius: 10px;
  background-color: #eaecf5;

  .overViewItem {
    width: 25%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;

    p {
      margin: 0.2em;
    }
  }

  @include for_media(mobileScreen) {
    flex-direction: column;
    gap: 1em;

    .overViewItem {
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      p {
        font-size: 1.1em !important;
      }
    }
  }

  .highLightContent {
    p {
      -webkit-text-stroke: 0.3px;
    }
  }

  .timeStamp {
    font-size: 1em !important;
    -webkit-text-stroke: 0 !important;
  }

  .positionValue {
    .amount {
      color: #2e7a31;
    }
  }

  .negativeValue {
    .amount {
      color: #f04438;
    }
  }

  .sideBarLine {
    height: 100%;
    position: absolute;
    height: 100%;
    width: 2px;
    background-color: #9ea5d1;
    left: -25px;

    @include for_media(mobileScreen) {
      height: 1px;
      width: 100%;
      left: 0;
      top: -0.5em;
    }
  }
}

.titlePart {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1em 0;

  h4 {
    margin: 0 !important;
    font-size: 1.4em !important;
  }

  .filterWrapper {
    width: 45%;
  }

  @include for_media(mobileScreen) {
    padding: 1.5em 0 !important;
  }
}

.filterWrapper {
  display: flex;
  margin: 1em 0;
  gap: 1em;
}

.sortFilter {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20%;
  min-width: 7em;
  background-color: #f5f5f5;
  border-radius: 13px;
  border: 1px solid #d5d7da;
  gap: 1em;
  cursor: pointer;

  svg {
    width: 22px;
    height: 22px;
  }

  p {
    font-size: 1.2em !important;
  }
}

.customInput {
  display: flex;
  align-items: center;
  gap: 1em;
  width: 80%;
  height: 3.7em;
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 13px;
  border: 1px solid #d5d7da;

  svg {
    width: 25px;
    height: 25px;
  }

  input {
    width: 85%;
    border: none !important;
    height: 2.5em;
    color: black !important;
    background-color: unset !important;
  }

  input:focus {
    outline: none !important;
    border: none !important;
  }

  input::placeholder {
    font-size: 1.2em !important;
  }

  @include for_media(mobileScreen) {
    width: 100%;
  }
}

.detailedContentWrapper {
  height: 45vh;
  overflow-y: auto;
  padding-right: 0.5em !important;

  @include for_media(mobileScreen) {
    padding-bottom: 5em;
  }
}

.detailedCardHeader {
  display: flex;
  justify-content: space-between;
  padding: 0.6em 1em;

  p {
    margin: 0;
    font-size: 1em !important;
    width: 20%;
    color: #717680;

    // text-align: center;
    @include for_media(mobileScreen) {
      font-size: 1em !important;
      -webkit-text-stroke: 0.3px;
      color: #535862 !important;
    }
  }

  @include for_media(mobileScreen) {
    div {
      p {
        width: 100%;
      }
    }

    .item {
      width: 33%;
    }
  }
}

.detailedContent {
  display: flex;
  justify-content: space-between;
  margin: 1em 0;
  padding: 1em;
  background-color: $white;
  border-radius: 10px;

  p {
    margin: 0;
    width: 20%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @include for_media(mobileScreen) {
      width: 33%;
      font-size: 0.95em !important;
    }
  }
}

.subContent {
  font-size: 1em !important;
}

.mobileDetailedContent {
  width: 33%;

  p {
    width: 100%;
    word-wrap: break-word;
    white-space: normal;
    @include for_media(mobileScreen) {
      font-size: 0.95em !important;
    }
  }
}

.amountContent {
  @include for_media(mobileScreen) {
    text-align: end;
    width: 40% !important;
    p {
      width: 80%;
      justify-content: flex-end !important;
    }
    span {
      width: 20%;
    }
  }
}

.journal_type {
  @include for_media(mobileScreen) {
    justify-content: center !important;
  }
}

.emptyFilterSearch {
  padding: 1em;
  text-align: center;
  font-size: 1.15em !important;
  color: #717680;
}

.emptyMsgTab {
  height: 35vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2em;
  align-items: center;

  @include for_media(mobileScreen) {
    text-align: center;
  }
}

.negativeAmount {
  color: red;
}

.positiveAmount {
  color: green;
}

.duedate {
  color: #717680 !important;
}

.amountContent {
  display: flex;
  gap: 1em;
  align-items: center;

  svg {
    height: 25px;
    width: 25px;
  }
}

.emptyMsg {
  height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2em;
  align-items: center;

  @include for_media(mobileScreen) {
    text-align: center;
    height: 45vh;
  }
}

.paginationWrapper {
  position: sticky;
  bottom: 0;
  display: flex !important;
  justify-content: flex-end !important;
  background-color: $white;
  padding: 1em;
}

@include for_media(mobileScreen) {
  .amountItem {
    text-align: center !important;
    width: 40% !important;
  }

  .voucherType {
    width: 30%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .invoiceItem {
    width: 30%;
  }
}
