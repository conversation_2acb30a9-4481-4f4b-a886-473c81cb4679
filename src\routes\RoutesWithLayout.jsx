import React from 'react';
import { Route } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import LayoutWrapper from '../layout/LayoutWrapper';
import { useAuth } from '../contexts/AuthContext';
import ComingSoon from '../components/global/components/ComingSoon';

const InvoicesCreationScreen = React.lazy(
  () => import('../components/screens/invoices-creation/InvoicesCreationScreen')
);
const InvoicesScreen = React.lazy(() => import('../components/screens/invoices/InvoicesScreen'));

// Auto Config
const AutoConfigScreen = React.lazy(() => import('../components/screens/auto-config/AutoConfigScreen'));
const SyncSettings = React.lazy(() => import('../components/screens/auto-config/sync-settings/SyncSettings'));
const General = React.lazy(() => import('../components/screens/auto-config/general/General'));
const Ledgers = React.lazy(() => import('../components/screens/auto-config/ledgers/Ledgers'));
const Supplier = React.lazy(() => import('../components/screens/auto-config/supplier/Supplier'));
const Item = React.lazy(() => import('../components/screens/auto-config/item/Item'));
const ItemMap = React.lazy(() => import('../components/screens/auto-config/item-map/ItemMap'));
const Texes = React.lazy(() => import('../components/screens/auto-config/texes/Texes'));

function RoutesWithLayout() {
  const { roleType } = useAuth();

  return (
    <Route
      path="/"
      element={
        <ProtectedRoute>
          <LayoutWrapper />
        </ProtectedRoute>
      }
    >
      {/* Default route for non-admin users */}
      {roleType !== 'admin' && <Route index element={<InvoicesCreationScreen />} />}

      {/* Main application routes */}
      <Route path="/invoices" element={<InvoicesScreen />} />
      <Route path="/tools" element={<ComingSoon title="Tools Coming Soon" description="We're building powerful tools to enhance your workflow. Stay tuned for exciting features!" />} />
      <Route path="/config" element={<AutoConfigScreen />}>
        <Route index element={<SyncSettings />} />
        <Route path="general" element={<General />} />
        <Route path="ledgers" element={<Ledgers />} />
        <Route path="supplier" element={<Supplier />} />
        <Route path="item" element={<Item />} />
        <Route path="item-maps" element={<ItemMap />} />
        <Route path="taxes" element={<Texes />} />
      </Route>
    </Route>
  );
}

export default RoutesWithLayout;
