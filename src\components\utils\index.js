import Cookies from 'js-cookie';
import { cookiedomain } from './constants';
import ls from 'local-storage';
import pdfIcon from '../../assets/Images/pdfIcon.png';
import docIcon from '../../assets/Images/doc.png';
import excelIcon from '../../assets/Images/xls.png';
import zipIcon from '../../assets/Images/zipIcon.png';
import rarIcon from '../../assets/Images/rarIcon.png';
import { BuildingIcon, DocumentWithRupeeIcon, EmailIcon, WhatsappIcon, WwwIcon } from '../../assets/svgs';
import style from './index.module.scss';
import { Image } from 'semantic-ui-react';
import { mapAiInvoiceStatus } from './aiUtils';

function updateUserInfo(token = null, role = 'normal', userId, businesses) {
  let val1 = token;
  const ck = Cookies.get('access_token', { domain: cookiedomain });
  if (ck != null) {
    val1 = ck;
    const now = new Date();
    const cookieObj = {
      data: val1,
      expiry: now.getTime() + 1209600000,
      role,
      userId,
      businesses,
    };
    ls.set('access_token', cookieObj);
    console.log('Thru cookie');
  } else if (token !== undefined && token !== null) {
    const now = new Date();
    const cookieObj = {
      data: token,
      expiry: now.getTime() + 1209600000,
      role,
      userId,
      businesses,
    };
    ls.set('access_token', cookieObj);
    console.log('Thru Params - post login', cookieObj);
  }
  if (val1 === undefined || val1 === null) {
    const cookieObj = ls.get('access_token');
    if (cookieObj !== undefined && cookieObj !== null) {
      const now = new Date();
      if (now.getTime() <= cookieObj.expiry) {
        val1 = cookieObj.data;
        cookieObj.expiry = now.getTime() + 1209600000;
        cookieObj.role = role;
        cookieObj.userId = userId;
        cookieObj.businesses = businesses;
        ls.set('access_token', cookieObj);
      } else {
        ls.remove('access_token');
        ls.remove('userName');
      }
    }
    console.log('Thru Local Store');
  }
}

function clearCookies() {
  let ck = Cookies.get('access_token', { domain: cookiedomain });
  console.log('before logout ck', ck, cookiedomain);
  Cookies.remove('access_token', { domain: cookiedomain });
  ck = Cookies.get('access_token', { domain: cookiedomain });
  ls.remove('access_token');
  ls.remove('userName');
  document.cookie = 'access_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
  document.location.href = '/login';
}

function processLogout(redirectToLogin = true) {
  Cookies.remove('access_token', { domain: cookiedomain });
  ls.remove('access_token');
  ls.remove('userName');
  ls.remove('selectedBusiness');
  ls.remove('recentSearches');
  ls.remove('globSelectedBusiness');
  document.cookie = 'access_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
  if (redirectToLogin) {
    document.location.href = '/login';
  }
  if (window.ReactNativeWebView) {
    window.ReactNativeWebView.postMessage('loggedOut');
  }
}

function getNonePreviewableFileTypePngIcon(fileUrl) {
  if (!fileUrl) return null;
  const parts = fileUrl.split('.');
  const fileType = parts[parts.length - 1];
  switch (fileType?.toLowerCase()) {
    case 'pdf':
      return { icon: pdfIcon, fileType: 'pdf' };
    case 'doc':
      return { icon: docIcon, fileType: 'doc' };
    case 'xls':
    case 'xlsx':
      return { icon: excelIcon, fileType: 'excel' };
    case 'docx':
      return { icon: docIcon, fileType: 'doc' };
    case 'zip':
      return { icon: zipIcon, fileType: 'zip' };
    case 'rar':
      return { icon: rarIcon, fileType: 'rar' };
    default:
      return null;
  }
}

function capitalizeWords(string) {
  if (!string) return '';
  return string.replace(/\b\w/g, (char) => char.toUpperCase());
}
const getPlatformIcon = (ticket) => {
  if (!ticket) return <DocumentWithRupeeIcon className={style.platformIcon} />;
  if (ticket.from_whatsapp) {
    return <WhatsappIcon className={style.platformIcon} />;
  } else if (ticket.from_email) {
    return <EmailIcon className={style.platformIcon} />;
  } else {
    return <WwwIcon className={style.platformIcon} />;
  }
};

function convertDateTimeFormat(createdAt, isIncludeTime = false) {
  if (!createdAt) return '';
  const date = new Date(createdAt);
  const day = date.getDate().toString().padStart(2, '0');
  const month = date.toLocaleString('en-US', { month: 'long' });
  const year = date.getFullYear();
  const hours = date.getHours() % 12 || 12;
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const period = date.getHours() >= 12 ? 'pm' : 'am';
  if (isIncludeTime) {
    return `${day} ${month}, ${year}, ${hours}:${minutes} ${period}`;
  }
  return `${day} ${month}, ${year}`;
}

function jsonKeyFormatLabel(key, useInstead = {}) {
  if (!key) return '';
  if (useInstead[key]) return useInstead[key];
  return key
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function renderOrganizationLogo(logoImgLink = null) {
  if (!logoImgLink) return <BuildingIcon />;
  return <Image src={logoImgLink} />;
}

const getStatusClass = (status, isAiStatus = false) => {
  if (!status) return '';
  const statusControl = isAiStatus ? mapAiInvoiceStatus(status) : status;
  switch (statusControl?.toLowerCase()) {
    case 'open':
    case 'new':
      return 'openStatus';
    case 'pending':
    case 'extracted':
      return 'pendingStatus';
    case 'closed':
    case 'exported':
      return 'closedStatus';
    case 'verified':
    case 'validated':
    case 'synced':
      return 'verifiedStatus';
    case 'deleted':
    case 'failed':
    case 'sync failed':
      return 'deletedStatus';
    case 'completed':
      return 'completedStatus';
    case 'duplicate':
      return 'duplicateStatus';
    case 'in process':
      return 'inProcessStatus';
    default:
      return '';
  }
};

const getStatusColor = (status) => {
  if (!status) return '';
  switch (status?.toLowerCase()) {
    case 'open':
      return '#2186D0';
    case 'pending':
      return '#4AC2FF';
    case 'closed':
      return '#293056';
    case 'verified':
      return '#EAECF5';
    case 'deleted':
      return '#FF6B6B';
    default:
      return '';
  }
};

const isValidValue = (value) => {
  if (value === null || value === undefined || Number.isNaN(value)) return false;
  if (typeof value === 'string' && value.trim() === '') return false;
  if (Array.isArray(value) && value.length === 0) return false;
  if (value instanceof Date) return !isNaN(value.getTime());
  if (value instanceof Map || value instanceof Set) return value.size > 0;
  if (typeof value === 'object') {
    if (Object.getOwnPropertyNames(value).length === 0 && Object.getOwnPropertySymbols(value).length === 0)
      return false;
  }
  return true;
};

const fileToPlainObject = (file) => {
  if (!file) return {};
  return {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified,
  };
};

const formatRelativeTime = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);

  if (diffInSeconds < 0) return 'now';

  const rtf = new Intl.RelativeTimeFormat(undefined, { numeric: 'auto' });

  if (diffInSeconds < 60) return rtf.format(0, 'seconds');
  if (diffInSeconds < 3600) return rtf.format(-Math.floor(diffInSeconds / 60), 'minutes');
  if (diffInSeconds < 86400) return rtf.format(-Math.floor(diffInSeconds / 3600), 'hours');
  if (diffInSeconds < 172800)
    return (
      'yesterday at ' +
      date.toLocaleTimeString(undefined, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      })
    );

  return date.toLocaleDateString(undefined, {
    day: 'numeric',
    month: 'short',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
};

const handleOpenInNewTab = (e, fileUrl) => {
  if (!fileUrl) return;
  e.preventDefault();
  if (fileUrl) {
    window.open(fileUrl, '_blank');
  } else {
    console.error('Invalid URL for opening in new tab');
  }
};

const transformToDropdownOptions = (
  items,
  { keyProp = 'id', valueProp = 'id', textProp = 'name', subOptions = '' } = {}
) => {
  if (!Array.isArray(items)) return [];
  return items.map((item) => ({
    key: item[keyProp] ?? '',
    value: item[valueProp] ?? '',
    text: item[textProp] ?? '',
    subOptions: item[subOptions] ?? '',
  }));
};

export {
  updateUserInfo,
  processLogout,
  clearCookies,
  capitalizeWords,
  convertDateTimeFormat,
  getNonePreviewableFileTypePngIcon,
  getPlatformIcon,
  isValidValue,
  jsonKeyFormatLabel,
  renderOrganizationLogo,
  getStatusClass,
  getStatusColor,
  fileToPlainObject,
  formatRelativeTime,
  handleOpenInNewTab,
  transformToDropdownOptions,
};
