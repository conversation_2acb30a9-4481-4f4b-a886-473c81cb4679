@import '../../../../../../assets/scss/main.scss';

.cashFlowTitleSection {
  display: flex;
  gap: 1em;
  svg {
    width: 35px;
    height: 35px;
  }
  div {
    p {
      margin: 0;
    }
    span {
      color: #717680;
    }
  }
}

.cashFlowHeader {
  display: flex;
  gap: 1em;
  align-items: center;
  justify-content: space-between;
}

.cashFlowContent {
  height: auto !important;
}

.cashFlowContainer {
  padding: 1.5em 0 !important;
  gap: 2em !important;
  padding-right: 0 !important;
}

.progressContainer {
  width: 100%;
  height: 15px;
  background-color: #eaecf5;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 1em;
}

.progressBar {
  height: 100%;
  background-color: $accentColor1;
  transition: width 0.3s ease-in-out;
}

.cashInfoContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1em 0;
  .cashInfo {
    div {
      display: flex;
      gap: 0.5em;
      align-items: center;
      p {
        color: #717680;
      }
      span {
        height: 12px;
        width: 12px;
        background-color: $accentColor1;
        border-radius: 35px;
      }
    }
  }
  .bankFlow {
    span {
      background-color: #eaecf5 !important;
    }
  }
}

.dashBoardView {
  display: flex;
  padding: 1.5em 0;
  gap: 1em;
  @include for_media(mobileScreen) {
    flex-direction: column;
    padding: 1em !important;
    .leftCardContent {
      width: 100%;
    }
  }
  .leftCardContent {
    width: 35%;
    .infoCard {
      padding: 1em;
      border-radius: 15px;
      background-color: $white;
      margin-bottom: 1em;
      .infoCardTitle {
        display: flex;
        gap: 1em;
        align-items: center;
        svg {
          width: 35px;
          height: 35px;
        }
      }
      .infoCardCount {
        padding: 1em 0.5em;
      }
    }
    @include for_media(mobileScreen) {
      width: 100% !important;
      display: flex;
      gap: 1em;
      p {
        font-size: 1em;
      }
    }
  }
  .rightCardContent {
    width: 65%;
    padding: 1em;
    border-radius: 10px;
    background-color: white;
    height: 400px;
    @include for_media(mobileScreen) {
      max-width: 100%;
      width: auto !important;
    }
  }
}
.subTitle {
  font-size: 1.4em !important;
  color: $primaryColor;
  margin: 0;
  padding: 0;
  margin-top: 1em !important;
  @include for_media(mobileScreen) {
    margin: 0 !important;
    padding: 0 1em;
  }
}

.chartWrapper {
  width: 85%;
  height: 85%;
  display: flex;
  align-items: center;
  canvas {
    max-width: 85%;
    max-height: 85%;
  }
}

.OverViewCard {
  padding: 1em;
  border-radius: 10px;
  background-color: $white;
  margin-bottom: 1em;
  .titleContent {
    display: flex;
    gap: 1em;
    justify-content: space-between;
    align-items: center;
  }
  p {
    margin: 0;
  }
  .leftContent {
    display: flex;
    gap: 1em;
    align-items: center;
    svg {
      width: 35px;
      height: 35px;
    }
  }
}

@include for_media(mobileScreen) {
  .titleContent {
    flex-direction: column;
    align-items: flex-start !important;
    .amount {
      font-size: 1.3em !important;
    }
  }
  .balanceInfo {
    padding: 1em;
    background-color: #eaecf5;
    border-radius: 10px;
    div {
      display: flex;
      justify-content: space-between;
      margin: 0.5em 0;
    }
    p {
      font-size: 1.1em !important;
    }
  }
  .amount {
    font-weight: 900;
  }
}

.loaderWrapper {
  height: 40vh;
  display: flex;
  justify-content: baseline;
  align-items: center;
  position: relative;
}

.loaderWrapper2 {
  height: 35vh;
  display: flex;
  justify-content: baseline;
  align-items: center;
  position: relative;
  background-color: white;
  border-radius: 15px;
}

.progressBarLoader {
  display: flex;
  justify-content: baseline;
  align-items: center;
  position: relative;
  height: 5.5em;
}
