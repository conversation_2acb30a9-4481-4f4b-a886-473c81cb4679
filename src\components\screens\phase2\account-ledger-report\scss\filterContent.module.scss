@import '../../../../../assets/scss/main.scss';

.dropdownWrapper {
  display: flex;
  align-items: center;
  padding-bottom: 1.5em;
  gap: 1em;

  @include for_media(mobileScreen) {
    flex-direction: column;
    align-items: unset;

    .dropDown {
      max-width: max-content;
    }
  }

  .dropDown {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 16em;
    padding: 0.5em 1em;
    border: 1px solid #9ea5d1;
    background-color: #eaecf5;
    border-radius: 10px;
    cursor: pointer;

    p {
      display: flex;
      align-items: center;
      gap: 0.8em;
      margin: 0;
    }

    svg {
      height: 25px;
      width: 25px;

      path {
        fill: $black;
      }
    }
  }
}

.accountTypeDropdown {
  min-width: 23em !important;

  .customInput {
    width: 100% !important;
  }
}

.accountDropDown {
  min-width: 25em !important;
  position: relative;

  @include for_media(mobileScreen) {
    max-width: 100% !important;
  }
}

.btnSection {
  display: flex;
  width: 100%;
  gap: 1em;
  justify-content: space-between;
  padding-top: 1em;
  border-top: 1px solid rgb(233, 233, 233);

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.5em;
    width: 48%;
    border-radius: 35px;
    font-size: 1.2em !important;
    cursor: pointer;
  }

  .clrBtn {
    background-color: #fdf7dd;
    border: 1px solid #f9e699;
    color: #7e6607;
  }

  .applyBtn2 {
    background-color: #d5d7da;
    color: $black;
  }
}

.filterPagination {
  display: flex;
  justify-content: center;
}

.activeDropdown {
  background-color: #363f72 !important;
  color: $white;

  svg {
    path {
      fill: white !important;
    }
  }
}

.appliedAccountList {
  position: absolute;
  height: 25px;
  width: 25px;
  border-radius: 100%;
  background-color: red;
  right: -10px;
  top: -10px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.selectedItems {
  max-width: 15em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
