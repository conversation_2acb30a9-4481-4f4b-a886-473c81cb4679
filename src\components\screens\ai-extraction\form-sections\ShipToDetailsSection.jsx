import React from 'react';
import <PERSON><PERSON>ield from './components/AiField';
import { getFieldAlertObject } from '../../../../components/utils/aiUtils';

const SECTION = 'ship_to_details';

function ShipToDetailsSection({ formData, isReadOnly, formAction }) {
  const data = formData[SECTION] || {};

  return (
    <>
      <div className="grid grid-cols-3 gap-4">
        {/* Party Name */}
        <AiField
          label="Party Name"
          isExactMatch={data?.exact_match?.party_name}
          alertObject={getFieldAlertObject(data, 'party_name')}
          className="only-1-column"
          type="text"
          name="party_name"
          id="party_name"
          value={data?.party_name ?? ''}
          onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'party_name', e.target.value)}
          disabled={isReadOnly}
        />

        {/* Party GST No. */}
        <AiField
          label="Party GST No."
          isExactMatch={data?.exact_match?.party_gst_no}
          alertObject={getFieldAlertObject(data, 'party_gst_no')}
          type="text"
          name="party_gst_no"
          id="party_gst_no"
          value={data?.party_gst_no ?? ''}
          onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'party_gst_no', e.target.value)}
          disabled={isReadOnly}
        />

        {/* State Name */}
        <AiField
          label="State Name"
          isExactMatch={data?.exact_match?.party_state_name}
          alertObject={getFieldAlertObject(data, 'party_state_name')}
          type="text"
          name="party_state_name"
          id="party_state_name"
          value={data?.party_state_name ?? ''}
          onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'party_state_name', e.target.value)}
          disabled={isReadOnly}
        />
      </div>

      {/* Party Address */}
      <AiField
        label="Party Address"
        isExactMatch={data?.exact_match?.party_address}
        alertObject={getFieldAlertObject(data, 'party_address')}
        className="only-1-column"
        name="party_address"
        id="party_address"
        value={data?.party_address ?? ''}
        onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'party_address', e.target.value)}
        disabled={isReadOnly}
      />
    </>
  );
}

export default ShipToDetailsSection;
