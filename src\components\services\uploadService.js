import apiClient from './apiClient'; // Assuming you have a configured Axios instance

const uploadService = {
  uploadFile: async (url, file, onProgress) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await apiClient.post(url, formData, {
        onUploadProgress: (progressEvent) => {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          if (onProgress) onProgress(percent);
        },
      });

      return response;
    } catch (error) {
      console.error('File upload failed:', error);
      throw error;
    }
  },
};

export default uploadService;
