import React from 'react';
import { <PERSON><PERSON>, <PERSON>, TextField, Typography } from '@mui/material';
import { Form, Formik } from 'formik';
import * as yup from 'yup';
import DynamicField from '../../../custom-components/DynamicField';
import { useAuth } from '../../../../contexts/AuthContext';
import apiClient from '../../../services/apiClient';
import { autoConfigUrls } from '../../../utils/apiurls';
import { useQuery } from '@tanstack/react-query';
import SearchAutocomplete from '../../../custom-components/SearchAutocomplete';
import { getErrorMessage } from '../../../utils/apiUtils';
import { toast } from 'react-toastify';
import LoadingWrapper from '../../../global/components/LoadingWrapper';

const gstValidationSchema = yup.object().shape({
  gst: yup
    .string()
    .matches(
      /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
      'Please enter a valid GST number (e.g., 22AAAAA0000A1Z5)'
    ),
});

function General() {
  const { globSelectedBusiness } = useAuth();

  const {
    data: businessInfo,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['businessInfo', globSelectedBusiness?.business_id],
    queryFn: () => apiClient.get(`${autoConfigUrls.getBusinessInfo}/${globSelectedBusiness?.business_id}`),
  });

  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    apiClient
      .patch(`${autoConfigUrls.getBusinessInfo}/${globSelectedBusiness?.business_id}`, values)
      .then(() => {
        toast.success('Business information updated successfully');
        resetForm({ values });
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(errorMessage);
      })
      .finally(() => {
        setSubmitting(false);
      });
  };

  const initialValues = {
    name: businessInfo?.name?.value,
    gst_type: businessInfo?.gst_type?.value,
    gst: businessInfo?.gst?.value,
    address: businessInfo?.address?.value,
    state_code: businessInfo?.state_code?.value,
  };

  return (
    <LoadingWrapper loading={isLoading} error={error}>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        enableReinitialize
        validationSchema={gstValidationSchema}
      >
        {({ values, setFieldValue, dirty, isSubmitting, errors, touched }) => (
          <Form className="flex flex-col gap-4 max-w-lg px-5 ml-10">
            <Typography variant="h6" fontWeight="bold" className="mb-5">
              Business Information
            </Typography>
            <DynamicField
              Component={TextField}
              label="Business Name"
              value={values.name}
              onChange={(e) => setFieldValue('name', e.target.value)}
              placeholder="Enter Business Name"
              config={businessInfo?.name}
            />
            <DynamicField
              Component={TextField}
              label="GST NO."
              value={values.gst}
              onChange={(e) => setFieldValue('gst', e.target.value)}
              fullWidth
              size="small"
              placeholder="Enter GST number"
              config={businessInfo?.gst}
              error={touched.gst && Boolean(errors.gst)}
              helperText={touched.gst && errors.gst}
            />

            <DynamicField
              Component={TextField}
              label="Address"
              value={values.address}
              onChange={(e) => setFieldValue('address', e.target.value)}
              placeholder="Enter Address"
              multiline
              minRows={3}
              maxRows={5}
              className="resize-none border border-gray-50"
              config={businessInfo?.address}
            />

            <DynamicField
              Component={SearchAutocomplete}
              label="State"
              value={values.state_code}
              onSelect={(value) => setFieldValue('state_code', value)}
              placeholder="Enter State"
              config={businessInfo?.state_code}
            />

            <DynamicField
              Component={SearchAutocomplete}
              label="GST Status"
              onSelect={(value) => setFieldValue('gst_type', value)}
              value={values.gst_type}
              config={businessInfo?.gst_type}
            />

            <div className="flex items-center gap-5">
              <Button variant="contained" className="w-fit" type="submit" disabled={!dirty || isSubmitting}>
                Save
              </Button>
              <Chip
                label={businessInfo?.config_status?.value ? 'Setup Complete' : 'Setup Incomplete'}
                color={businessInfo?.config_status?.value ? 'success' : 'error'}
                size="small"
                onClick={() => {}}
              />
            </div>
          </Form>
        )}
      </Formik>
    </LoadingWrapper>
  );
}

export default General;
