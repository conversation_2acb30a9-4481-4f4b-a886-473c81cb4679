import React from 'react';
import { useLocation } from 'react-router-dom';
import CostReport from './cost-report/CostReport';
import RevenueReport from './revenue-report/RevenueReport';
import ReceivableReport from './receivable-report/ReceivableReport';
import AccountOrLedgerReport from './account-ledger-report/AccountOrLedgerReport';
import InventoryReport from './inventory-report/InventoryReport';
import PayableReport from './payable-report/PayableReport';
import ProjectedCashFlow from './projected-cash-flow-report/ProjectedCashFlow';
import Header from '../../global/Header';
import NavigationBar from '../NavigationBar';
import style from './reportMainLayout.module.scss';
import { mediaBreakpoint } from '../../global/MediaBreakPointes';

const ReportMainLayout = () => {
  const location = useLocation();
  const path = location.pathname.split('/').pop();
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;

  const reportComponents = {
    'revenue-report': <RevenueReport />,
    'ledger-report': <AccountOrLedgerReport />,
    'expense-report': <CostReport />,
    'receivable-report': <ReceivableReport />,
    'accounts-payables': <PayableReport />,
    'inventory-report': <InventoryReport />,
    'cash-flow': <ProjectedCashFlow />,
  };

  if (isResponsive) {
    return reportComponents[path];
  }

  return (
    <>
      <Header />
      <div className={style.mainContainer}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>{reportComponents[path]}</div>
      </div>
    </>
  );
};

export default ReportMainLayout;
