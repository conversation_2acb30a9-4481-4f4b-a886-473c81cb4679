import { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { Image, Checkbox, Loader } from 'semantic-ui-react';
import style from './scss/checkBoxListing.module.scss';
import { NoImageProvidedIcon } from '../../assets/svgs';
import { Search, ChevronDown, ChevronRight } from 'lucide-react';
import useLoadMoreWithSearch from '../global/hooks/useLoadMoreWithSearch';

const CheckBoxListing = ({
  onSelectionChange,
  label = '-',
  paramsKey,
  defaultLogo = null,
  options = [],
  selectedValues = [],
  showSearch = false,
  searchPlaceholder = 'Search...',
  filterValueKey = 'value',
  url = null,
  searchParamName = 'name',
  transformOptionsObj = null,
  disabled = false,
  injectComponent = null,
}) => {
  const [isShowItems, setIsShowItems] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const DefaultLogo = defaultLogo ?? NoImageProvidedIcon;
  const fetchUrl = isShowItems && (url || null);

  const {
    setQuery,
    data: apiData,
    nextPageUrl,
    isLoading,
    error,
    LoadMoreButton,
  } = useLoadMoreWithSearch(fetchUrl, searchParamName, 600, true);

  useEffect(() => {
    if (isShowItems) {
      setQuery(searchQuery);
    }
  }, [searchQuery, isShowItems]);

  const transformedApiData = useMemo(() => {
    if (!apiData) return [];

    return apiData.map((item) => ({
      key: item?.[transformOptionsObj?.key] || item?.id || item?.value,
      value: item?.[transformOptionsObj?.value] || item?.value || item?.id,
      label: item?.[transformOptionsObj?.label] || item?.name || item?.text,
      src: item?.image || item?.src,
      className: item?.className,
    }));
  }, [apiData, transformOptionsObj]);

  const filteredLocalOptions = useMemo(() => {
    if (url) return [];

    return searchQuery
      ? options.filter((option) => option.label?.toLowerCase().includes(searchQuery.toLowerCase()))
      : [...options];
  }, [searchQuery, options, url]);

  const displayOptions = useMemo(() => {
    if (url) return transformedApiData;
    return filteredLocalOptions;
  }, [url, transformedApiData, filteredLocalOptions]);

  // Render item content (logo or className-based element)
  const renderOptionContent = useCallback((item) => {
    if (item?.className) {
      return <span className={item.className} />;
    }

    return (
      <div className={style.businessLogo}>
        {item?.src ? <Image src={item.src} alt={item.label} /> : <DefaultLogo />}
      </div>
    );
  }, []);

  return (
    <div className={`${style.optionsContainer} ${disabled ? style.disabled : ''}`}>
      <div className={style.optionsWrapper}>
        <div className={style.labelWrapper} onClick={() => !disabled && setIsShowItems(!isShowItems)}>
          <p>{label}</p>
          <div className={style.iconWrapper}>
            {isShowItems ? <ChevronDown className={style.icon} /> : <ChevronRight className={style.icon} />}
          </div>
        </div>

        {isShowItems && (
          <div className={style.listingWrapper}>
            {showSearch && (
              <div className={style.searchWrapper}>
                <Search className={style.searchIcon} />
                <input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={style.searchInput}
                />
              </div>
            )}

            {!isLoading ? (
              <>
                {displayOptions?.length > 0 ? (
                  displayOptions.map((option) => (
                    <label key={option.key || option.value} className={style.listingItemContainer}>
                      <div className={style.listingItem}>
                        {renderOptionContent(option)}
                        <p>{option.label}</p>
                      </div>
                      <Checkbox
                        checked={selectedValues?.includes(option[filterValueKey])}
                        onChange={(_, { checked }) => onSelectionChange?.(paramsKey, option[filterValueKey], checked)}
                      />
                    </label>
                  ))
                ) : (
                  <div className={style.noResults}>
                    <p>{error || 'No results found'}</p>
                  </div>
                )}
                {injectComponent && injectComponent}
                {nextPageUrl && !isLoading && <div className={style.loadMoreBtnWrapper}>{LoadMoreButton}</div>}
              </>
            ) : (
              <div className={style.loaderWrapper}>
                <Loader active inline="centered" size="small" />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(CheckBoxListing);
