import React from 'react';
import FilterContent from './FilterContent';
import AccountDropdownContent from './AccountDropdownContent';
import DetailedContent from './DetailedContent';
import RenderDownloadModal from '../../../../utils/reportUtils/RenderDownloadModal';
import ReportHeader from '../../../../global/components/ReportHeader';
import ReportTimelineDropdown from '../../../../global/components/ReportTimelineDropdown';
import { useAuth } from '../../../../../contexts/AuthContext';
import { getDateRange } from '../../../../utils/dateUtils';

const RenderOverallContent = ({
  accountTypeOptions,
  handleRefresh,
  setDownloadModal,
  setDateInfo,
  transactionDetails,
  accountPopupRef,
  handleSearch,
  searchTerm,
  accountTypeDropdown,
  selectedAccountType,
  handleAccountDropdown,
  handleAccountDropdownApply,
  handleClearAccountFilter,
  setAccountTypeDropdown,
  appliedAccountList,
  handleSelectDropdown,
  setActiveInput,
  handleDateSelection,
  dateInfo,
  activeInput,
  popupRef,
  selectedTimeline,
  handleCloseDateFilter,
  openDropdown,
  isLoading,
  overViewContent,
  handleSortDropdown,
  renderSortPopupContent,
  sortDropdown,
  setOpenDropdown,
  handleTransactionSearch,
  transactionSearch,
  transactionActivePage,
  handleTransactionPagination,
  downloadModal,
  handleDownload,
  handleClose,
  handleApplyFilter,
  handleAccountFilterPagination,
  activeFilterPage,
  handleDropdownList,
}) => {
  const { isMobileScreen } = useAuth();

  const accountDropdownProps = {
    accountPopupRef,
    handleSearch,
    searchTerm,
    accountTypeDropdown,
    accountTypeOptions,
    selectedAccountType,
    handleAccountDropdown,
    handleAccountFilterPagination,
    handleAccountDropdownApply,
    activeFilterPage,
    handleClearAccountFilter,
    setAccountTypeDropdown,
  };

  const timeLineModalProps = {
    handleSelectDropdown,
    setActiveInput,
    handleDateSelection,
    dateInfo,
    activeInput,
    setDateInfo,
    popupRef,
    selectedTimeline,
    handleCloseDateFilter,
  };

  return (
    <>
      <ReportHeader
        title="Account/Ledger Report"
        data={accountTypeOptions}
        handleRefresh={handleRefresh}
        setDownloadModal={setDownloadModal}
        isDisable={transactionDetails?.data?.length <= 0}
      />
      <FilterContent
        renderAccountTypeContent={<AccountDropdownContent {...accountDropdownProps} />}
        renderPopupContent={<ReportTimelineDropdown {...timeLineModalProps} />}
        appliedAccountList={appliedAccountList}
        setAccountTypeDropdown={setAccountTypeDropdown}
        accountTypeDropdown={accountTypeDropdown}
        handleDropdownList={handleDropdownList}
        selectedTimeline={selectedTimeline}
        dateInfo={dateInfo}
        openDropdown={openDropdown}
      />

      <DetailedContent
        isLoading={isLoading}
        overViewContent={overViewContent}
        handleSortDropdown={handleSortDropdown}
        renderSortPopupContent={renderSortPopupContent}
        sortDropdown={sortDropdown}
        setOpenDropdown={setOpenDropdown}
        handleTransactionSearch={handleTransactionSearch}
        transactionSearch={transactionSearch}
        transactionDetails={transactionDetails}
        transactionActivePage={transactionActivePage}
        handleTransactionPagination={handleTransactionPagination}
      />
      <RenderDownloadModal
        isOpen={downloadModal}
        onClose={() => setDownloadModal(false)}
        content={`Your Account/Ledger Report for the time period ${
          getDateRange(selectedTimeline?.value)?.startDate
        } to ${getDateRange(selectedTimeline?.value)?.endDate} is ready to download.`}
        downloadFunction={handleDownload}
      />
      {isMobileScreen && (
        <>
          <AccountDropdownContent {...accountDropdownProps} />
          <ReportTimelineDropdown
            {...timeLineModalProps}
            openDropdown={openDropdown}
            handleClose={handleClose}
            handleApplyFilter={handleApplyFilter}
          />
        </>
      )}
    </>
  );
};

export default RenderOverallContent;
