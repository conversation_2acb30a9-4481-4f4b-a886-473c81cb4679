@import '../../../../../assets/scss/main.scss';

.mobileViewContainer::-webkit-scrollbar {
  display: none;
  /* For Chrome, Safari, and Opera */
}

.mobileViewContainer {
  background-color: #f6f8fa;
  position: absolute;
  z-index: 1000;
  height: 100vh;
  width: 100%;
  padding: 1em;
  overflow-y: auto;
  -ms-overflow-style: none;
  /* For IE and Edge */
  scrollbar-width: none;

  /* For Firefox */
  .rightContentWrapper {
    width: 100%;

    .contentWrapper {
      flex-direction: column;
      padding: 1em 0;
    }

    .chartWrapper {
      width: 100%;
      background-color: unset;
      padding: 0;
    }

    .detailedWrapper {
      width: 100%;

      .detailsList {
        gap: 1em !important;
        display: block !important;
        // height: 45vh !important;
        // overflow-y: auto;
        padding-bottom: 6em;

        div {
          margin: 1em 0;

          p {
            font-size: 1.1em !important;
          }
        }
      }
    }
  }

  .popupContainer {
    padding: 1em !important;
  }

  .backIcon {
    padding: 1em 0;

    svg {
      height: 35px;
      width: 35px;
    }
  }

  .dropDown {
    width: auto !important;

    p {
      svg {
        display: none;
      }
    }
  }

  .applyBtn {
    padding: 1em 1em 0 1em !important;
  }

  .downloadBtnWrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1em;
    background-color: $white;
    z-index: 100;
  }

  .downloadBtn {
    background-color: #f6d659;
    color: #7e6607;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3.1em;
    gap: 1em;
    border-radius: 35px !important;
    width: 100%;
    cursor: pointer;
    -webkit-text-stroke: 0.25px;
    font-size: 1.2em !important;

    svg {
      path {
        fill: #7e6607;
      }
    }
  }

  .modalContent {
    h4 {
      padding: 0.5em 1em;
    }
  }
}

.highLightContent {
  -webkit-text-stroke: 0.3px;
}

.timeStamp {
  font-size: 1em !important;
  -webkit-text-stroke: 0 !important;
}

//changed these values to black as cretaing confusion for accountant
.positionValue {
  // color: #2E7A31;
  color: black;
}

.negativeValue {
  // color: #F04438;
  color: black;
}
