import React, { useMemo } from 'react';
import style from './scss/adminUnreadNotifications.module.scss';
import NotificationItem from '../../../NotificationItems';
import { useAuth } from '../../../../../contexts/AuthContext';

function AdminUnreadNotifications() {
  const { notifications, setNotifications } = useAuth();

  const unreadNotifications = useMemo(() => {
    return notifications.filter((notification) => !notification.is_read);
  }, [notifications]);

  return (
    <>
      <p className={style.heading}>Notifications</p>
      <div className={style.notificationContainer}>
        {unreadNotifications.length > 0 ? (
          unreadNotifications.map((info) => (
            <NotificationItem key={info?.id} info={info} admin={true} setNotifications={setNotifications} />
          ))
        ) : (
          <div className={style.content}>
            <p>No Notifications Yet</p>
            <p className={style.desc}>We’ll keep you updated as soon as there’s something new</p>
          </div>
        )}
      </div>
    </>
  );
}

export default AdminUnreadNotifications;
