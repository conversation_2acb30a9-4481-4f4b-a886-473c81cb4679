import React, { useMemo, useState } from 'react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { IconButton, Paper } from '@mui/material';
import { ZoomIn, ZoomOut, RestartAlt } from '@mui/icons-material';

function InvoicePreview({ fileUrl, loading = false }) {
  const [imgError, setImgError] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const isPdf = useMemo(() => fileUrl?.toLowerCase().endsWith('.pdf'), [fileUrl]);

  if (loading) {
    return <div className="blankSkeleton" />;
  }

  if (!fileUrl) {
    return (
      <div className="flex items-center justify-center h-full w-full text-gray-500">No invoice file to display</div>
    );
  }

  if (isPdf) {
    return (
      <object data={fileUrl} type="application/pdf" width="100%" height="100%">
        <p className="p-4 text-sm text-gray-600">
          Unable to display PDF.
          <br />
          <a href={fileUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">
            Click here to view or download
          </a>
        </p>
      </object>
    );
  }

  if (imgError) {
    return (
      <div className="flex items-center justify-center h-full w-full text-red-500 text-sm">
        Failed to load image.
        <button
          onClick={() => setImgError(false)}
          className="ml-2 px-2 py-1 border text-blue-500 border-blue-500 rounded hover:bg-blue-50"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <TransformWrapper
      initialScale={1}
      minScale={0.5}
      maxScale={4}
      doubleClick={{ disabled: false, mode: 'reset' }}
      wheel={{ step: 0.05 }}
      pinch={{ step: 0.05 }}
      onTransformed={(p) => setZoomLevel(Math.round(p.state.scale * 100))}
    >
      {({ zoomIn, zoomOut, resetTransform }) => (
        <>
          {/* Controls */}
          <Paper elevation={2} className="absolute top-2 right-2 z-10 flex flex-col gap-1 p-1">
            <IconButton size="small" onClick={() => zoomIn(0.25)} className="text-gray-600 hover:text-blue-600">
              <ZoomIn fontSize="small" />
            </IconButton>
            <IconButton size="small" onClick={() => zoomOut(0.25)} className="text-gray-600 hover:text-blue-600">
              <ZoomOut fontSize="small" />
            </IconButton>
            <IconButton size="small" onClick={() => resetTransform(1)} className="text-gray-600 hover:text-blue-600">
              <RestartAlt fontSize="small" />
            </IconButton>
          </Paper>

          {/* Zoom Level Display */}
          <Paper
            elevation={1}
            className="absolute top-2 left-2 z-10 px-2 py-1 text-xs text-gray-600 bg-white/70 backdrop-blur-sm select-none"
          >
            {zoomLevel}%
          </Paper>

          {/* Image Preview */}
          <TransformComponent>
            <img
              src={fileUrl}
              alt="Invoice preview"
              loading="lazy"
              onError={() => setImgError(true)}
              className="object-contain"
              draggable={false}
            />
          </TransformComponent>
        </>
      )}
    </TransformWrapper>
  );
}

export default InvoicePreview;
