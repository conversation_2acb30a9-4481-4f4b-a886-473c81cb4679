const sliceText = (text, sliceTill = 20) => {
  if (!text) return '';
  return text.length > sliceTill ? text.slice(0, sliceTill) + '...' : text;
};

const parseStringToValue = (val) => {
  if (typeof val !== 'string') return val;
  if (!isNaN(val) && val.trim() !== '') {
    return val.includes('.') ? parseFloat(val) : parseInt(val, 10);
  }
  if (val.toLowerCase() === 'true') return true;
  if (val.toLowerCase() === 'false') return false;
  return val;
};

function conditionEval(expression = '', context = null) {
  if (!expression || typeof expression !== 'string' || !context) return false;
  // Tokenize while preserving operators and operands with spaces
  // This splits on space but keeps '&&' and '||' as separate tokens
  const tokens = expression
    .replace(/\s*&&\s*/g, ' && ')
    .replace(/\s*\|\|\s*/g, ' || ')
    .split(' ')
    .filter(Boolean);

  function evalExpr(exprTokens) {
    let orIndex = exprTokens.indexOf('||');
    if (orIndex !== -1) {
      return evalExpr(exprTokens.slice(0, orIndex)) || evalExpr(exprTokens.slice(orIndex + 1));
    }

    let andIndex = exprTokens.indexOf('&&');
    if (andIndex !== -1) {
      return evalExpr(exprTokens.slice(0, andIndex)) && evalExpr(exprTokens.slice(andIndex + 1));
    }

    if (exprTokens.length !== 3) return false;

    const [field, operator, rawValue] = exprTokens;

    // Strip quotes if present in condition's value (single or double)
    const value =
      (rawValue.startsWith("'") && rawValue.endsWith("'")) || (rawValue.startsWith('"') && rawValue.endsWith('"'))
        ? rawValue.slice(1, -1)
        : rawValue;

    const actual = context[field];

    if (operator === '==') return String(actual) === String(value);
    if (operator === '!=') return String(actual) !== String(value);

    return false;
  }

  return evalExpr(tokens);
}

export { sliceText, parseStringToValue, conditionEval };
