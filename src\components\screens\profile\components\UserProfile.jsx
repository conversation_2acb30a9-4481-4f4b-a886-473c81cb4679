import React, { useState } from 'react';
import Avatar from 'react-avatar';
import { LogoutIcon } from '../../../../assets/svgs';
import style from './scss/UserProfile.module.scss';
import { processLogout } from '../../../utils';
import UserProfileSkeleton from '../../../skeletons/UserProfileSkeleton';
import ResetPasswordModal from '../../modals/ResetPasswordModal';

const UserProfile = ({ userData, userInfo, error, loading }) => {
  const [resetPasswordOpen, setResetPasswordOpen] = useState(false);

  return (
    <div className={style.detailWrapper}>
      <div className={style.detailsContainer}>
        {loading ? (
          <UserProfileSkeleton />
        ) : error ? (
          <p>{error}</p>
        ) : (
          <>
            <div className={style.profileWrapper}>
              <Avatar
                className={style.avatarProfile}
                color="#EAECF5"
                name={userData?.results[0]?.full_name || 'User'}
              />
              <h4>{userData?.results[0]?.full_name}</h4>
            </div>
            <hr />
            <div className={style.details}>
              <p className={style.label}>User ID</p>
              <p>{userData?.results[0]?.id || userInfo?.userId}</p>
              <p className={style.label}>Mobile Number</p>
              <p>{userData?.results[0]?.whatsapp || 'Not available'}</p>
            </div>
            <p className={style.resetBtn} onClick={() => setResetPasswordOpen(true)}>
              Reset Password
            </p>
            <p className={style.logoutBtn} onClickCapture={processLogout}>
              <LogoutIcon /> Logout
            </p>
            <ResetPasswordModal open={resetPasswordOpen} onClose={() => setResetPasswordOpen(false)} />
          </>
        )}
      </div>
    </div>
  );
};

export default UserProfile;
