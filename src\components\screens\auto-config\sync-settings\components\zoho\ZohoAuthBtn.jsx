import { Check, Loader2, X } from 'lucide-react';
import React, { useState } from 'react';
import { Button, Chip } from '@mui/material';
import { connectToZoho, disconnectFrom<PERSON>oho, zohoCallback } from '../../../../../services/syncSettingsServices';
import { useAuth } from '../../../../../../contexts/AuthContext';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../../../utils/apiUtils';
import { authPopup } from '../../../../../utils/authUtils';

function ZohoAuthBtn({ setIsConnected, isConnected, organizationId, region }) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const { globSelectedBusiness } = useAuth();
  const handleConnectDisconnect = () => {
    if (isConnected) {
      setIsDisconnecting(true);
      disconnectFromZoho(globSelectedBusiness?.business_id)
        .then(() => {
          setIsConnected(false);
          setIsDisconnecting(false);
          toast.success('Zoho disconnected successfully!');
        })
        .catch((err) => {
          const error = getErrorMessage(err);
          toast.error(error);
          setIsDisconnecting(false);
        });
    } else {
      setIsConnecting(true);
      const redirectUrl = window.location.href;
      connectToZoho(globSelectedBusiness?.business_id, organizationId, region)
        .then((res) => {
          const authUrl = res?.auth_url;
          if (!authUrl) throw new Error('No auth URL provided');
          authPopup(authUrl, redirectUrl)
            .then((res) => {
              const restAuthParams = res?.split('?')[1];
              if (!restAuthParams) throw new Error('No auth params provided');
              zohoCallback(globSelectedBusiness?.business_id, restAuthParams)
                .then(() => {
                  setIsConnected(true);
                  toast.success('Zoho connected successfully!');
                })
                .catch((err) => {
                  const error = getErrorMessage(err);
                  toast.error(error);
                  setIsConnecting(false);
                });
            })
            .catch((err) => {
              const error = getErrorMessage(err);
              toast.error(error);
            })
            .finally(() => {
              setIsConnecting(false);
            });
        })
        .catch((err) => {
          const error = getErrorMessage(err);
          toast.error(error);
          setIsConnecting(false);
        });
    }
  };

  return (
    <div className="flex items-center gap-4">
      <div
        data-tooltip-id={!organizationId && !isConnected ? 'tooltip' : undefined}
        data-tooltip-content={!organizationId && !isConnected ? 'Please enter organization ID' : undefined}
      >
        <Button
          onClick={handleConnectDisconnect}
          disabled={isConnecting || isDisconnecting || (!organizationId && !isConnected)}
          variant={isConnected ? 'contained' : 'contained'}
          color={isConnected ? 'error' : 'primary'}
          startIcon={isConnecting || isDisconnecting ? <Loader2 className="w-5 h-5 animate-spin" /> : null}
        >
          {isConnecting
            ? 'Connecting...'
            : isDisconnecting
              ? 'Disconnecting...'
              : isConnected
                ? 'Disconnect'
                : 'Connect'}
        </Button>
      </div>
      <Chip
        icon={isConnected ? <Check size={16} /> : <X size={16} />}
        label={isConnected ? 'Connected' : 'Not Connected'}
        color={isConnected ? 'success' : 'error'}
        variant="outlined"
        onClick={() => {}}
        sx={{
          fontWeight: 'bold',
          cursor: 'default',
          '& .MuiChip-icon': {
            color: 'inherit',
            marginLeft: '8px',
          },
        }}
      />
    </div>
  );
}

export default ZohoAuthBtn;
