import React from 'react';
import Bar<PERSON>hart from '../../../../global/Charts/BarChart';
import { Popup } from 'semantic-ui-react';
import { EmptyBox, SearchIcon, SortIcon } from '../../../../../assets/svgs';
import style from '../scss/detailedReport.module.scss';
import { LoadingWrapper } from '../../../../global/components';
import { formatAmount, isToday } from '../../../../utils/dateUtils';
import { useAuth } from '../../../../../contexts/AuthContext';
import ActiveTabContent from '../../../../global/components/ActiveTabContent';

const DetailedReportContent = ({
  activeTab,
  isLoading,
  receivableDetails,
  handlePagination,
  activePage,
  setActiveTab,
  handleDropdownList,
  openDropdown,
  setOpenDropdown,
  customerDetails,
  content,
  invoiceInfo,
  detailedLoading,
  searchTerm,
  handleSearch,
}) => {
  const chartKey = JSON.stringify(receivableDetails);

  const { isMobileScreen } = useAuth();

  const labels = receivableDetails?.data?.map((item) => {
    if (!item || typeof item.label !== 'string') return item?.label;

    // If it's not a valid date, return the label directly
    if (isNaN(Date.parse(item.label))) {
      return item.label;
    }

    const date = new Date(item.label);

    if (receivableDetails?.metadata?.granularity === 'date') {
      if (isToday(date)) {
        return 'Today';
      }
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
      });
    } else if (receivableDetails?.metadata?.granularity === 'month') {
      return date.toLocaleString('en-US', { month: 'short' });
    } else if (receivableDetails?.metadata?.granularity === 'week') {
      const weekNumber = Math.ceil(date.getDate() / 7);
      return `Week ${weekNumber} - ${date.toLocaleString('en-US', {
        month: 'short',
      })}`;
    }

    return item.label;
  });

  const barData = receivableDetails?.data?.map((item) => item.amount);
  const trendlineData = receivableDetails?.data?.map((item) => item.amount);

  const barColors = receivableDetails?.data?.map(
    (info) =>
      info?.label === 'Over Due'
        ? ['#ED5E1C', '#CF4000'] // Mobile gradient for Over Due
        : isToday(info?.label)
          ? ['#3498DB', '#005C9F'] // Mobile gradient for today
          : ['#CCE1EF', '#CCE1EF'] // Mobile gradient for other days
  );

  if (!isLoading && (receivableDetails?.data?.length === 0 || !receivableDetails?.data)) {
    return (
      <div className={style.emptyMsg}>
        <EmptyBox />
        <p>No data available for Receivable reports at this time</p>
      </div>
    );
  }

  return (
    <div className={style.contentWrapper}>
      <div className={style.chartWrapper}>
        <BarChart
          labels={labels}
          key={chartKey}
          barData={barData}
          trendlineData={trendlineData}
          barColors={barColors}
          // barColors={["#3357FF", "#FF33A1"]}
          barObj={{
            gradientAddColorStart: 'rgba(38, 51, 126, 1)',
            gradientAddColorStop: 'rgba(78, 91, 166, 1)',
            unit: 'K',
            currency: '₹',
            barThickness: 40,
            borderRadius: {
              topLeft: 4,
              topRight: 4,
            },
            order: 2,
          }}
          pagination={receivableDetails?.pagination}
          handlePagination={handlePagination}
          activePage={activePage}
          disableTrendline={true}
          trendlineObj={{
            label: 'Trendline',
            borderColor: 'rgba(247, 144, 9, 1)',
          }}
        />
      </div>
      <div className={style.detailedWrapper}>
        <div className={style.overAllInfo}>
          <div className={style.leftInfo}>
            <h4>Total Receivable </h4>
          </div>
          <div className={style.rightInfo}>
            <h4>{formatAmount(receivableDetails?.summary?.total_debit, true)}</h4>
          </div>
        </div>
        <div className={style.actionBtnWrappers}>
          <p
            className={activeTab === 'customer' ? style.activeTab : ''}
            onClickCapture={() => setActiveTab('customer')}
          >
            Customer View
          </p>
          <p className={activeTab === 'invoice' ? style.activeTab : ''} onClickCapture={() => setActiveTab('invoice')}>
            Invoice View
          </p>
        </div>
        <div className={style.detailsListContainer}>
          <div className={style.detailTitleContainer}>
            <h5>Detailed Report</h5>
            {(isMobileScreen || activeTab === 'invoice') && (
              <Popup
                className={style.popup}
                trigger={
                  <div className={style.sortFilter} onClick={() => handleDropdownList()}>
                    {' '}
                    <SortIcon />
                    <p>Sort</p>
                  </div>
                }
                content={content}
                position="bottom left"
                on="click"
                hoverable
                basic
                open={!isMobileScreen && openDropdown}
                onClose={() => {
                  setOpenDropdown(false);
                }}
              />
            )}
          </div>
          {activeTab === 'customer' && (
            <div className={style.filterWrapper}>
              <div className={style.customInput}>
                <SearchIcon />
                <input
                  type="text"
                  placeholder="Search by Ledger Name, Invoice Number, or Amount..."
                  onChange={handleSearch}
                  value={searchTerm}
                />
              </div>
              {!isMobileScreen && (
                <Popup
                  className={style.popup}
                  trigger={
                    <div className={style.sortFilter} onClick={() => handleDropdownList()}>
                      {' '}
                      <SortIcon />
                      <p>Sort</p>
                    </div>
                  }
                  content={content}
                  position="bottom left"
                  on="click"
                  hoverable
                  basic
                  open={!isMobileScreen && openDropdown}
                  onClose={() => {
                    setOpenDropdown(false);
                  }}
                />
              )}
            </div>
          )}

          <div className={`${activeTab === 'invoice' ? style.invoiceTab : ''} ${style.accordWrapper}`}>
            <LoadingWrapper loading={detailedLoading?.customer || detailedLoading?.invoice} minHeight={true}>
              <ActiveTabContent
                style={style}
                activeTab={activeTab}
                customerDetails={customerDetails}
                invoiceInfo={invoiceInfo}
                key={activeTab}
                reportName="receivable"
              />
            </LoadingWrapper>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailedReportContent;
