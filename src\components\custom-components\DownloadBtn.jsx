import { Download } from 'lucide-react';
import { toast } from 'react-toastify';
import { Button, Menu, MenuItem } from '@mui/material';
import axios from 'axios';
import ls from 'local-storage';
import { restbaseurl } from '../utils/constants';
import { getErrorMessage, extractFilenameFromHeaders } from '../utils/apiUtils';
import { useState } from 'react';

/**
 * DownloadBtn Component
 * 
 * @param {Object} props
 * @param {string} props.downloadUrl - The URL to download from (used when selectionOptions is null)
 * @param {Array<Object>} [props.selectionOptions=null] - Array of download options for the dropdown menu
 * @example
 * <DownloadBtn 
 *   selectionOptions={[
 *     { label: 'Download CSV', value: 'csv', url: '/api/export/csv' },
 *     { label: 'Download Excel', value: 'excel', url: '/api/export/excel' },
 *     { label: 'Download PDF', value: 'pdf', url: '/api/export/pdf' }
 *   ]}
 * />
 */
const DownloadBtn = ({ downloadUrl, onError, className = '', disabled = false, selectionOptions = null }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (e) => {
    if (selectionOptions) {
      setAnchorEl(e.currentTarget);
    } else {
      handleDownload(downloadUrl);
    }
  };

  const handleOptionSelect = (url) => {
    handleDownload(url);
    setAnchorEl(null);
  };

  const handleDownload = async (url) => {
    const headers = {
      Authorization: `Bearer ${ls.get('access_token')?.data}`,
    };
    axios
      .get(`${restbaseurl}/${url}`, {
        headers,
        responseType: 'blob',
      })
      .then((res) => {
        const blob = new Blob([res?.data], {
          type: 'text/csv',
        });
        const filename = extractFilenameFromHeaders(res.headers) || 'download.csv';
        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = filename;
        downloadLink.click();
        URL.revokeObjectURL(downloadLink.href);
        downloadLink.remove();
      })
      .catch((err) => {
        toast.error(getErrorMessage(err));
        onError?.(err);
      });
  };

  return (
    <>
      <Button
        id="export-data-btn"
        onClick={handleClick}
        disabled={disabled}
        variant="contained"
        startIcon={<Download size={16} />}
        className={className}
        aria-controls={open ? 'download-menu' : undefined}
        aria-haspopup={selectionOptions ? 'true' : undefined}
        aria-expanded={open ? 'true' : undefined}
      >
        Download
      </Button>

      {selectionOptions && (
        <Menu id="download-menu" anchorEl={anchorEl} open={open} onClose={() => setAnchorEl(null)}>
          {selectionOptions.map((option) => (
            <MenuItem key={option.value} onClick={() => handleOptionSelect(option.url)}>
              {option.label}
            </MenuItem>
          ))}
        </Menu>
      )}
    </>
  );
};

export default DownloadBtn;
