import React from 'react';
import TaskAltIcon from '@mui/icons-material/TaskAlt';

function TallySubmitBtn({ status, handleSubmit, isDisabled }) {
  return (
    (status === '1' || status === '2') && (
      <button
        id="validate-button"
        type="button"
        className="btn-submit"
        onClick={() => handleSubmit()}
        disabled={isDisabled}
      >
        <span className="flex items-center gap-2 flex-shrink-0">
          <TaskAltIcon />
          Validate
        </span>
      </button>
    )
  );
}

export default TallySubmitBtn;
