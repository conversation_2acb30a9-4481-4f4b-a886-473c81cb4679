import React, { useState, useEffect, useRef } from 'react';
import style from './scss/receivableReport.module.scss';
import { mediaBreakpoint } from '../../../global/MediaBreakPointes';
import GlobalService from '../../../services/GlobalServices';
import { resturls } from '../../../utils/apiurls';
import ls from 'local-storage';
import { useNavigate } from 'react-router-dom';
import { BackIcon, DownloadIcon } from '../../../../assets/svgs';
import RenderOverallContent from './components/RenderOverallContent';
import { LoadingWrapper } from '../../../global/components';
import ReportSortFilter from '../../../global/components/ReportSortFilter';

const ReceivableReport = () => {
  const [selectedTimeline, setTimeline] = useState(null);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [downloadModal, setDownloadModal] = useState(false);
  const [activePage, setActivePage] = useState(1);
  const [receivableDetails, setReceivableDetails] = useState({});
  const [activeTab, setActiveTab] = useState('customer');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [customerDetails, setCustomerDetails] = useState({});
  const [invoiceInfo, setInvoiceInfo] = useState({});
  const [originalList, setOriginalList] = useState({
    customerData: [],
    invoiceList: [],
  });
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const [detailedLoading, setDetailedLoading] = useState({
    customer: false,
    invoice: false,
  });
  const business_id = ls.get('selectedBusiness')?.business_id;

  const navigate = useNavigate();

  const obtainReceivablesReportDetails = () => {
    const storedReceivableData = ls.get('receivableData');
    const storedCustomerData = ls.get('customerData');
    const storedInvoiceData = ls.get('invoiceData');

    const currentDate = new Date();

    let shouldFetchCustomer = !storedCustomerData;
    let shouldFetchInvoice = !storedInvoiceData;

    if (storedReceivableData) {
      const { data, expiry_at } = storedReceivableData;
      const expiryDate = new Date(expiry_at);

      if (expiryDate < currentDate) {
        ls.remove('receivableData');
        fetchReceivablesReportData();
        shouldFetchCustomer = true;
        shouldFetchInvoice = true;
      } else {
        setReceivableDetails(data);
      }
    } else {
      fetchReceivablesReportData();
    }

    if (shouldFetchCustomer) {
      fetchCustomerContent();
    } else {
      setCustomerDetails(storedCustomerData);
      setOriginalList((prev) => ({
        ...prev,
        customerData: storedCustomerData?.data,
      }));
    }

    if (shouldFetchInvoice) {
      fetchInvoiceContent();
    } else {
      setInvoiceInfo(storedInvoiceData);
      setOriginalList((prev) => ({
        ...prev,
        invoiceList: storedInvoiceData?.data,
      }));
    }
  };

  const fetchReceivablesReportData = () => {
    setIsLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          const { expiry_at } = respdata;
          const dataToStore = {
            data: respdata,
            expiry_at,
          };
          ls.set('receivableData', dataToStore); // Store under a separate key
          setReceivableDetails(respdata);
          setIsLoading(false);
        }
      },
      `${resturls.obtainAccountReceivable}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  const fetchCustomerContent = () => {
    setDetailedLoading({ ...detailedLoading, customer: true });
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          setDetailedLoading({ ...detailedLoading, customer: false });
          setCustomerDetails(respdata);
          setOriginalList((prev) => ({
            ...prev,
            customerData: respdata?.data, // Ensure data is correctly assigned
          }));

          ls.set('customerData', respdata); // Store under a separate key
        }
      },
      `${resturls.obtainCustomerContent}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  const fetchInvoiceContent = () => {
    setDetailedLoading({ ...detailedLoading, invoice: true });
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata?.data) {
          setDetailedLoading({ ...detailedLoading, invoice: false });
          setInvoiceInfo(respdata);
          setOriginalList((prev) => ({
            ...prev,
            invoiceList: respdata?.data, // Ensure data is correctly assigned
          }));

          ls.set('invoiceData', respdata); // Store under a separate key
        }
      },
      `${resturls.obtainInvoiceContent}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  useEffect(() => {
    obtainReceivablesReportDetails();
  }, []);

  const handlePagination = (direction) => {
    const totalPage = receivableDetails?.pagination?.total_pages;
    let page = activePage;

    if (direction === 'right') {
      if (page < totalPage) {
        page = page + 1;
      }
    } else if (direction === 'left') {
      if (page > 1) {
        page = page - 1;
      }
    }
    setActivePage(page);
  };

  const handleSelectDropdown = (selectedOption) => {
    if (selectedOption?.value === selectedTimeline?.value) {
      setOpenDropdown(false);
      return;
    }

    setTimeline(selectedOption);
  };

  const handleApplySort = () => {
    let sortedData = [];
    if (activeTab === 'customer') {
      sortedData = [...customerDetails?.data];
    } else {
      sortedData = [...invoiceInfo?.data];
    }
    const selectedOption = selectedTimeline;

    if (selectedOption.value === 'earliestToLatest') {
      if (activeTab === 'customer') {
        sortedData = sortedData.map((customer) => ({
          ...customer,
          invoices: {
            ...customer.invoices,
            data: [...customer.invoices.data].sort((a, b) => new Date(a.due_date) - new Date(b.due_date)),
          },
        }));
      } else {
        sortedData = [...sortedData].sort((a, b) => new Date(a.due_date) - new Date(b.due_date));
      }
    } else if (selectedOption.value === 'latestToEarliest') {
      if (activeTab === 'customer') {
        sortedData = sortedData.map((customer) => ({
          ...customer,
          invoices: {
            ...customer.invoices,
            data: [...customer.invoices.data].sort((a, b) => new Date(b.due_date) - new Date(a.due_date)),
          },
        }));
      } else {
        sortedData = [...sortedData].sort((a, b) => new Date(b.due_date) - new Date(a.due_date));
      }
    } else if (selectedOption.value === 'lowToHigh') {
      if (activeTab === 'customer') {
        sortedData = [...sortedData].sort((a, b) => a.amount - b.amount);
        sortedData = sortedData.map((customer) => ({
          ...customer,
          invoices: {
            ...customer.invoices,
            data: [...customer.invoices.data].sort((a, b) => a.amount - b.amount),
          },
        }));
      } else {
        sortedData = [...sortedData].sort((a, b) => a.amount - b.amount);
      }
    } else if (selectedOption.value === 'highToLow') {
      if (activeTab === 'customer') {
        sortedData = [...sortedData].sort((a, b) => b.amount - a.amount); // Fix: Change the order here
        sortedData = sortedData.map((customer) => ({
          ...customer,
          invoices: {
            ...customer.invoices,
            data: [...customer.invoices.data].sort((a, b) => b.amount - a.amount),
          },
        }));
      } else {
        sortedData = [...sortedData].sort((a, b) => b.amount - a.amount);
      }
    }
    if (activeTab === 'customer') {
      setCustomerDetails({ ...customerDetails, data: sortedData });
    } else {
      setInvoiceInfo({ ...invoiceInfo, data: sortedData });
    }

    setOpenDropdown(false);
  };

  const handleClear = () => {
    if (activeTab === 'customer') {
      setCustomerDetails({ ...customerDetails, data: originalList?.customerData });
    } else {
      setInvoiceInfo({ ...invoiceInfo, data: originalList?.invoiceList });
    }
    setTimeline(false);
    setOpenDropdown(false);
  };

  const handleDropdownList = () => {
    setOpenDropdown(true);
  };

  const handleSearch = (e) => {
    const { value } = e.target;
    setSearchTerm(value);
    console.log(originalList, 'originalList');

    if (activeTab === 'customer') {
      const filteredList = originalList?.customerData?.filter((info) => {
        return (
          info?.ledger_name?.toLowerCase().includes(value.toLowerCase()) ||
          info?.invoices?.data?.some(
            (invoice) =>
              invoice?.invoice_number?.toString().includes(value) || invoice?.amount?.toString().includes(value)
          )
        );
      });

      setCustomerDetails({ ...customerDetails, data: filteredList });
    } else {
      const filteredList = originalList?.invoiceList?.filter((info) =>
        info?.ledger_name?.toLowerCase().includes(value.toLowerCase())
      );
      setInvoiceInfo({ ...invoiceInfo, data: filteredList });
    }
  };

  const handleRefresh = () => {
    ls.remove('receivableDetails');
    ls.remove('receivableData');
    ls.remove('customerData');
    ls.remove('invoiceData');
    obtainReceivablesReportDetails();
  };

  const handleDownload = () => {
    GlobalService.generalSelect(
      (respdata, error) => {
        if (error || !respdata) {
          console.error('Download failed:', error);
          alert('Failed to download the report. Please check your data or try again.');
          return; // Stop execution if error occurs or data is empty
        }

        setDownloadModal(false);

        // Convert response to a Blob with 'text/csv' MIME type
        const blob = new Blob([respdata], { type: 'text/csv' });
        const today_date = new Date().toISOString().split('T')[0];
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `Receivable_Report_${today_date}.csv`;

        // Append the link to the DOM and simulate a click to start the download
        document.body.appendChild(link);
        link.click();

        // Clean up by revoking the object URL
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      },
      `${resturls.downloadReceivableReport}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  const handleClose = () => {
    setOpenDropdown(false);
  };

  const commonProps = {
    handleRefresh,
    receivableDetails,
    setDownloadModal,
    activeTab,
    isLoading,
    handlePagination,
    activePage,
    setActiveTab,
    handleDropdownList,
    selectedTimeline,
    handleSelectDropdown,
    handleApplySort,
    handleClear,
    openDropdown,
    setOpenDropdown,
    invoiceInfo,
    detailedLoading,
    searchTerm,
    handleSearch,
    customerDetails,
    downloadModal,
    handleDownload,
  };

  if (isResponsive) {
    return (
      <LoadingWrapper loading={isLoading} minHeight={true}>
        <div className={style.mobileViewContainer}>
          <div className={style.backIcon}>
            <span onClickCapture={() => navigate('/reportsMenu')}>
              <BackIcon />
            </span>
          </div>
          <div className={style.rightContentWrapper}>
            <RenderOverallContent {...commonProps} />
          </div>
          <div className={style.downloadBtnWrapper}>
            <button className={style.downloadBtn} onClick={() => setDownloadModal(true)}>
              <DownloadIcon />
              Download Report
            </button>
          </div>
          <ReportSortFilter
            selectedTimeline={selectedTimeline}
            handleSelectDropdown={handleSelectDropdown}
            handleApplySort={handleApplySort}
            handleClear={handleClear}
            openDropdown={openDropdown}
            handleClose={handleClose}
          />
        </div>
      </LoadingWrapper>
    );
  }

  return (
    <>
      <LoadingWrapper loading={isLoading}>
        <RenderOverallContent {...commonProps} />
      </LoadingWrapper>
    </>
  );
};

export default ReceivableReport;
