import React, { useEffect, useState } from 'react';
import style from './scss/organisationUserList.module.scss';
import { useNavigate } from 'react-router-dom';

import {
  Image,
  Table,
  Card,
  Pagination,
  Checkbox,
  Input,
  Loader,
  Popup,
  Dropdown,
  Modal,
  Form,
  Button,
} from 'semantic-ui-react';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import {
  activationIcon,
  deleteIcon,
  downloadIcon,
  dropdownIcon,
  DropdownIcon,
  editIcon,
  lockIcon,
  mailIcon,
  orLineSvg,
  phoneIcon,
  superUserIcon,
  threeDotIcon,
} from '../global/Icons';
import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import infoIcon from '../../assets/Images/infoIcon.png';
//TODO: Fetching and adding part is left

const EmailServerConfPage = () => {
  // const [ businessList, setBusinessList ] = useState();
  const [activeModal, setActiveModal] = useState();
  const [activePopup, setActivePopup] = useState();
  const [selectedInfo, setSelectedInfo] = useState();
  const [serverInfo, setServerInfo] = useState([]);
  const [selectedId, setSelectedId] = useState();
  // const [serverInfo, setServerInfo] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1); // For total pages
  const [isNewFetching, setIsNewFetching] = useState(false);
  const [paginationInfo, setPaginationInfo] = useState();
  const [isLoading, setIsLoading] = useState(true);

  const [initialValues, setInitialValues] = useState({
    server: '',
    email: '',
    password: '',
  });
  // const navigate = useNavigate();

  // cosnt

  const statusOptions = [
    { key: 'open', value: 'Open', text: 'Open', className: style.openStatus },
    {
      key: 'pending',
      value: 'Pending',
      text: 'Pending',
      className: style.pendingStatus,
    },
    {
      key: 'closed',
      value: 'Closed',
      text: 'Closed',
      className: style.closedStatus,
    },
    {
      key: 'verified',
      value: 'Verified',
      text: 'Verified',
      className: style.verifiedStatus,
    },
    {
      key: 'deleted',
      value: 'Deleted',
      text: 'Deleted',
      className: style.deletedStatus,
    },
  ];
  const roleOptions = [
    { key: 'Business user', value: 'Business user', text: 'Business user' },
    {
      key: 'Business super user',
      value: 'Business superuser',
      text: 'Business super user',
    },
    { key: 'Accountant', value: 'Accountant', text: 'Accountant' },
    { key: 'Manager', value: 'Manager', text: 'Manager' },
  ];

  // useEffect

  const fetchEmailConf = (page = 1) => {
    setIsNewFetching(true);
    const url = `${resturls.emailserver}?page=${page}`;
    setIsLoading(true);

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.results) {
          setPaginationInfo(respdata);
          const { results, count } = respdata;

          // Calculate the total pages based on the count and results per page (assume 10 items per page)
          const pages = Math.ceil(count / 10); // Adjust 10 based on your API settings

          setServerInfo(results);
          setTotalPages(pages); // Set total pages
          setCurrentPage(page); // Set current page
          setIsNewFetching(false);
          setIsLoading(false);
        }
      },
      url,
      {},
      'GET'
    );
  };

  useEffect(() => {
    fetchEmailConf(1); // Fetch data on component mount
  }, []);

  useEffect(() => {
    console.log(serverInfo, 'serverinfo'); // Log serverInfo only after it updates
  }, [serverInfo]);

  // console.log(serverInfo, "serverinfo");

  const handleConfDelete = (id) => {
    console.log(id);
    setActiveModal(id);
    const url = `${resturls.emailserver}${id}/`;
    GlobalService.generalSelect(
      (respdata) => {
        console.log('deleted succesfully');
        fetchEmailConf(1);
        setActiveModal(false);
        toast.success(`${serverName} deleted successfully`);
      },
      url,
      {},
      'DELETE'
    );
  };

  const organisationList = [
    {
      name: 'smtp.mtltd.com',
      status: 'Configured',
      detail: 'smtp.mtltd.com:587',
    },
    {
      name: 'smtp.dotsstationery.com',
      status: 'Configured',
      detail: 'smtp.dotsstationery.com:465',
    },
    {
      name: 'smtp.abc-business.com',
      status: 'Not Configured',
      detail: 'smtp.abc-business.com:25',
    },
  ];

  const formik = useFormik({
    initialValues: initialValues,
    // validationSchema,
    onSubmit: (values) => {
      GlobalService.generalSelect(
        (respdata) => {
          // navigate('/')
        },
        `${resturls.createBusiness}`,
        values,
        'POST'
      );
    },
  });

  const handleActiveModal = (key, info) => {
    if (key === 'edit') {
      setSelectedInfo(info);
      // setInitialValues(info)
    }
    setActiveModal(key);
    setActivePopup(false);
  };

  const renderPopupContent = (info) => {
    const list = [
      {
        name: 'Edit Configuration',
        icon: editIcon(),
        key: 'edit',
        info: selectedId,
      },
      // {name: "Activate Server", icon: activationIcon(), key: "activate"},
      {
        name: 'Delete Configuration',
        icon: deleteIcon(),
        key: 'delete',
        info: selectedId,
      },
    ];
    return (
      <div className={style.popupList}>
        {list?.map((item) => {
          return (
            <div className={style.popupItem} onClickCapture={() => handleActiveModal(item?.key, item?.info)}>
              {item?.icon} <p>{item?.name}</p>
            </div>
          );
        })}
      </div>
    );
  };

  const handlePopupOpen = (id) => {
    setActivePopup(id); // Set the active popup to the clicked row's id
  };

  const handlePopupClose = () => {
    setActivePopup(null); // Close the popup when clicked outside
  };

  const renderList = () => {
    return (
      <>
        {/* Show loader when data is loading */}
        {isLoading ? (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(255, 255, 255, 0.8)', // Optional overlay
              zIndex: 1000,
            }}
          >
            <Loader active inline="centered" size="medium" />
          </div>
        ) : (
          <div>
            <Table basic="very">
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell className={style.subjectheaderRow}>Server Name</Table.HeaderCell>
                  <Table.HeaderCell>SMTP Details</Table.HeaderCell>
                  <Table.HeaderCell>Associated Business</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell></Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {serverInfo?.map((data) => (
                  <Table.Row key={data?.id}>
                    <Table.Cell className={style.userInfo}>
                      <p className={style.userName}>{data.server_name}</p>
                    </Table.Cell>
                    <Table.Cell>{data?.email_host}</Table.Cell>
                    <Table.Cell>{data?.business?.name || '-'}</Table.Cell>
                    <Table.Cell>
                      <span className={data?.is_active ? style.activeStatus : style.inActive}>
                        {data?.is_active ? 'Configured' : 'Not Configured'}
                      </span>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Pagination Component */}
        <div className={style.paginationWrapper}>
          {paginationInfo?.count > 10 && (
            <Pagination
              activePage={currentPage}
              totalPages={totalPages}
              onPageChange={(_, { activePage }) => {
                fetchEmailConf(activePage); // Fetch the page data when page is changed
              }}
              boundaryRange={1}
              siblingRange={1} // Adjust sibling range to show nearby pages
            />
          )}
        </div>
      </>
    );
  };

  const renderButtons = (content, className) => {
    console.log(className, 'classname');

    return (
      <div className={style.buttonWrapper}>
        <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
          Cancel
        </Button>
        <Button type="submit" className={`${className} ${style.nextBtn}`}>
          {content || 'Save Details'}
        </Button>
      </div>
    );
  };

  ///add new email server modal code start from below
  const [businessList, setBusinessList] = useState([]);
  const [businessCount, setBusinessCount] = useState(0);
  const [nextPage, setNextPage] = useState(1);
  const [isFetching, setIsFetching] = useState(false);
  const [selectedBusiness, setSelectedBusiness] = useState(null);
  const [serverName, setServerName] = useState('');
  const [emailHost, setEmailHost] = useState('');
  const [smtpPort, setSmtpPort] = useState('');
  const [imapHost, setImapHost] = useState('');
  const [imapPort, setImapPort] = useState('');
  const [emailHostUser, setEmailHostUser] = useState('');
  const [emailHostPassword, setEmailHostPassword] = useState('');
  const [selectserverInfo, setSelectserverInfo] = useState(null);
  // const [businessId, setBusinessId] = useState("")

  const [formData, setFormData] = useState({
    server_name: '',
    email_host: 'smtp.gmail.com',
    email_host_password: '',
    smtp_port: 587,
    imap_host: 'imap.gmail.com',
    imap_port: 993,
    email_host_user: '',
    email_use_tls: false,
    email_use_ssl: false,
    business: '',
  });

  const [EditformData, setEditFormData] = useState({
    server_name: '',
    email_host: '',
    email_host_password: '',
    smtp_port: '',
    imap_host: '',
    imap_port: '',
    email_host_user: '',
    email_use_tls: false,
    email_use_ssl: false,
    business: '',
  });

  // Initial state for errors
  const [errors, setErrors] = useState({});
  const handleSubmitNew = (e) => {
    e.preventDefault();
    const validationErrors = validate();
    setErrors(validationErrors);
    if (Object.keys(validationErrors).length === 0) {
      console.log('Form submitted successfully', formData);
      GlobalService.generalSelect(
        (respdata) => {
          console.log('Server info response:', respdata);
          setActiveModal(false);
          window.location.reload();
        },
        `${resturls.emailserver}`,
        formData,
        'POST'
      );
    }
  };

  const handleSubmitEdit = (e) => {
    e.preventDefault();
    const validationErrors = validate();
    // setErrors(validationErrors);
    // if (Object.keys(validationErrors).length === 0) {
    console.log('Form submitted successfully', formData);
    GlobalService.generalSelect(
      (respdata) => {
        console.log('Server info response:', respdata);
        setActiveModal(false);
        window.location.reload();
      },
      `${resturls.emailserver}${selectedId}/`,
      EditformData,
      'PATCH'
    );
    // }
  };

  const fetchBusinesses = (page = 1) => {
    setIsFetching(true);
    const url = `${resturls.getBusinesses}?page=${page}`;
    GlobalService.generalSelect(
      (respdata) => {
        const { results, count, next } = respdata;
        setBusinessList((prev) => [...prev, ...results]);
        setBusinessCount(count);
        if (next) {
          setNextPage(page + 1);
        } else {
          setNextPage(null);
        }

        setIsFetching(false);
      },
      url,
      {},
      'GET'
    );
  };

  console.log('nextPage', nextPage);

  const selectedServer = (id) => {
    const url = `${resturls.emailserver}${id}/`;
    GlobalService.generalSelect(
      (respdata) => {
        // const { results, count, next } = respdata;
        // const { results } = respdata;
        const serverData = respdata;
        setEditFormData({
          server_name: serverData.server_name || '',
          email_host: serverData.email_host || '',
          smtp_port: serverData.smtp_port || '',
          imap_host: serverData.imap_host || '',
          imap_port: serverData.imap_port || '',
          email_host_user: serverData.email_host_user || '',
          email_host_password: serverData.email_host_password || '',
          email_use_tls: serverData.email_use_tls || false,
          email_use_ssl: serverData.email_use_ssl || false,
          business: serverData.business?.id || '',
        });

        setIsFetching(false);
      },
      url,
      {},
      'GET'
    );
  };

  useEffect(() => {
    selectedServer(selectedId);
  }, [selectedId]);

  useEffect(() => {
    fetchBusinesses();
  }, []);

  const handleDropdownChange = (e, { value }) => {
    setFormData({
      ...formData,
      business: value,
    });
    console.log('Selected Business:', value);
  };

  const handleDropdownChangeEdit = (e, { value }) => {
    setEditFormData({
      ...EditformData,
      business: value,
    });
    console.log('Selected Business:', value);
  };

  const businessOptions = businessList.map((business) => ({
    key: business.business_id,
    text: business.business_name,
    value: business.business_id,
  }));

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleChangeEdit = (e) => {
    const { name, value, type, checked } = e.target;
    setEditFormData({
      ...EditformData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const validate = () => {
    const newErrors = {};
    if (!formData.server_name) newErrors.server_name = 'Server Name is required';
    if (!formData.email_host) newErrors.email_host = 'Email Host is required';
    if (!formData.smtp_port) newErrors.smtp_port = 'SMTP Port is required';
    if (!formData.imap_host) newErrors.imap_host = 'IMAP Host is required';
    if (!formData.imap_port) newErrors.imap_port = 'IMAP Port is required';
    if (!formData.business) newErrors.business = 'Business is required';
    if (!formData.email_host_user) newErrors.email_host_user = 'Email Host User is required';
    return newErrors;
  };

  console.log('Buisness_count', businessCount);
  const sslTlsOptions = [
    { key: 'tls', text: 'TLS', value: 'tls' },
    { key: 'ssl', text: 'SSL', value: 'ssl' },
  ];

  const getInitialEncryption = () => {
    if (EditformData.email_use_ssl) return 'ssl';
    if (EditformData.email_use_tls) return 'tls';
  };

  const ServerDetailsForm = () => (
    <>
      <div className={style.formContainer}>
        <Form.Field className={style.formField}>
          <label>
            Business <span style={{ color: 'red' }}>*</span>
          </label>
          <Dropdown
            placeholder="Select a Business"
            fluid
            className="customDropdown4"
            selection
            options={[
              ...businessOptions,
              ...(nextPage
                ? [
                    {
                      key: 'load-more',
                      text: (
                        <Button
                          type="button"
                          onClick={() => fetchBusinesses(nextPage)}
                          disabled={isFetching}
                          loading={isFetching}
                          className={style.loadMoreButton}
                        >
                          Load More
                        </Button>
                      ),
                      disabled: true, // Prevent actual selection
                    },
                  ]
                : []),
            ]}
            value={formData.business}
            onChange={handleDropdownChange}
            disabled={isFetching && !businessList.length}
          />
        </Form.Field>

        {/* Server Name */}
        <Form.Field className={style.formField}>
          <label>Server Name</label>
          <Form.Input
            id="server_name"
            name="server_name"
            placeholder="Enter Server Name"
            onChange={handleChange}
            value={formData.server_name}
            error={errors.server_name ? { content: errors.server_name, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* Server Email */}
        <Form.Field className={style.formField}>
          <label>SMTP HOST</label>
          <Form.Input
            id="email_host"
            name="email_host"
            placeholder="Enter smtp host"
            onChange={handleChange}
            value={formData.email_host}
            error={errors.email_host ? { content: errors.email_host, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* Server Password */}
        <Form.Field className={style.formField}>
          <label>Server Password</label>
          <Form.Input
            id="email_host_password"
            name="email_host_password"
            type="password"
            placeholder="Enter server password"
            onChange={handleChange}
            value={formData.email_host_password}
            error={errors.email_host_password ? { content: errors.email_host_password, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* Email Host User */}
        <Form.Field className={style.formField}>
          <label>Email User</label>
          <Form.Input
            id="email_host_user"
            name="email_host_user"
            placeholder="Enter Email User"
            onChange={handleChange}
            value={formData.email_host_user}
            error={errors.email_host_user ? { content: errors.email_host_user, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* SMTP Port */}
        <Form.Field className={style.formField}>
          <label>SMTP Port</label>
          <Form.Input
            id="smtp_port"
            name="smtp_port"
            type="number"
            placeholder="Enter SMTP port"
            onChange={handleChange}
            value={formData.smtp_port}
            error={errors.smtp_port ? { content: errors.smtp_port, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* IMAP Host */}
        <Form.Field className={style.formField}>
          <label>IMAP Host</label>
          <Form.Input
            id="imap_host"
            name="imap_host"
            placeholder="Enter IMAP Host"
            onChange={handleChange}
            value={formData.imap_host}
            error={errors.imap_host ? { content: errors.imap_host, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* IMAP Port */}
        <Form.Field className={style.formField}>
          <label>IMAP Port</label>
          <Form.Input
            id="imap_port"
            name="imap_port"
            type="number"
            placeholder="Enter IMAP Port"
            onChange={handleChange}
            value={formData.imap_port}
            error={errors.imap_port ? { content: errors.imap_port, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* Email Encryption */}
        <Form.Field className={style.formField}>
          <label>Email Encryption</label>
          <Dropdown
            id="email_encryption"
            name="email_encryption"
            placeholder="Select Encryption Type"
            fluid
            className="customDropdown4"
            selection
            options={sslTlsOptions}
            value={formData.email_encryption || 'none'}
            onChange={(e, { value }) => {
              // Update form data based on selected encryption
              setFormData((prevData) => ({
                ...prevData,
                email_encryption: value,
                email_use_tls: value === 'tls',
                email_use_ssl: value === 'ssl',
              }));
            }}
          />
        </Form.Field>
      </div>

      {/* Form Buttons */}
      {/* <div className={style.buttonWrapper}>
<Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
Cancel
</Button>
<Button type="submit" className={`${style.nextBtn}`}>
Save Details
</Button> */}
      {/* </div> */}
      {/* </div> */}
      <>
        {/* Form Buttons */}
        <div className={style.buttonWrapper}>
          <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
            Cancel
          </Button>
          <Button type="submit" className={`${style.nextBtn}`}>
            Save Details
          </Button>
        </div>
      </>
    </>
  );

  const EditServerDetails = () => (
    <>
      <div className={style.formContainer}>
        <Form.Field className={style.formField}>
          <label>Business</label>
          <Dropdown
            placeholder="Select a Business"
            fluid
            className="customDropdown4"
            selection
            options={[
              ...businessOptions,
              ...(nextPage
                ? [
                    {
                      key: 'load-more',
                      text: (
                        <Button
                          type="button"
                          onClick={() => fetchBusinesses(nextPage)}
                          disabled={isFetching}
                          loading={isFetching}
                          className={style.loadMoreButton}
                        >
                          Load More
                        </Button>
                      ),
                      disabled: true, // Prevent actual selection
                    },
                  ]
                : []),
            ]}
            value={EditformData.business}
            onChange={handleDropdownChangeEdit}
            disabled={isFetching && !businessList.length}
          />
        </Form.Field>

        {/* Server Name */}
        {/* Server Name */}
        <Form.Field className={style.formField}>
          <label>Server Name</label>
          <Form.Input
            id="server_name"
            name="server_name"
            placeholder="Enter Server Name"
            onChange={handleChangeEdit}
            value={EditformData.server_name}
            error={errors.server_name ? { content: errors.server_name, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* Server Email */}
        <Form.Field className={style.formField}>
          <label>SMTP HOST</label>
          <Form.Input
            id="email_host"
            name="email_host"
            placeholder="Enter smtp host"
            onChange={handleChangeEdit}
            value={EditformData.email_host}
            error={errors.email_host ? { content: errors.email_host, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* Email Host User */}
        <Form.Field className={style.formField}>
          <label>Email User</label>
          <Form.Input
            id="email_host_user"
            name="email_host_user"
            placeholder="Enter Email User"
            onChange={handleChangeEdit}
            value={EditformData.email_host_user}
            error={errors.email_host_user ? { content: errors.email_host_user, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* Server Password */}
        <Form.Field className={style.formField}>
          <label>Server Password</label>
          <Form.Input
            id="email_host_password"
            name="email_host_password"
            type="password"
            placeholder="Enter server password"
            onChange={handleChangeEdit}
            value={EditformData.email_host_password}
            error={errors.email_host_password ? { content: errors.email_host_password, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* SMTP Port */}
        <Form.Field className={style.formField}>
          <label>SMTP Port</label>
          <Form.Input
            id="smtp_port"
            name="smtp_port"
            type="number"
            placeholder="Enter SMTP port"
            onChange={handleChangeEdit}
            value={EditformData.smtp_port}
            error={errors.smtp_port ? { content: errors.smtp_port, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* IMAP Host */}
        <Form.Field className={style.formField}>
          <label>IMAP Host</label>
          <Form.Input
            id="imap_host"
            name="imap_host"
            placeholder="Enter IMAP Host"
            onChange={handleChangeEdit}
            value={EditformData.imap_host}
            error={errors.imap_host ? { content: errors.imap_host, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* IMAP Port */}
        <Form.Field className={style.formField}>
          <label>IMAP Port</label>
          <Form.Input
            id="imap_port"
            name="imap_port"
            type="number"
            placeholder="Enter IMAP Port"
            onChange={handleChangeEdit}
            value={EditformData.imap_port}
            error={errors.imap_port ? { content: errors.imap_port, pointing: 'below' } : null}
          />
        </Form.Field>

        {/* Email Encryption */}
        <Form.Field className={style.formField}>
          <label>Email Encryption</label>
          <Dropdown
            id="email_encryption"
            name="email_encryption"
            placeholder="Select Encryption Type"
            fluid
            className="customDropdown4"
            selection
            options={sslTlsOptions}
            value={getInitialEncryption()}
            onChange={(e, { value }) => {
              // Update form data based on selected encryption
              setEditFormData((prevData) => ({
                ...prevData,
                email_encryption: value,
                email_use_tls: value === 'tls',
                email_use_ssl: value === 'ssl',
              }));
            }}
          />
        </Form.Field>
        {/* </div> */}
      </div>
      <>
        {/* Form Buttons */}
        <div className={style.buttonWrapper}>
          <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
            Cancel
          </Button>
          <Button type="submit" className={`${style.nextBtn}`}>
            Save Details
          </Button>
        </div>
      </>
    </>
  );
  const renderModalContent = () => {
    if (activeModal === 'add') {
      return (
        <div className={style.modalContent}>
          <h5>Add Server</h5>
          <Form className={style.formWrapper} onSubmit={handleSubmitNew}>
            {ServerDetailsForm()}
            {/* {renderButtons()} */}
          </Form>
        </div>
      );
    }

    if (activeModal === 'edit') {
      return (
        <div className={style.modalContent}>
          <h5>Edit Server</h5>
          <Form className={style.formWrapper} onSubmit={handleSubmitEdit}>
            {EditServerDetails()}
            {/* {renderButtons()} */}
          </Form>
        </div>
      );
    }
    if (activeModal === 'delete') {
      return (
        <div className={`${style.autoHeight} ${style.modalContent}`}>
          <div className={style.imgWrapper}>
            <Image src={infoIcon} />
          </div>
          <div className={style.descContent}>
            <h5> {`Are you sure you want to delete “${serverName}” configuration?`}</h5>
            <p>This action cannot be undone, and the configuration will be permanently removed from the system.</p>
          </div>
          {/* {renderButtons('Deactivate', style.redBtn)}a */}
          <div className={style.buttonWrapper}>
            <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
              Cancel
            </Button>
            <Button type="submit" className={style.redBtn} onClick={() => handleConfDelete(selectedId)}>
              Delete
            </Button>
          </div>
        </div>
      );
    }
  };
  const [downloadLink, setDownloadLink] = useState('');
  const [logDowloadLoad, setlogDowloadLoad] = useState(false);

  const obtainLogDownloadLink = () => {
    setlogDowloadLoad(true);

    GlobalService.generalSelect(
      (respdata) => {
        const { download_link } = respdata;
        console.log(respdata);
        setDownloadLink(download_link);
        setlogDowloadLoad(false);
      },
      `${resturls.emailserverDownload}`,
      {},
      'GET'
    );
  };

  useEffect(() => {
    obtainLogDownloadLink();
  }, []);

  return (
    <div>
      <Header />
      <div className={style.bussinessListScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>

        <div className={style.rightContentWrapper}>
          <div className={style.headerPart}>
            <div>
              <h4>Email Server Configuration</h4>
            </div>
            <div className={style.btnWrapper}>
              {downloadLink ? (
                <a href={downloadLink}>
                  <div className={style.downloadBtn}>
                    {logDowloadLoad ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 50 50"
                        style={{
                          width: '24px',
                          height: '24px',
                          display: 'block',
                          margin: '0 auto',
                        }}
                      >
                        <circle
                          cx="25"
                          cy="25"
                          r="20"
                          fill="none"
                          stroke="black"
                          strokeWidth="4"
                          strokeDasharray="80"
                          strokeDashoffset="0"
                          strokeLinecap="round"
                          transform="rotate(-90 25 25)"
                        >
                          <animate
                            attributeName="stroke-dashoffset"
                            from="0"
                            to="160"
                            dur="1s"
                            repeatCount="indefinite"
                          />
                        </circle>
                      </svg>
                    ) : (
                      <>{downloadIcon()} Download</>
                    )}
                  </div>
                </a>
              ) : (
                <div className={style.downloadBtn}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 50 50"
                    style={{
                      width: '24px',
                      height: '24px',
                      display: 'block',
                      margin: '0 auto',
                    }}
                  >
                    <circle
                      cx="25"
                      cy="25"
                      r="20"
                      fill="none"
                      stroke="black"
                      strokeWidth="4"
                      strokeDasharray="80"
                      strokeDashoffset="0"
                      strokeLinecap="round"
                      transform="rotate(-90 25 25)"
                    >
                      <animate attributeName="stroke-dashoffset" from="0" to="160" dur="1s" repeatCount="indefinite" />
                    </circle>
                  </svg>
                </div>
              )}
            </div>
          </div>

          {/* Conditionally render loader or table */}
          {/* {isLoading ? (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(255, 255, 255, 0.8)", // Optional overlay
            zIndex: 1000,
          }}
        >
          <Loader active inline="centered" size="medium" />
        </div>
      ) : ( */}
          <div className={style.tableWrapper}>{renderList()}</div>
          {/* )} */}
        </div>
      </div>

      <Modal basic size="small" open={activeModal} onClose={() => setActiveModal(false)}>
        {renderModalContent()}
      </Modal>
    </div>
  );
};

export default EmailServerConfPage;
