import React from 'react';
import { useParams } from 'react-router-dom';
import TicketsListTable from '../../custom-components/TicketsListTable';
import LoadingWrapper from '../../global/components/LoadingWrapper';
import usePaginationWithSearch from '../../global/hooks/usePaginationWithSearch';
import { resturls } from '../../utils/apiurls';
import TitleArea from './common/TitleArea';
import style from './ticketListScreen.module.scss';
import LayoutWrapper from '../../generic-components/LayoutWrapper';

function TicketListScreen() {
  const { businessId } = useParams();

  const { data, loading, error, query, setQuery, PaginationComponent, setExtraParams } = usePaginationWithSearch({
    url: `${resturls.ticketList}${businessId ? `?business_id=${businessId}` : ''}`,
    queryParam: 'search_query',
  });

  return (
    <LayoutWrapper>
      <TitleArea query={query} setQuery={setQuery} setExtraParams={setExtraParams} />
      <LoadingWrapper loading={loading} error={error}>
        <div id="table-container" className={style.tableContainer}>
          <TicketsListTable
            data={data}
            isShowStickyCell={true}
            organization
            assignee
            reuqested_by
            status
            priority
            date
          />
        </div>
      </LoadingWrapper>
      <div className={style.paginationWrapper}>{PaginationComponent}</div>
    </LayoutWrapper>
  );
}

export default React.memo(TicketListScreen);
