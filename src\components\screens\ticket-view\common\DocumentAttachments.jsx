import React, { useState } from 'react';
import style from './scss/documentAttachments.module.scss';
import { getNonePreviewableFileTypePngIcon } from '../../../utils';
import { Image, Modal } from 'semantic-ui-react';
import { DownloadIcon } from '../../../../assets/svgs';
import SlicedText from '../../../generic-components/SlicedText';
import { useAuth } from '../../../../contexts/AuthContext';
import AiBtn from './AiBtn';

const DocumentAttachments = ({ ticketId, attachments, businessObj, isAiEnabled }) => {
  const [isShowGiantPreview, setIsShowGiantPreview] = useState(null);
  const { isMobileScreen } = useAuth();
  if (!attachments?.length && !attachments) return <></>;
  return (
    <div className={style.attachmentsContainer}>
      {attachments.map((item) => {
        const fileUrl = item?.document_url || item?.url;
        const fileName = fileUrl?.split('/').pop();
        const fileId = item?.id || item?.document_id;
        const isNonePreviewable = getNonePreviewableFileTypePngIcon(fileUrl);

        return isNonePreviewable ? (
          <div className={style.noneImageContainer} key={item?.document_id}>
            <div className={style.fileNameWrapper}>
              <div className={style.noneImageWrapper}>
                <Image src={isNonePreviewable?.icon} alt="File Icon" />
              </div>
              <SlicedText text={fileName} sliceTill={isMobileScreen ? 10 : 25} />
            </div>
            <div className={style.btnWrapper}>
              {isNonePreviewable?.fileType === 'pdf' && isAiEnabled && (
                <AiBtn
                  ticketId={ticketId}
                  aiExtractStatusCode={item?.ai_data_extracted ?? false}
                  fileName={fileName}
                  fileUrl={fileUrl}
                  fileId={fileId}
                  businessObj={businessObj}
                />
              )}
              <a
                href={fileUrl}
                download={fileName}
                target="_blank"
                rel="noopener noreferrer"
                className="px-3 py-2 rounded-md bg-gray-100"
              >
                <DownloadIcon />
              </a>
            </div>
          </div>
        ) : (
          <div className={style.imageWrapper} key={item?.document_id}>
            <Image onClick={() => setIsShowGiantPreview(fileUrl)} src={fileUrl} alt={fileName} />
            <a
              href={fileUrl}
              download={fileName}
              target="_blank"
              rel="noopener noreferrer"
              className="p-1 ml-2 h-8 w-8 flex justify-center items-center rounded-md bg-gray-100"
            >
              <DownloadIcon />
            </a>
            {isAiEnabled && (
              <AiBtn
                ticketId={ticketId}
                aiExtractStatusCode={item?.ai_data_extracted ?? false}
                fileName={fileName}
                fileUrl={fileUrl}
                fileId={fileId}
                businessObj={businessObj}
              />
            )}
          </div>
        );
      })}
      <Modal open={isShowGiantPreview} onClose={() => setIsShowGiantPreview(null)}>
        <Modal.Content>
          <div className="flex justify-center items-center">
            <Image src={isShowGiantPreview} alt={isShowGiantPreview} />
          </div>
        </Modal.Content>
      </Modal>
    </div>
  );
};

export default React.memo(DocumentAttachments);
