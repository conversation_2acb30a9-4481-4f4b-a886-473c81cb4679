import {
  rightArrowIcon,
  revenueReportIcon,
  costReportIcon,
  accountReceivableIcon,
  accountPayableIcon,
  inventoryReportIcon,
  ledgerReportIcon,
  cashFlowReportIcon,
} from '../../global/Icons';

export const REPORTS_MENU_DATA = [
  {
    title: 'Revenue Report',
    icon: revenueReportIcon,
    description: 'Overview of revenue for the given period.',
    redirectTo: '/revenue-report',
  },
  {
    title: 'Expense Report',
    icon: costReportIcon,
    description: 'Details of the expense associated with operations.',
    redirectTo: '/expense-report',
  },
  {
    title: 'Account/Ledger Report',
    icon: ledgerReportIcon,
    description: 'Detailed view of account transactions and balances.',
    redirectTo: '/ledger-report',
  },
  {
    title: 'Inventory Report',
    icon: inventoryReportIcon,
    description: 'Overview of inventory stock levels and values.',
    redirectTo: '/inventory-report',
  },
  {
    title: 'Total Receivable',
    icon: accountReceivableIcon,
    description: 'List of accounts that are yet to be collected.',
    redirectTo: '/receivable-report',
  },
  {
    title: 'Total Payable',
    icon: accountPayableIcon,
    description: 'List of accounts that need to be paid.',
    redirectTo: '/accounts-payables',
  },
  {
    title: 'Projected Cash Flow',
    icon: cashFlowReportIcon,
    description: 'Forecast of cash flow over a specific period.',
    redirectTo: '/cash-flow',
  },
];
