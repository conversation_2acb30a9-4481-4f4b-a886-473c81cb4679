import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import style from './scss/businessList.module.scss';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import { Image, Table, Card, Pagination, Dropdown, Modal, Form, Button, Input, Loader } from 'semantic-ui-react';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import { ticketLogIcon, DropdownIcon, flagIcon } from '../global/Icons';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import debounce from 'lodash/debounce';
import SearchableFormSelect from '../custom-components/SearchableFormSelect';

const TicketManagement = () => {
  const [TicketList, setTicketList] = useState();
  const [activeModal, setActiveModal] = useState();
  const [BuisnessSuperUserList, setBuisnessSuperUserList] = useState([]);
  const [BuisnesCount, setBuisnessCount] = useState(0);
  const [businessList, setBusinessList] = useState(0);
  const [isLoading, setIsLoading] = useState(1);
  const [step, setStep] = useState({
    step1: true,
    step2: false,
    active: 'step1',
  });
  const [paginationInfo, setPaginationInfo] = useState();
  const [activePage, setActivePage] = useState(1);
  const [filters, setFilters] = useState({
    searchText: '',
    selectedStatus: '',
    selectedOrganisation: '',
    selectedCategory: '',
  });
  const [categoryList, setCategoryList] = useState([]);

  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const navigate = useNavigate();
  const [nextPageOrg, setnextPageOrg] = useState(1);
  const [isFetchingCategories, setIsFetchingCategories] = useState(false);
  const [isFetchingBusinesses, setIsFetchingBusinesses] = useState(false);
  const [nextPage, setNextPage] = useState(null);

  useEffect(() => {
    obtainBusinessList(1);
    obtainCategoryList(1); // Fetch first page of businesses
  }, []);

  const obtainCategoryList = (page = 1) => {
    setIsFetchingCategories(true);
    const url = `${resturls.obtainCategortList}?page=${page}`;

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.results) {
          const { results, next } = respdata;
          const all = { key: 'all-category', value: '', text: 'All category' };
          const list = results.map((category) => ({
            key: category.id,
            value: category.name,
            text: category.name,
          }));

          const updatedList = page === 1 ? [all, ...list] : [...categoryList, ...list];
          setCategoryList(updatedList);

          if (next) {
            setNextPage(page + 1);
          } else {
            setNextPage(null);
          }

          setIsFetchingCategories(false);
        }
      },
      url,
      {},
      'GET'
    );
  };

  const obtainBusinessList = (page = 1) => {
    setIsFetchingBusinesses(true);
    const url = `${resturls.getBusinesses}?page=${page}`;

    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.results) {
          const { results, next } = respdata;
          const optionList = results.map((info) => ({
            key: info?.business_id,
            value: info?.business_id,
            text: info?.business_name,
          }));

          const updatedList =
            page === 1
              ? [
                  {
                    key: 'all_organisation',
                    value: '',
                    text: 'All organization',
                  },
                  ...optionList,
                ]
              : [...businessList, ...optionList];

          setBusinessList(updatedList);

          if (next) {
            setnextPageOrg(page + 1);
          } else {
            setnextPageOrg(null);
          }

          setIsFetchingBusinesses(false);
        }
      },
      url,
      {},
      'GET'
    );
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => {
      const updatedFilters = { ...prev, [key]: value };
      const queryParams = new URLSearchParams();

      if (updatedFilters.searchText) queryParams.append('search_query', updatedFilters.searchText);
      if (updatedFilters.selectedStatus) queryParams.append('status', updatedFilters.selectedStatus);
      if (updatedFilters.selectedOrganisation) queryParams.append('business_id', updatedFilters.selectedOrganisation);
      if (updatedFilters.selectedCategory) queryParams.append('category', updatedFilters.selectedCategory);
      setIsLoading(true);
      // Make the API call with the constructed query string
      GlobalService.generalSelect(
        (respdata) => {
          const { results } = respdata;
          setTicketList([]);
          setBuisnessCount(respdata.count); // Update the business count
          setTicketList(results); // Update the business list
          setPaginationInfo(respdata);
          setIsLoading(false);
        },
        `${resturls.ticketList}?${queryParams.toString()}`,
        {},
        'GET'
      );

      return updatedFilters;
    });
  };

  const handleSearch = useCallback(
    debounce((query) => {
      handleFilterChange('searchText', query);
    }, 0),
    []
  );

  const handleInputChange = (e) => {
    const query = e.target.value;
    handleSearch(query);
  };

  useEffect(() => {
    setIsLoading(true);
    GlobalService.generalSelect(
      (respdata) => {
        const { results } = respdata;
        setBuisnessCount(respdata.count);
        setTicketList(results);
        setPaginationInfo(respdata);
        setIsLoading(false);
      },
      `${resturls.ticketList}`,
      {},
      'GET'
    );
  }, []);

  useEffect(() => {
    GlobalService.generalSelect(
      (respdata) => {
        setBuisnessSuperUserList(respdata.data);
      },
      `${resturls.obtainCategoryWiseUser}?user_type=business_superusers`,
      {},
      'GET'
    );
  }, []);

  const validationSchema = Yup.object({
    name: Yup.string().required('Name is required').max(50, 'Name must be 50 characters or less'),

    gst: Yup.string()
      .required('GST number is required')
      .matches(/^[0-9]{15}$/, 'GST number must be exactly 15 digits'),

    email: Yup.string().required('Email is required').email('Invalid email address'),

    phone: Yup.string()
      .required('Phone number is required')
      .matches(/^[0-9]{10}$/, 'Phone number must be exactly 10 digits'),

    address: Yup.string().required('Address is required').max(200, 'Address must be 200 characters or less'),

    businessType: Yup.string().required('Business type is required'),

    primaryContact: Yup.string()
      .required('Primary contact is required')
      .matches(/^[0-9]{10}$/, 'Primary contact must be exactly 10 digits'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      gst: '',
      email: '',
      phone: '',
      address: '',
      businessType: '',
      primaryContact: '',
      business_superuser: '', // Added new initial field
      logo: '',
    },
    validationSchema,
  });
  const orgIcon = () => (
    <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2.26126 1.26126C2.78871 0.733816 3.50408 0.4375 4.25 0.4375H7.25C7.99592 0.4375 8.71129 0.733816 9.23874 1.26126C9.76618 1.78871 10.0625 2.50408 10.0625 3.25V13.1875H12.6875V11.2343C12.3518 11.1392 12.0427 10.9595 11.7916 10.7084C11.4048 10.3216 11.1875 9.79701 11.1875 9.25V7.75C11.1875 7.20299 11.4048 6.67839 11.7916 6.29159C12.1784 5.9048 12.703 5.6875 13.25 5.6875C13.797 5.6875 14.3216 5.9048 14.7084 6.29159C15.0952 6.67839 15.3125 7.20299 15.3125 7.75V9.25C15.3125 9.79701 15.0952 10.3216 14.7084 10.7084C14.4573 10.9595 14.1482 11.1392 13.8125 11.2343V13.1875H14.75C15.0607 13.1875 15.3125 13.4393 15.3125 13.75C15.3125 14.0607 15.0607 14.3125 14.75 14.3125H1.25C0.93934 14.3125 0.6875 14.0607 0.6875 13.75C0.6875 13.4393 0.93934 13.1875 1.25 13.1875H1.4375V3.25C1.4375 2.50408 1.73382 1.78871 2.26126 1.26126ZM2.5625 13.1875H5.1875V10.75C5.1875 10.4393 5.43934 10.1875 5.75 10.1875C6.06066 10.1875 6.3125 10.4393 6.3125 10.75V13.1875H8.9375V3.25C8.9375 2.80245 8.75971 2.37322 8.44324 2.05676C8.12678 1.74029 7.69755 1.5625 7.25 1.5625H4.25C3.80245 1.5625 3.37322 1.74029 3.05676 2.05676C2.74029 2.37323 2.5625 2.80245 2.5625 3.25V13.1875ZM4.4375 4.75C4.4375 4.43934 4.68934 4.1875 5 4.1875H6.5C6.81066 4.1875 7.0625 4.43934 7.0625 4.75C7.0625 5.06066 6.81066 5.3125 6.5 5.3125H5C4.68934 5.3125 4.4375 5.06066 4.4375 4.75ZM13.25 6.8125C13.0014 6.8125 12.7629 6.91127 12.5871 7.08709C12.4113 7.2629 12.3125 7.50136 12.3125 7.75V9.25C12.3125 9.49864 12.4113 9.7371 12.5871 9.91291C12.7629 10.0887 13.0014 10.1875 13.25 10.1875C13.4986 10.1875 13.7371 10.0887 13.9129 9.91291C14.0887 9.7371 14.1875 9.49864 14.1875 9.25V7.75C14.1875 7.50136 14.0887 7.2629 13.9129 7.08709C13.7371 6.91127 13.4986 6.8125 13.25 6.8125ZM4.4375 7.75C4.4375 7.43934 4.68934 7.1875 5 7.1875H6.5C6.81066 7.1875 7.0625 7.43934 7.0625 7.75C7.0625 8.06066 6.81066 8.3125 6.5 8.3125H5C4.68934 8.3125 4.4375 8.06066 4.4375 7.75Z"
        fill="#717680"
      />
    </svg>
  );

  const renderLogo = (logo) => {
    if (logo) {
      return <Image src={logo} />;
    }
    return orgIcon();
  };

  const formatDate = (isoDateString) => {
    const date = new Date(isoDateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'Open':
        return style.openStatus; // Class for open status
      case 'Pending':
        return style.pendingStatus; // Class for pending status
      case 'Closed':
        return style.closedStatus; // Class for closed status
      case 'Verified':
        return style.verifiedStatus; // Class for verified status
      case 'Deleted':
        return style.deletedStatus; // Class for deleted status
      default:
        return ''; // No status class if undefined or empty
    }
  };

  const renderList = () => {
    const itemsPerPage = 10;
    const totalPages = Math.ceil(paginationInfo?.count / itemsPerPage);
    const handlePaginationChange = (e, { activePage }) => {
      setActivePage(activePage);
      const queryParams = new URLSearchParams();
      if (filters.searchText?.length > 0) queryParams.append('search_query', filters.searchText);
      if (filters.selectedStatus?.length > 0) queryParams.append('status', filters.selectedStatus);
      if (filters.selectedOrganisation?.length > 0) queryParams.append('business_id', filters.selectedOrganisation);
      if (filters.selectedCategory?.length > 0) queryParams.append('category', filters.selectedCategory);
      const endpoint = `${resturls.ticketList}?page=${activePage}`;
      setIsLoading(true);
      GlobalService.generalSelect(
        (respdata) => {
          if (respdata && respdata.results) {
            setTicketList(respdata.results);
            setPaginationInfo(respdata);
            setIsLoading(false);
          } else {
            console.warn('No results found in response:', respdata);
            setTicketList([]);
          }
        },
        `${endpoint}&${queryParams}`,
        {},
        'GET'
      );
    };
    if (TicketList?.length === 0) {
      return (
        <div className={style.noMatchMsg}>
          <p>No matches found</p>
        </div>
      );
    }
    return (
      <>
        <div className={`${style.ticketTable} ${style.tableWrapper}`}>
          <Table basic="very">
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell className={style.subjectheaderRow}>Ticket Subject & ID</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Organization</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Client Name & Date of creation</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Category</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Sub category</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Status</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Priority</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Assigned to</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}>Last Updated At</Table.HeaderCell>
                <Table.HeaderCell className={style.subjectheaderRow}></Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {TicketList?.map((data) => (
                <Table.Row onClick={() => navigate(`/ticketDetails/${data?.id}`)}>
                  <Table.Cell>
                    <div className={style.ticketSubject}>
                      <div className={style.logo}>{ticketLogIcon()}</div>
                      <div className={style.businessName}>
                        <p>{data?.subject}</p>
                        <span className={style.id_status}>#{data?.id}</span>
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div className={style.businessInfo}>
                      <div className={style.logo}>{renderLogo(data?.business_image)}</div>
                      {data?.business?.business_name}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div className={style.clientInfo}>
                      <p>{data?.created_by}</p>
                      <span className={style.id_status}>{data?.created_at && formatDate(data?.created_at)}</span>
                    </div>
                  </Table.Cell>
                  <Table.Cell style={{ textAlign: 'center' }}>
                    <span className={`${data?.category?.name && style.category}`}>{data?.category?.name || '-'}</span>
                  </Table.Cell>
                  <Table.Cell style={{ textAlign: 'center' }}>
                    <span className={`${data?.sub_category?.name && style.category}`}>
                      {data?.sub_category?.name || '-'}
                    </span>
                  </Table.Cell>
                  <Table.Cell>
                    <span className={`${style.status} ${getStatusClass(data?.status)}`}>{data?.status || '-'}</span>
                  </Table.Cell>
                  <Table.Cell>
                    {data?.priority === 'High priority' && <div className={style.flag}>{flagIcon()}</div>}
                  </Table.Cell>
                  <Table.Cell>{data?.assign_to?.user_full_name || '-'}</Table.Cell>
                  <Table.Cell>{(data?.updated_at && formatDate(data?.updated_at)) || '-'}</Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
        <div className={style.paginationWrapper}>
          {paginationInfo?.count > 10 && TicketList?.length > 0 && (
            <Pagination activePage={activePage} totalPages={totalPages} onPageChange={handlePaginationChange} />
          )}
        </div>
      </>
    );
  };
  const Superuseroptions =
    BuisnessSuperUserList?.length > 0 &&
    BuisnessSuperUserList?.map((user) => ({
      key: user.user_id,
      value: user.user_id,
      text: user.full_name,
    }));

  const renderCardList = () => {
    return (
      <div className={`${style.ticketTable} ${style.tableWrapper}`}>
        <div className={style.ticketList}>
          {TicketList?.map((data) => (
            <Card className={`${style.ticketCard}`}>
              <Card.Content className={style.ticketContent}>
                <div className={style.rightContent}>
                  <h5>{data?.business_name}</h5>
                  <p>{data?.business_id}</p>
                  {data?.accountant_name && (
                    <p>
                      {data?.accountant_name || '-'}
                      {`  (Accountant)`}
                    </p>
                  )}
                </div>
                <div className={style.leftContent}>{renderLogo(data?.business_image)}</div>
              </Card.Content>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const TicketStatus = [
    { key: 'All', value: '', text: 'All' },
    { key: 'open', value: 'Open', text: 'Open', className: style.openStatus },
    {
      key: 'pending',
      value: 'Pending',
      text: 'Pending',
      className: style.pendingStatus,
    },
    {
      key: 'closed',
      value: 'Closed',
      text: 'Closed',
      className: style.closedStatus,
    },
    {
      key: 'verified',
      value: 'Verified',
      text: 'Verified',
      className: style.verifiedStatus,
    },
    {
      key: 'deleted',
      value: 'Deleted',
      text: 'Deleted',
      className: style.deletedStatus,
    },
  ];

  const renderCreateForm = () => {
    if (step?.active === 'step1') {
      return (
        <div className={style.formContainer}>
          <Form.Field className={style.formField}>
            <label>Business Name</label>
            <Form.Input
              id="name"
              name="name"
              placeholder="Enter business name"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.name}
              error={
                formik.touched.name && formik.errors.name ? { content: formik.errors.name, pointing: 'below' } : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>GST Number</label>
            <Form.Input
              id="gst"
              name="gst"
              placeholder="Enter GST"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.gst}
              error={formik.touched.gst && formik.errors.gst ? { content: formik.errors.gst, pointing: 'below' } : null}
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Address</label>
            <Form.Input
              id="address"
              name="address"
              placeholder="Enter address"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.address}
              error={
                formik.touched.address && formik.errors.address
                  ? { content: formik.errors.address, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>

          <Form.Field className={style.formField}>
            <label>Google Map Link</label>
            <Form.Input
              id="google_map_link"
              name="google_map_link"
              placeholder="Enter Google Map Link"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.google_map_link}
              error={formik.touched.gst && formik.errors.gst ? { content: formik.errors.gst, pointing: 'below' } : null}
            />
          </Form.Field>
          <Form.Field className={style.formField}>
            <label>Business Type</label>
            <Form.Input
              id="businessType"
              name="businessType"
              placeholder="Enter businessType"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.businessType}
              error={
                formik.touched.businessType && formik.errors.businessType
                  ? { content: formik.errors.businessType, pointing: 'below' }
                  : null
              }
            />
          </Form.Field>
        </div>
      );
    }
    return (
      <div className={style.formContainer}>
        <Form.Field className={style.formField}>
          <label>Phone Number</label>
          <Form.Input
            id="phone"
            name="phone"
            placeholder="Enter Phone Number"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.phone}
            error={
              formik.touched.phone && formik.errors.phone ? { content: formik.errors.phone, pointing: 'below' } : null
            }
          />
        </Form.Field>

        <Form.Field className={style.formField}>
          <label>Email</label>
          <Form.Input
            id="email"
            name="email"
            placeholder="Enter email"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.email}
            error={
              formik.touched.email && formik.errors.email ? { content: formik.errors.email, pointing: 'below' } : null
            }
          />
        </Form.Field>

        <Form.Field className={style.formField}>
          <label>Business Superuser</label>
          <Dropdown
            placeholder="Select a Superuser"
            fluid
            search
            selection
            options={Superuseroptions}
            onChange={(e, { value }) => formik.setFieldValue('business_superuser', value)}
            value={formik.values.business_superuser}
          />
          {formik.errors.business_superuser && <div>{formik.errors.business_superuser}</div>}
        </Form.Field>
      </div>
    );
  };

  const renderButtons = () => {
    if (step?.active === 'step1') {
      return (
        <div className={style.buttonWrapper}>
          <Button type="button" className={style.cancel} onClick={() => setActiveModal(false)}>
            Cancel
          </Button>
          <Button
            type="button"
            className={style.nextBtn}
            onClick={() => setStep({ ...step, active: 'step2', step2: true })}
          >
            Next
          </Button>
        </div>
      );
    }
    return (
      <div className={style.buttonWrapper}>
        <Button className={style.cancel} type="button" onClick={() => setActiveModal(false)}>
          Cancel
        </Button>
        <div className={style.subBtnWrapper}>
          <Button type="button" className={style.backBtn} onClick={() => setStep({ ...step, active: 'step1' })}>
            Back
          </Button>
          <Button
            type="submit"
            onClick={() => {
              const updatedValues = {
                ...formik.values,
                business_superuser: Number(formik.values.business_superuser), // Convert to number
              };
              GlobalService.generalSelect(
                (respdata) => {
                  setActiveModal(false);
                  window.location.reload();
                },
                `${resturls.createBusiness}`,
                updatedValues,
                'POST'
              );
            }}
            className={style.nextBtn}
          >
            Save
          </Button>
        </div>
      </div>
    );
  };

  const renderCreateModal = () => {
    return (
      <Modal basic size="small" open={activeModal} onClose={() => setActiveModal(false)}>
        <div className={style.modalContent}>
          <h5>Add New Business</h5>
          <div className={style.progressWrapper}>
            <div className={style.progress}>
              <span
                className={`${step?.step1 ? style.enableDot : ''} ${
                  step?.active === 'step1' ? style.activeDot : ''
                } ${style.dot}`}
              />
              <span className={style.line} />
              <span
                className={`${step?.step2 ? style.enableDot : ''} ${
                  step?.active === 'step2' ? style.activeDot : ''
                } ${style.dot}`}
              />
            </div>
            <div className={style.labelWrapper}>
              <div className={style.label}>
                <span className={style.firstLable}>Basic Info</span>
                <span>Primary Contact</span>
              </div>
            </div>
          </div>
          <Form className={style.formWrapper} onSubmit={formik.handleSubmit}>
            {renderCreateForm()}
            {renderButtons()}
          </Form>
        </div>
      </Modal>
    );
  };

  const organisationList = () => {
    if (businessList?.length > 0) {
      return businessList;
    }
    return [];
  };

  return (
    <>
      <Header />
      <div className={style.bussinessListScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>
          <div className={style.headerPart}>
            <div>
              <h4 className={style.separate}>
                All Tickets <span className={style.span_orgCOunt}>{BuisnesCount}</span>
              </h4>
              <p className={style.desc}>Track and Manage All Service Tickets in One Place</p>
            </div>
            <div className={style.btnWrapper}></div>
          </div>
          <div className={style.searchWrapper}>
            <Input
              className={style.searchInput}
              icon="search"
              placeholder="Search"
              iconPosition="left"
              value={filters?.searchText}
              onChange={handleInputChange}
            />
            <Form.Select
              placeholder="Status"
              className="customDropdown5"
              icon={<DropdownIcon />}
              options={TicketStatus}
              onChange={(e, { value }) => handleFilterChange('selectedStatus', value)}
            />
            <SearchableFormSelect
              url={resturls.getBusinesses}
              queryParams="business_name"
              placeholder="Organization"
              className="customDropdown5"
              icon={<DropdownIcon />}
              transformOptions={{
                key: 'business_id',
                value: 'business_id',
                text: 'business_name',
              }}
              clearable
              onChange={(e, { value }) => handleFilterChange('selectedOrganisation', value)}
            />

            <Form.Select
              placeholder="Category"
              className="customDropdown5"
              icon={<DropdownIcon />}
              options={[
                ...categoryList,
                ...(nextPage
                  ? [
                      {
                        key: 'load-more',
                        text: (
                          <Button
                            type="button"
                            onClick={() => obtainCategoryList(nextPage)}
                            disabled={isFetchingCategories}
                            loading={isFetchingCategories}
                            className={style.loadMoreButton}
                          >
                            Load More
                          </Button>
                        ),
                        disabled: true, // Prevent actual selection
                      },
                    ]
                  : []),
              ]}
              onChange={(e, { value }) => handleFilterChange('selectedCategory', value)}
            />
          </div>
          <div>
            {isLoading ? (
              <div className={style.loaderContainer}>
                <Loader active inline="centered" size="medium" />
              </div>
            ) : isResponsive ? (
              renderCardList()
            ) : (
              renderList()
            )}
          </div>
        </div>
      </div>
      {renderCreateModal()}
    </>
  );
};

export default TicketManagement;
