//environment variables
export const protocol = process.env.REACT_APP_PROTOCOL;
export const domain = process.env.REACT_APP_WS_URL;
export const port = process.env.REACT_APP_PORT;
export const reactport = process.env.REACT_APP_REACTPORT;
export const reactcontext = process.env.REACT_APP_REACTCONTEXT;
export const apicontext = process.env.REACT_APP_APICONTEXT;
export const cookiedomain = process.env.REACT_APP_COOKIEDOMAIN;
export const restbaseurl = process.env.REACT_APP_RESTBASEURL;
export const cdnurl = process.env.REACT_APP_CDNURL;
export const uploadsContext = process.env.REACT_APP_UPLOADSCONTEXT;
export const contexPath = process.env.REACT_APP_CONTEXPATH;
export const timerRefreshInterval = 120000;
export const CREATION_BUSINESS_ID = process.env.REACT_APP_CREATION_BUSINESS_ID;
/**
 * Dropdown Options
 */
export const timelineDropdownOptions = [
  {
    text: 'Last 7 days',
    value: 'last7days',
  },
  {
    text: 'Current Month',
    value: 'monthToDate',
  },
  {
    text: 'Last 30 days',
    value: 'last30days',
  },
  {
    text: 'Current Quarter',
    value: 'quarterToDate',
  },
  {
    text: 'Current Year',
    value: 'yearToDate',
  },
  {
    text: 'Custom Range',
    value: 'customDate',
  },
];

/**
 * Sorting Options
 */
export const SortOptions = [
  {
    title: 'Due Date',
    options: [
      { text: 'Oldest to Latest', value: 'earliestToLatest' },
      { text: 'Latest to Oldest', value: 'latestToEarliest' },
    ],
  },
  {
    title: 'Amount',
    options: [
      { text: 'Low to High', value: 'lowToHigh' },
      { text: 'High to Low', value: 'highToLow' },
    ],
  },
];

/**
 * Month Names List
 */
export const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

export const INDIAN_STATES = [
  { code: 'JK', name: 'Jammu and Kashmir', code_num: '01', state_name_code_num: '01 - Jammu and Kashmir' },
  { code: 'HP', name: 'Himachal Pradesh', code_num: '02', state_name_code_num: '02 - Himachal Pradesh' },
  { code: 'PB', name: 'Punjab', code_num: '03', state_name_code_num: '03 - Punjab' },
  { code: 'CH', name: 'Chandigarh', code_num: '04', state_name_code_num: '04 - Chandigarh' },
  { code: 'UK', name: 'Uttarakhand', code_num: '05', state_name_code_num: '05 - Uttarakhand' },
  { code: 'HR', name: 'Haryana', code_num: '06', state_name_code_num: '06 - Haryana' },
  { code: 'DL', name: 'Delhi', code_num: '07', state_name_code_num: '07 - Delhi' },
  { code: 'RJ', name: 'Rajasthan', code_num: '08', state_name_code_num: '08 - Rajasthan' },
  { code: 'UP', name: 'Uttar Pradesh', code_num: '09', state_name_code_num: '09 - Uttar Pradesh' },
  { code: 'BR', name: 'Bihar', code_num: '10', state_name_code_num: '10 - Bihar' },
  { code: 'SK', name: 'Sikkim', code_num: '11', state_name_code_num: '11 - Sikkim' },
  { code: 'AR', name: 'Arunachal Pradesh', code_num: '12', state_name_code_num: '12 - Arunachal Pradesh' },
  { code: 'NL', name: 'Nagaland', code_num: '13', state_name_code_num: '13 - Nagaland' },
  { code: 'MN', name: 'Manipur', code_num: '14', state_name_code_num: '14 - Manipur' },
  { code: 'MZ', name: 'Mizoram', code_num: '15', state_name_code_num: '15 - Mizoram' },
  { code: 'TR', name: 'Tripura', code_num: '16', state_name_code_num: '16 - Tripura' },
  { code: 'ML', name: 'Meghalaya', code_num: '17', state_name_code_num: '17 - Meghalaya' },
  { code: 'AS', name: 'Assam', code_num: '18', state_name_code_num: '18 - Assam' },
  { code: 'WB', name: 'West Bengal', code_num: '19', state_name_code_num: '19 - West Bengal' },
  { code: 'JH', name: 'Jharkhand', code_num: '20', state_name_code_num: '20 - Jharkhand' },
  { code: 'OR', name: 'Odisha', code_num: '21', state_name_code_num: '21 - Odisha' },
  { code: 'CG', name: 'Chhattisgarh', code_num: '22', state_name_code_num: '22 - Chhattisgarh' },
  { code: 'MP', name: 'Madhya Pradesh', code_num: '23', state_name_code_num: '23 - Madhya Pradesh' },
  { code: 'GJ', name: 'Gujarat', code_num: '24', state_name_code_num: '24 - Gujarat' },
  {
    code: 'DN',
    name: 'Dadra and Nagar Haveli and Daman and Diu',
    code_num: '26',
    state_name_code_num: '26 - Dadra and Nagar Haveli and Daman and Diu',
  },
  { code: 'MH', name: 'Maharashtra', code_num: '27', state_name_code_num: '27 - Maharashtra' },
  { code: 'KA', name: 'Karnataka', code_num: '29', state_name_code_num: '29 - Karnataka' },
  { code: 'GA', name: 'Goa', code_num: '30', state_name_code_num: '30 - Goa' },
  { code: 'LD', name: 'Lakshadweep', code_num: '31', state_name_code_num: '31 - Lakshadweep' },
  { code: 'KL', name: 'Kerala', code_num: '32', state_name_code_num: '32 - Kerala' },
  { code: 'TN', name: 'Tamil Nadu', code_num: '33', state_name_code_num: '33 - Tamil Nadu' },
  { code: 'PY', name: 'Puducherry', code_num: '34', state_name_code_num: '34 - Puducherry' },
  {
    code: 'AN',
    name: 'Andaman and Nicobar Islands',
    code_num: '35',
    state_name_code_num: '35 - Andaman and Nicobar Islands',
  },
  { code: 'TS', name: 'Telangana', code_num: '36', state_name_code_num: '36 - Telangana' },
  { code: 'AP', name: 'Andhra Pradesh', code_num: '37', state_name_code_num: '37 - Andhra Pradesh' },
];

export const defaultToastOptions = {
  autoClose: 2000,
  hideProgressBar: true,
  closeButton: true,
  isLoading: false,
};
