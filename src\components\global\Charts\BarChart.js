import React, { useState, useEffect, useRef } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { rightArrowIcon } from '../Icons';
import './scss/barChart.scss';
import { Loader } from 'semantic-ui-react';
import { formatNumber } from '../../utils/dateUtils';

// Register the required ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, PointElement, LineElement, LineController, Title, Tooltip);

const BarChart = ({
  labels,
  barData, // expects numeric values (e.g., [1070.27, 170.11, ...])
  trendlineData, // expects numeric values
  barColors = [],
  barObj = {},
  trendlineObj,
  style,
  trendlineLabels, // optional: used for trendline tooltips
  pagination,
  handlePagination,
  activePage,
  disableTrendline,
}) => {
  const [showTrendline, setShowTrendline] = useState(false);
  const { currency, gradientAddColorStart, gradientAddColorStop } = barObj;
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (labels?.length > 0) {
      setIsLoading(false); // Set to false once labels are available
    }
  }, [labels]);

  const scrollContainerRef = useRef(null);
  const chartRef = useRef(null);

  useEffect(() => {
    setTimeout(() => {
      if (chartRef.current && scrollContainerRef.current) {
        const chartInstance = chartRef.current;
        const todayIndex = labels.indexOf('Today');

        if (todayIndex !== -1) {
          const barMeta = chartInstance?.getDatasetMeta(0);
          if (barMeta && barMeta.data[todayIndex]) {
            const barElement = barMeta.data[todayIndex];
            const scrollContainer = scrollContainerRef.current;

            const barXPosition = barElement.x || barElement.tooltipPosition().x;

            // Scroll to the end position
            scrollContainer.scrollTo({
              left: barXPosition - scrollContainer.clientWidth + barElement.width,
              behavior: 'smooth',
            });
          }
        }
      }
    }, 500); // Delay to ensure chart renders fully
  }, [labels, chartRef]);

  const data = {
    labels,
    datasets: [
      {
        data: barData,
        backgroundColor: (context) => {
          const index = context.dataIndex;
          const chart = context.chart;
          const { ctx, chartArea } = chart;

          if (!chartArea) {
            return barColors[index]?.[0] || 'rgba(38, 51, 126, 1)'; // Fallback solid color
          }

          const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom);
          console.log(barColors, 'barColors');

          const startColor = barColors[index]?.[0] || 'rgba(38, 51, 126, 1)';
          const stopColor = barColors[index]?.[1] || 'rgba(200, 200, 255, 1)';

          gradient.addColorStop(0, startColor); // Top color
          gradient.addColorStop(1, stopColor); // Bottom color

          return gradient;
        },

        ...barObj,
      },
      // Include trendline dataset only if trendlineData exists and the toggle is on
      ...(trendlineData && showTrendline
        ? [
            {
              label: 'Trendline',
              data: trendlineData,
              type: 'line',
              tooltip: {
                enabled: true,
                callbacks: {
                  label: function (context) {
                    const trendLabel = trendlineLabels ? trendlineLabels[context.dataIndex] : '';
                    return `${trendLabel} - ${currency || '₹'} ${formatNumber(context.raw)}`;
                  },
                },
              },
              borderColor: trendlineObj?.borderColor || 'rgba(247, 144, 9, 1)',
              borderWidth: trendlineObj?.borderWidth || 2,
              pointBackgroundColor: trendlineObj?.pointBackgroundColor || 'white',
              pointRadius: trendlineObj?.pointRadius || 5,
              order: trendlineObj?.order || 1,
              barThickness: trendlineObj?.barThickness || 10,
              z: trendlineObj?.z || 1,
              ...trendlineObj,
            },
          ]
        : []),
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          // Hide the tooltip title
          title: function () {
            return '';
          },
          // Format the tooltip label to show the formatted number
          label: function (context) {
            const labelText = labels[context.dataIndex];
            return `${labelText} - ${currency || '₹'} ${formatNumber(context.raw)}`;
          },
        },
      },
    },
    layout: {
      padding: {
        top: 20, // Extra padding so text isn’t hidden
      },
    },
    scales: {
      x: {
        grid: {
          display: false, // Hide x-axis grid lines
          drawBorder: false,
        },
        border: {
          display: false,
        },
      },
      y: {
        grid: {
          display: false, // Hide y-axis grid lines
          drawBorder: false,
        },
        border: {
          display: false,
        },
        ticks: {
          display: false, // Hide y-axis labels
        },
      },
    },
    elements: {
      bar: {
        borderWidth: 0,
        barThickness: 250,
      },
    },
  };

  // Custom plugin to render formatted labels on top of bars
  const customPlugins = [
    {
      id: 'barLabelPlugin',
      beforeDatasetsDraw: (chart) => {
        const { ctx, data } = chart;
        const barDataset = data.datasets[0];
        chart.getDatasetMeta(0).data.forEach((bar, index) => {
          const value = barDataset.data[index];
          ctx.fillStyle = 'black';
          ctx.font = '12px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(`${currency || '₹'}${formatNumber(value)}`, bar.x, bar.y - 10);
        });
      },
    },
  ];

  if (isLoading) {
    return (
      <div className="loader">
        <Loader active />
      </div>
    );
  }

  const width = labels?.length > 6 ? `${labels.length * 65}px` : '100%';

  return (
    <div className="chartMainContentWarpper" ref={scrollContainerRef}>
      <div style={{ minWidth: width, height: '80%' }}>
        <Bar
          style={style}
          data={data}
          options={{
            ...options,
            maintainAspectRatio: false,
            responsive: true,
          }}
          plugins={customPlugins}
          ref={chartRef}
        />
      </div>
      <div
        className={`additionDetails 
          ${
            !(trendlineData && !disableTrendline) || !(pagination && pagination?.total_pages > 1)
              ? 'singleAddition'
              : ''
          } 
          ${
            trendlineData && !disableTrendline && !(pagination && pagination?.total_pages > 1) ? 'trendlineActive' : ''
          }`}
      >
        {trendlineData && !disableTrendline && (
          <div className="trendLineEnable">
            <input
              type="checkbox"
              id="trendlineCheckbox"
              checked={showTrendline}
              onChange={() => setShowTrendline(!showTrendline)}
            />
            <label htmlFor="trendlineCheckbox">Show Previous Period Trendline</label>
          </div>
        )}
        {pagination && pagination?.total_pages > 1 && (
          <div className="paginationWrapper">
            <div
              className={`arrowWrapper leftArrow ${activePage === 1 ? 'disableBtn' : ''}`}
              onClickCapture={() => activePage !== 1 && handlePagination('left')}
            >
              {rightArrowIcon()}
            </div>
            <div
              className={`arrowWrapper ${activePage === pagination?.total_pages ? 'disableBtn' : ''}`}
              onClickCapture={() => activePage !== pagination?.total_pages && handlePagination('right')}
            >
              {rightArrowIcon()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BarChart;
