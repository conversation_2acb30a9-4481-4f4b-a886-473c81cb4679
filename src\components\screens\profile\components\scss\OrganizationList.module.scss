@import '../../../../../assets/scss/main.scss';

.orgListWrapper {
  margin: 1.5em;
}

.orgCountText {
  width: 150px;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
}

.orgItem {
  display: flex;
  gap: 1em;
  margin-top: 1em;
  padding: 1em;
  background-color: $white;
  border-radius: 10px;
  align-items: center;
  justify-content: space-between;

  .leftContent {
    display: flex;
    gap: 1em;
    align-items: center;
  }

  .logo {
    padding: 0.5em;
    box-shadow: 0 0 1px 2px $borderColor;
    border-radius: 10px;
    img,
    svg {
      width: 30px;
      height: 30px;
    }
  }

  p {
    margin: 0;
    font-size: 1em !important;
  }

  .content {
    .name {
      font-weight: 900;
    }
  }

  .viewDetail {
    font-size: 1em !important;
    display: flex;
    color: #363f72;
    align-items: center;
    gap: 0.5em;
    -webkit-user-select: none;
    user-select: none;
    cursor: pointer;
    svg {
      width: 15px;
      height: 12px;
    }
  }
}

.paginationWrapper {
  padding: 2em 0;
}
