import React, { useState } from 'react';
import { Checkbox } from 'semantic-ui-react';
import CashFlowChart from '../../../../global/Charts/CashFlowChart';
import { EmptyBox } from '../../../../../assets/svgs';
import style from '../scss/detailedContent.module.scss';
import { LoadingWrapper } from '../../../../global/components';
import { formatAmount, formatDateRange } from '../../../../utils/dateUtils';

const DetailedContent = ({ resp, isLoading }) => {
  const [negativeValues, setNagativeValues] = useState(false);

  const receivables = resp?.data?.map((item) => item?.account_receivable);
  const payables = resp?.data?.map((item) => item?.account_payable);
  const closingBalance = resp?.data?.map((item) => item?.closing_balance);
  const categories = resp?.data?.map((item) => item?.label);
  const filteredData = resp?.data && resp?.data?.filter((item) => item.closing_balance < 0);
  const data = negativeValues ? filteredData : resp?.data;

  if (!isLoading && (resp?.data?.length === 0 || !resp?.data)) {
    return (
      <div className={style.emptyMsg}>
        <EmptyBox />
        <p>No data available for Projected Cash Flow at this time</p>
      </div>
    );
  }
  return (
    <div className={style.contentWrapper}>
      <div className={style.chartWrapper}>
        <LoadingWrapper loading={isLoading} minHeight={true}>
          <CashFlowChart
            receivables={receivables || []}
            payables={payables || []}
            closingBalance={closingBalance || []}
            categories={categories || []}
          />
        </LoadingWrapper>
      </div>
      <div className={style.detailedWrapper}>
        <div className={style.overAllInfo}>
          <div className={style.leftInfo}>
            <p>Current Bank Balance</p>
            <p>Current Cash Balance</p>
          </div>
          <div className={style.rightInfo}>
            <p>{formatAmount(resp?.current_bank_balance)}</p>
            <p>{formatAmount(resp?.current_cash_balance)}</p>
          </div>
        </div>
        <div className={style.detailsListContainer}>
          <h5>Detailed Report</h5>
          <div className={style.negative}>
            <Checkbox checked={negativeValues} onChange={() => setNagativeValues(!negativeValues)} />
            <span>show only days with negative balance</span>
          </div>
          <LoadingWrapper loading={isLoading} minHeight={true}>
            <div className={style.detailsList}>
              {/* Desktop View: Show 4 columns */}

              {data?.length > 0 ? (
                <>
                  <div className={`${style.Lists}`}>
                    <li className={style.MobileDate}>Date</li>
                    <li>Amount Receivables</li>
                    <li>Amount Payables</li>
                    <li>Closing Balance</li>
                  </div>
                  {data?.map((info) => (
                    <div className={style.rowContainer} key={info?.label}>
                      {/* Show Date above Receivables in mobile view only */}
                      <p className={style.dateColumn}>{formatDateRange(info?.label, '', true)}</p>

                      <div className={style.rowData}>
                        <p className={style.MobileDate}>{formatDateRange(info?.label, '', true)}</p>
                        <p style={{ color: 'green' }}>{formatAmount(info?.account_receivable)}</p>
                        <p style={{ color: 'red' }}>{formatAmount(info?.account_payable)}</p>
                        <p className={style.closingBalance}>{formatAmount(info?.closing_balance)}</p>
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                <div className={style.emptyMsg}>
                  <EmptyBox />
                  {negativeValues ? (
                    <p>No data available for Negative balance at this time</p>
                  ) : (
                    <p>No data available for Projected Cash Flow at this time</p>
                  )}
                </div>
              )}
            </div>
          </LoadingWrapper>
        </div>
      </div>
    </div>
  );
};

export default DetailedContent;
