@import '../../../../assets/scss/main.scss';

.resetPasswordModal {
  form {
    padding: 1em 0;
  }

  .error {
    color: #b42318;
    background-color: #fef3f2;
    padding: 0.75em;
    border-radius: 8px;
    margin-bottom: 1em;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .inputGroup {
    margin-bottom: 1.2em;

    label {
      display: block;
      margin-bottom: 0.5em;
      color: #344054;
      font-weight: 500;
    }

    input {
      width: 100%;
      padding: 0.75em;
      border: 1px solid #d0d5dd;
      border-radius: 8px;
      font-size: 1em;

      &:focus {
        outline: none;
        box-shadow: 0 0 0 4px rgba(127, 86, 217, 0.1);
      }
    }
  }

  .buttons {
    display: flex;
    justify-content: flex-end;
    gap: 1em;
    margin-top: 2em;

    button {
      padding: 0.75em 1.5em;
      border-radius: 8px;
      font-weight: 600;
      font-size: 0.9em;
      cursor: pointer;
      transition: background-color 0.15s;

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }

    .cancelBtn {
      background-color: white;
      border: 1px solid #d0d5dd;
      color: #344054;

      &:hover {
        background-color: #f9fafb;
      }
    }

    .submitBtn {
      background-color: $accentBgColor1 !important;
      border: 1px solid $accentBorder1;
      color: $primaryColor !important;
      &:hover {
        background-color: $accentHover1 !important;
      }
    }
  }
}

.passwordInput {
  position: relative;
  display: flex;
  align-items: center;
  input {
    width: 100%;
  }

  .eyeIcon {
    display: flex;
    align-items: center;
    position: absolute;
    right: 0.7em;
    height: 100%;
    cursor: pointer;
    color: #666;
    font-size: 1.15em;
    &:hover {
      color: #333;
    }
  }
}
