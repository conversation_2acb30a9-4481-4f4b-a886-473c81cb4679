@import '../../../../assets/scss/main.scss';

.customAccordion {
  .accordionTitle {
    display: flex;
    justify-content: space-between;
    padding: 1em !important;
    background-color: white;
    border: 1px solid #e9eaeb !important;
    border-radius: 13px !important;
    position: relative;
    z-index: 5;
    cursor: unset !important;

    p {
      margin: 0;
      font-size: 1.2em !important;
      @include for_media(mobileScreen) {
        font-size: 1em !important;
      }
    }
    .accordionName {
      width: 65%;
      @include for_media(mobileScreen) {
        font-size: 1em !important;
      }
    }
  }

  .accordContentWrapper {
    background-color: #eaecf5;
    position: relative;
    z-index: 1;
    border-radius: 0px 0px 10px 10px;
    bottom: 1em;
    padding: 1.5em 0em 0.5em 0em;

    .invoice {
      font-size: 1em !important;
      color: #414651;
      padding: 0 1em;
      margin: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;

      svg {
        height: 18px;
        width: 18px;
      }
    }

    .accordContent {
      display: none;
    }
  }

  .activeAccordion {
    padding: 1.5em 0em 1em 0em;

    .accordContent {
      display: block;
    }

    span {
      svg {
        transform: rotate(180deg);
      }
    }
  }

  .listContainer {
    padding: 1em 1em 0 1em;

    .listItem {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1em;

      p {
        margin: 0;
        font-size: 1.1em !important;
        width: 33%;
        @include for_media(mobileScreen) {
          font-size: 1em !important;
        }
      }

      .amount {
        text-align: end;
      }
    }
  }
}

.alignInvoice {
  word-break: break-word;
}
