@import '../../../../../assets/scss/main.scss';

.detailedWrapper {
  width: 50%;

  @include for_media(mobileScreen) {
    width: 100%;
  }

  .overAllInfo {
    padding: 1em;
    background-color: #eaecf5;
    display: flex;
    justify-content: space-between;
    border-radius: 15px;

    .leftInfo {
      h4 {
        display: flex;
        gap: 1em;
        align-items: center;
        font-size: 1.3em !important;
        margin: 0;
        @include for_media(mobileScreen) {
          font-size: 1.1em !important;
        }
        svg {
          width: 30px;
          height: 30px;
        }
      }

      p {
        color: #252b37;
        font-size: 1.1em !important;
      }
    }

    .rightInfo {
      h4 {
        font-size: 1.3em !important;
        margin: 0;
        @include for_media(mobileScreen) {
          font-size: 1.1em !important;
        }
      }
    }
  }

  .detailsListContainer {
    padding: 2em 0 0 0;

    @include for_media(mobileScreen) {
      padding-bottom: 2em !important;
    }

    h5 {
      margin: 0;
      font-size: 1.3em !important;
    }

    .detailsPagination {
      background: none !important;
    }

    .detailsList {
      //    display: flex;
      //    flex-direction: column;
      //    gap: 1.5em;
      margin: 1em 0;
      height: 50vh;
      overflow: auto;
      padding-right: 1em;

      div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1em;
        background-color: $white;
        border-radius: 10px;
        margin: 1em 0;

        p {
          margin: 0;
          @include for_media(mobileScreen) {
            font-size: 0.95em !important;
          }
        }
      }
    }
  }
}

.paginationWrapper {
  position: sticky;
  bottom: 0;
  display: flex !important;
  justify-content: flex-end !important;
}

.paginationWrapper div {
  max-width: 100% !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  scrollbar-width: none; /* Hides scrollbar in Firefox */
  -ms-overflow-style: none; /* Hides scrollbar in IE/Edge */
}

.paginationWrapper div::-webkit-scrollbar {
  display: none; /* Hides scrollbar in Chrome, Safari */
}
