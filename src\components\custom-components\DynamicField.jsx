import React, { useContext } from 'react';
import { FormControlLabel, Select, MenuItem } from '@mui/material';
import { FormikContext } from 'formik';
import SearchAutocomplete from './SearchAutocomplete';
import { conditionEval } from '../utils/stringUtils';
import LabeledField from '../ui-components/LabeledField';

export default function DynamicField({
  Component = null,
  config = null,
  label = null,
  required = null,
  disabled = null,
  control = null,
  size = 'small',
  options = [],
  ...props
}) {
  const formValues = useFormikContextValues();

  if (!Component && !control) throw new Error('Either Component or control is required');
  if (config && !config?.show_field) return null;
  const isDisabled = disabled !== null ? disabled : config?.access === 'RO';
  let isRequired = required;

  if (required === null && config) {
    if (typeof config.mandatory === 'object' && 'condition' in config.mandatory) {
      isRequired = conditionEval(config.mandatory.condition, formValues);
    } else {
      isRequired = !!config?.mandatory;
    }
  }

  // Handle Select component with options
  if (Component === Select && options.length > 0) {
    return (
      <LabeledField label={label} isRequired={isRequired} isDisabled={isDisabled}>
        <Select fullWidth size={size} required={isRequired} disabled={isDisabled} {...props}>
          {options.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </LabeledField>
    );
  }

  if (Component === SearchAutocomplete) {
    const dropdown_selector = config?.dropdown_selector;
    const isAsPerList = dropdown_selector?.toLowerCase() === 'as per list' || config?.dropdown_list?.length > 0;
    const staticOptions = isAsPerList ? config?.dropdown_list : null;

    return (
      <LabeledField label={label} isRequired={isRequired} isDisabled={isDisabled}>
        <SearchAutocomplete
          additionalParams={{ include: dropdown_selector }}
          staticOptions={staticOptions}
          required={isRequired}
          disabled={isDisabled}
          {...props}
        />
      </LabeledField>
    );
  }

  if (control)
    return <FormControlLabel control={React.cloneElement(control, { disabled: isDisabled, ...props })} label={label} />;

  return (
    <LabeledField label={label} isRequired={isRequired} isDisabled={isDisabled}>
      <Component size={size} required={isRequired} disabled={isDisabled} {...props} />
    </LabeledField>
  );
}

// Custom hook to safely get Formik context
// This prevents errors when DynamicField is used outside of a Formik provider
function useFormikContextValues() {
  const formikContext = useContext(FormikContext);

  if (!formikContext) {
    return null;
  }
  return formikContext?.values || null;
}
