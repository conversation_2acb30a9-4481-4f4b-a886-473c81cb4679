import React, { useState } from 'react';
import style from './filtersAccountant.module.scss';
import { FilterIcon } from '../../../../assets/svgs';
import FiltersMenu from './components/FiltersMenu';
import useUpdateEffect from '../../../global/hooks/useUpdateEffect';
import FilterBtn from '../../../ui-components/FilterBtn';

function FiltersAccountant({ setQuery, setExtraParams }) {
  const [isHighPriority, setIsHighPriority] = useState(false);
  const [isShowFiltersMenu, setIsShowFiltersMenu] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [isFilterApplied, setIsFilterApplied] = useState(0);

  useUpdateEffect(() => {
    setQuery('');
    setExtraParams({
      priority: isHighPriority && 'High priority',
      ...selectedOptions,
    });
  }, [isHighPriority, isFilterApplied]);

  return (
    <>
      <FilterBtn isActive={isFilterApplied} onClick={() => setIsShowFiltersMenu(!isShowFiltersMenu)} />

      <div
        className={`${isHighPriority && style.activePriorityBtn} ${style.priorityBtn}`}
        onClick={() => setIsHighPriority(!isHighPriority)}
      >
        <p>High priority</p>
      </div>

      {isShowFiltersMenu && (
        <div className={style.filtersMenuContainer} onClick={() => setIsShowFiltersMenu(false)}>
          <FiltersMenu
            setIsShowFiltersMenu={setIsShowFiltersMenu}
            setSelectedOptions={setSelectedOptions}
            selectedOptions={selectedOptions}
            setIsFilterApplied={setIsFilterApplied}
          />
        </div>
      )}
    </>
  );
}

export default React.memo(FiltersAccountant);
