@import '../../../../assets//scss/main.scss';

.dividedContainer {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  max-height: 75vh;
  position: relative;

  .leftPart {
    width: 40%;
    transition: width 0.3s ease;

    .additionalInfo {
      flex-direction: column;
      gap: 1.75rem;
      padding: 2rem;
      background-color: $white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      height: 100%;

      .progressBar {
        width: 100%;
        padding: 0.5rem;

        svg {
          width: 100%;
          height: auto;
          transition: transform 0.2s ease;

          &:hover {
            transform: scale(1.02);
          }
        }
      }
    }
  }

  .rightPart {
    height: 67vh;
    width: 60%;
    background-color: $white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
  }
}
