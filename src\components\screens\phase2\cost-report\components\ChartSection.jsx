import React from 'react';
import BarChart from '../../../../global/Charts/BarChart';
import style from '../scss/costReport.module.scss';
import { LoadingWrapper } from '../../../../global/components';
import { formatDateForMonth, isToday } from '../../../../utils/dateUtils';
import { useAuth } from '../../../../../contexts/AuthContext';

const ChartSection = ({ isLoading, selectedTimeline, costDetails, dateData, trendlineData }) => {
  const { isMobileScreen } = useAuth();

  // Sort costDetails data (oldest to newest)
  const sortedData = costDetails?.data?.slice().sort((a, b) => {
    return new Date(a.label) - new Date(b.label);
  });

  // Generate x-axis labels
  const labels = sortedData?.map((item) => {
    const label = item?.label;
    if (!label) return '';

    if (costDetails?.metadata?.granularity === 'day') {
      return isToday(label) ? 'Today' : new Date(label).toLocaleDateString('en-GB', { day: '2-digit', month: 'short' });
    }
    if (costDetails?.metadata?.granularity === 'month') {
      return new Date(label).toLocaleString('en-US', { year: 'numeric', month: 'short' });
    }
    if (costDetails?.metadata?.granularity === 'week') {
      return label;
    }
    return formatDateForMonth(label);
  });

  // Sort trendline data (oldest to newest)
  const granularity = trendlineData?.metadata?.granularity;
  const sortedTrendlineData = trendlineData?.data?.slice().sort((a, b) => {
    return new Date(a.label) - new Date(b.label);
  });

  // Generate trendline labels
  const trendlineLabels = sortedTrendlineData?.map((item) => {
    const label = item?.label;
    if (!label) return '';

    if (granularity === 'day') {
      return isToday(label) ? 'Today' : new Date(label).toLocaleDateString('en-GB', { day: '2-digit', month: 'short' });
    }
    if (granularity === 'month') {
      return new Date(label).toLocaleString('en-US', { year: 'numeric', month: 'short' });
    }
    if (granularity === 'week') {
      return label;
    }
    return formatDateForMonth(label);
  });

  // Shift labels for short date ranges
  let shouldShiftLabels = false;
  if (dateData?.startDate && dateData?.endDate) {
    const diffInDays = (new Date(dateData.endDate) - new Date(dateData.startDate)) / (1000 * 60 * 60 * 24);
    shouldShiftLabels = diffInDays <= 30;
  } else {
    shouldShiftLabels = !['last7days', '30days'].includes(selectedTimeline?.value);
  }

  if (shouldShiftLabels && trendlineLabels?.length > 1) {
    trendlineLabels?.unshift(trendlineLabels?.pop());
  }

  // Process trendline data
  let trendlineData1 = [];
  if (sortedTrendlineData?.length) {
    let transformedData = [...sortedTrendlineData];
    if (shouldShiftLabels && transformedData?.length > 1) {
      transformedData?.unshift(transformedData?.pop());
    }
    trendlineData1 = transformedData?.map((item) => item.amount);
  }

  const barData = sortedData?.map((item) => item.amount);

  const barColors = sortedData?.map(
    (info) =>
      isToday(info?.label)
        ? ['#FFAE57', '#D94A28'] // Mobile gradient for today
        : ['#F6D4B2', '#F6D4B2'] // Mobile gradient for other days
  );

  const chartKey = JSON.stringify(costDetails);
  return (
    <div className={style.chartWrapper}>
      <LoadingWrapper loading={isLoading} minHeight={true}>
        <BarChart
          labels={labels}
          key={chartKey}
          barData={barData}
          trendlineData={trendlineData1 || []}
          trendlineLabels={trendlineLabels || []}
          barColors={barColors}
          barObj={{
            gradientAddColorStart: 'rgba(38, 51, 126, 1)',
            gradientAddColorStop: 'rgba(78, 91, 166, 1)',
            currency: '₹',
            barThickness: 40,
            borderRadius: { topLeft: 4, topRight: 4 },
            order: 2,
          }}
          disableTrendline={selectedTimeline?.value === 'customDate'}
          trendlineObj={{
            label: 'Trendline',
            borderColor: 'rgba(247, 144, 9, 1)',
          }}
        />
      </LoadingWrapper>
    </div>
  );
};

export default ChartSection;
