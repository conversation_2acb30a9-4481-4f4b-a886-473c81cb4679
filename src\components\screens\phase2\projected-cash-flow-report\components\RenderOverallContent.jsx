import React from 'react';
import DetailedContent from './DetailedContent';
import RenderDownloadModal from '../../../../utils/reportUtils/RenderDownloadModal';
import ReportHeader from '../../../../global/components/ReportHeader';

const RenderOverallContent = ({
  cashFlowInfo,
  fetchCashFlowData,
  btnLoader,
  setDownloadModal,
  isLoading,
  downloadModal,
  downloadFunction,
}) => {
  return (
    <>
      <ReportHeader
        title="Projected Cash Flow"
        data={cashFlowInfo}
        handleRefresh={fetchCashFlowData}
        setDownloadModal={setDownloadModal}
        loading={btnLoader}
      />
      <DetailedContent resp={cashFlowInfo} isLoading={isLoading} />
      <RenderDownloadModal
        isOpen={downloadModal}
        onClose={() => setDownloadModal(false)}
        content={`Your Projected Cash Flow Report is prepared and ready for download. You can save it for further review`}
        downloadFunction={downloadFunction}
        checkBoxDisable={true}
      />
    </>
  );
};

export default RenderOverallContent;
