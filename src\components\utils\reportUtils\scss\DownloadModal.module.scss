@import '../../../../assets/scss/main.scss';

.modalContent {
  border-radius: 20px !important;
}

.mobileViewContainer::-webkit-scrollbar {
  display: none; /* For Chrome, Safari, and Opera */
}

.mobileViewContainer {
  background-color: #f6f8fa;
  position: absolute;
  z-index: 1000;
  height: 100vh;
  width: 100%;
  padding: 1em;
  overflow-y: auto;
  -ms-overflow-style: none; /* For IE and Edge */
  scrollbar-width: none; /* For Firefox */
  .rightContentWrapper {
    width: 100%;
    .headerContent {
      .downloadBtn {
        display: none;
      }
      .refreshBtn {
        width: 35px;
        height: 35px;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 10px !important;
        background-color: $white;
        span {
          display: none;
        }
      }
      .leftContent {
        p {
          font-size: 1em !important;
        }
      }
    }
    .contentWrapper {
      flex-direction: column;
      padding: 1em 0;
    }
    .chartWrapper {
      width: 100%;
      background-color: unset;
      padding: 0;
    }
    .detailedWrapper {
      width: 100%;
      .detailsList {
        gap: 1em !important;
        display: block !important;
        // height: 45vh !important;
        // overflow-y: auto;
        padding-bottom: 6em;
        div {
          margin: 1em 0;
          p {
            font-size: 1.1em !important;
          }
        }
      }
    }
  }
  .popupContainer {
    padding: 0 1em !important;
  }
  .backIcon {
    padding: 1em 0;
    svg {
      height: 35px;
      width: 35px;
    }
  }
  .dropDown {
    width: auto !important;
    p {
      svg {
        display: none;
      }
    }
  }
  .applyBtn {
    padding: 1em 1em 0 1em !important;
  }
}

.downloadBtnWrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1em;
  background-color: $white;
  z-index: 100;
}
.downloadBtn {
  background-color: #f6d659;
  color: #7e6607;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3.1em;
  gap: 1em;
  border-radius: 35px !important;
  cursor: pointer;
  -webkit-text-stroke: 0.25px;
  font-size: 1.2em !important;
  svg {
    path {
      fill: #7e6607;
    }
  }
  @include for_media(mobileScreen) {
    width: 95%;
  }
}

.modalContent {
  h4 {
    padding: 0.5em 1em;
  }
}
.downloadModal {
  .fileContent {
    padding: 1em;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    h4 {
      margin: 0;
    }
    p {
      color: #717680;
      @include for_media(mobileScreen) {
        font-size: 1.1em !important;
      }
    }
  }
}

.checkBoxWrapper {
  display: flex;
  padding: 1em;
  justify-content: center;
  gap: 1em;
  align-items: center;
  background-color: #f5f5f5;
  border: 1px solid #e9eaeb;
  border-radius: 15px;
  margin: 1em;
  cursor: pointer;
}

.applyBtn {
  display: flex;
  justify-content: center;
  button {
    background-color: #f6d659;
    margin-top: 10px;
    color: #7e6607;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3.1em;
    gap: 1em;
    border-radius: 35px !important;
    cursor: pointer;
    -webkit-text-stroke: 0.25px;
    font-size: 1.2em !important;
    svg {
      path {
        fill: #7e6607;
      }
    }
  }
}
