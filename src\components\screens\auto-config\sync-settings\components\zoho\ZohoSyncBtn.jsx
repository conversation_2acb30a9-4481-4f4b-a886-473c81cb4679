import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../../../../../contexts/AuthContext';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../../../utils/apiUtils';
import useUpdateEffect from '../../../../../global/hooks/useUpdateEffect';
import { Button, LinearProgress, Alert, CircularProgress, Typography } from '@mui/material';
import { fullSync, getSyncStatus } from '../../../../../services/syncSettingsServices';
import SyncIcon from '@mui/icons-material/Sync';

const ZohoSyncButton = ({ isConnected, refetchSyncStatus, syncProgress, didSyncFailed }) => {
  const { globSelectedBusiness } = useAuth();
  const [isProgressPolling, setIsProgressPolling] = useState(syncProgress !== 100);
  const [syncFailed, setSyncFailed] = useState(didSyncFailed);
  
  //this is being used for polling sync status and getting progress percentage
  const { data: syncStatus } = useQuery({
    queryKey: ['sync-status', globSelectedBusiness?.business_id],
    queryFn: () => getSyncStatus(globSelectedBusiness?.business_id),
    enabled: isConnected && isProgressPolling && !syncFailed,
    refetchInterval: 2000,
    refetchIntervalInBackground: false,
  });

  const handleSyncNow = () => {
    setIsProgressPolling(true);
    setSyncFailed(false);
    fullSync(globSelectedBusiness?.business_id)
      .then((res) => {
        if (res?.status?.toLowerCase() !== 'success') {
          throw new Error(res?.message);
        }
      })
      .catch((error) => {
        const errorMessage = getErrorMessage(error);
        toast.error(errorMessage);
        setIsProgressPolling(false);
        setSyncFailed(true);
      })
      .finally(() => refetchSyncStatus());
  };

  // Check if sync is completed
  useUpdateEffect(() => {
    const status = syncStatus?.sync_status?.toLowerCase();
    const progress = Number(syncStatus?.sync_progress);

    if (status === 'failed') {
      setSyncFailed(true);
      refetchSyncStatus();
      setIsProgressPolling(false);
      return;
    }
    if (progress === 100) {
      setIsProgressPolling(false);
      if (status === 'completed') {
        refetchSyncStatus();
        toast.success('Sync completed successfully!');
      }
    }
  }, [syncStatus]);

  const currentProgress = syncStatus?.sync_progress || 0;

  return (
    <div className="flex flex-col gap-3 min-w-[140px]">
      {syncFailed ? (
        <div className="flex flex-col gap-2">
          {!isProgressPolling && (
            <Alert severity="error" className="!py-0 !px-3">
              Sync failed
            </Alert>
          )}
          <Button
            onClick={handleSyncNow}
            disabled={!isConnected}
            variant="contained"
            color="error"
            startIcon={<SyncIcon />}
            className="py-2 px-4 rounded-xl font-medium"
          >
            Retry Sync
          </Button>
        </div>
      ) : (
        <Button
          onClick={handleSyncNow}
          disabled={!isConnected || isProgressPolling}
          variant="contained"
          startIcon={isProgressPolling ? <CircularProgress size={16} className="text-accent1" /> : <SyncIcon />}
          className="py-2 px-4 rounded-xl font-medium"
        >
          {isProgressPolling ? 'Syncing...' : 'Sync Now'}
        </Button>
      )}

      {/* Progress Bar */}
      {isProgressPolling && (
        <div className="w-full">
          <LinearProgress variant="determinate" value={currentProgress} className="h-2 rounded-full" />
          <div className="flex justify-between items-center mt-1">
            <Typography variant="caption" className="text-gray-600">
              Progress
            </Typography>
            <Typography variant="caption" className="text-gray-600 font-medium">
              {currentProgress}%
            </Typography>
          </div>
        </div>
      )}
    </div>
  );
};

export default ZohoSyncButton;
