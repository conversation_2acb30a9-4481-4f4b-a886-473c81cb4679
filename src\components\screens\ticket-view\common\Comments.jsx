import React, { useEffect, useRef, useState } from 'react';
import style from './scss/comments.module.scss';
import Avatar from 'react-avatar';
import { useAuth } from '../../../../contexts/AuthContext';
import { formatRelativeTime, jsonKeyFormatLabel } from '../../../utils';
import { LockIcon } from '../../../../assets/svgs';
import DocumentAttachments from './DocumentAttachments';
import dompurify from 'dompurify';
import ls from 'local-storage';
import { domain } from '../../../utils/constants';

const getAvatarName = (user, currentUserId) => {
  const name = jsonKeyFormatLabel(user?.name || user?.full_name || user?.role);
  if (user?.user_id === currentUserId) return { avatarName: name, name: 'You' };
  return { avatarName: name, name };
};

function Comments({ ticket }) {
  const { userInfo, roleType } = useAuth();
  const [comments, setComments] = useState(ticket?.comments || []);
  const receivedCommentIds = useRef(new Set(ticket?.comments?.map((c) => c.id) || []));
  useEffect(() => {
    const token = ls.get('access_token')?.data;
    const ticketId = ticket?.id;
    if (!token || !ticketId) {
      console.error('Missing token or commentSection ID.');
      return;
    }

    const wsUrl = `wss://${domain}/ws/comments/${ticketId}/?token=${token}`;
    const socket = new WebSocket(wsUrl);

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (typeof data !== 'object' || data?.comments) return;

        if (receivedCommentIds.current.has(data.id)) {
          console.warn('Duplicate comment received, ignoring:', data.id);
          return;
        }

        receivedCommentIds.current.add(data.id);

        const formatedCommentObj = {
          ...data,
          user: {
            name: data.user.split('|')[0].trim(),
            user_id: data.user_id,
            role: data.user_role,
          },
        };
        setComments((prevComments) => [formatedCommentObj, ...prevComments]);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    socket.onerror = (error) => console.error('WebSocket error:', error.message);

    return () => {
      socket.close();
    };
  }, []);

  if (!ticket) return <></>;
  return (
    <div className={style.commentsWrapper}>
      {comments?.map((comment) => {
        if ((comment.visiblity === 'private' && roleType === 'user') || Object.keys(comment).length < 1) return <></>;
        const name = getAvatarName(comment?.user, userInfo?.userId);
        return (
          <div className={style.commentContainer} key={comment?.id || comment?.updated_at}>
            <div className={style.timelineContainer}>
              <Avatar className={style.avatar} color="#D5D7DA" name={name.avatarName} />
            </div>
            <div className={style.contentContainer}>
              <div className={style.infoContainer}>
                <div className={style.nameContainer}>
                  <span className={style.nameWrapper}>
                    {name.name}
                    {comment.visiblity === 'private' && (
                      <span className={style.private}>
                        <LockIcon />
                      </span>
                    )}
                  </span>
                </div>
                <span className={style.timeContainer}>{formatRelativeTime(comment?.created_at)}</span>
              </div>
              <div className={style.messageContainer}>
                <p
                  className={style.message}
                  dangerouslySetInnerHTML={{
                    __html: dompurify.sanitize(comment?.description?.replace(/\n/g, '<br>')),
                  }}
                ></p>
                <DocumentAttachments
                  ticketId={ticket?.id}
                  isAiEnabled={ticket?.permissions?.show_ai_extraction ?? false}
                  businessObj={ticket?.business}
                  attachments={comment?.documents}
                />
              </div>
            </div>
          </div>
        );
      })}
      <div className={style.commentContainer}>
        <div className={style.timelineContainer}>
          <div className={style.emptyProfile}>
            <span />
          </div>
        </div>
        <div className={style.contentContainer}>
          <div className={style.infoContainer}>
            <div className={style.nameContainer}>
              <span className={style.nameWrapper}>
                <span className={style.ticketCreatedBy}>
                  Ticket Created by{' '}
                  {ticket?.from_email && !ticket?.created_by ? 'Email Forwarding' : ticket?.created_by}
                  {roleType === 'admin' || roleType === 'accountant'
                    ? ticket?.assign_to && `, assigned to ${ticket?.assign_to?.user_full_name}`
                    : ticket?.requested_by &&
                      `, Requested by ${ticket?.requested_by?.full_name || ticket?.requested_by?.user_full_name}`}
                </span>
              </span>
            </div>
            <span className={style.timeContainer}>{formatRelativeTime(ticket?.created_at)}</span>
          </div>
          <div className={style.messageContainer}>
            {ticket?.description?.length > 0 && (
              <p
                className={style.message}
                dangerouslySetInnerHTML={{
                  __html: dompurify.sanitize(ticket?.description),
                }}
              ></p>
            )}
            <DocumentAttachments
              ticketId={ticket?.id}
              isAiEnabled={ticket?.permissions?.show_ai_extraction ?? false}
              businessObj={ticket?.business}
              attachments={ticket?.attachements}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Comments;
