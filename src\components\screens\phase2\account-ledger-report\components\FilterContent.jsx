import React from 'react';
import { Popup } from 'semantic-ui-react';
import { CalenderIcon, DropdownIcon, OrgBuildingIcon } from '../../../../../assets/svgs';
import style from '../scss/filterContent.module.scss';
import { useAuth } from '../../../../../contexts/AuthContext';

const FilterContent = ({
  appliedAccountList,
  setAccountTypeDropdown,
  accountTypeDropdown,
  handleDropdownList,
  selectedTimeline,
  dateInfo,
  openDropdown,
  renderAccountTypeContent,
  renderPopupContent,
}) => {
  const { isMobileScreen } = useAuth();
  return (
    <div className={style.dropdownWrapper}>
      <Popup
        className={style.popup}
        trigger={
          <div
            className={`${style.accountDropDown} ${style.dropDown} ${accountTypeDropdown && style.activeDropdown}`}
            onClick={() => setAccountTypeDropdown(true)}
          >
            <p>
              <OrgBuildingIcon />
              {appliedAccountList?.length > 0 ? (
                <div className={style.selectedItems}>
                  {appliedAccountList.slice(0, 5).map((item, index) => (
                    <span key={index}>
                      {item?.text}
                      {index < Math.min(appliedAccountList.length, 5) - 1 ? ', ' : ''}
                    </span>
                  ))}
                  {appliedAccountList.length > 5 && ' ...'}
                </div>
              ) : (
                'Select Account/Ledger'
              )}
            </p>
            <DropdownIcon />
            {appliedAccountList?.length > 0 && !accountTypeDropdown && (
              <span className={style.appliedAccountList}>
                {appliedAccountList?.filter((info) => String(info.value).startsWith('all') === false)?.length}
              </span>
            )}
          </div>
        }
        content={renderAccountTypeContent}
        position="bottom left"
        on="click"
        hoverable
        basic
        open={!isMobileScreen && accountTypeDropdown}
        closeOnDocumentClick={false}
      />
      <Popup
        className={style.popup}
        trigger={
          <div className={style.dropDown} onClick={() => handleDropdownList()}>
            <p>
              <CalenderIcon />
              {selectedTimeline?.value !== 'customDate'
                ? selectedTimeline?.text
                : `${dateInfo?.startDate} - ${dateInfo?.endDate}`}
            </p>
            <DropdownIcon />
          </div>
        }
        content={renderPopupContent}
        position="bottom left"
        on="click"
        hoverable
        basic
        open={!isMobileScreen && openDropdown}
        closeOnDocumentClick={false}
      />
    </div>
  );
};

export default FilterContent;
