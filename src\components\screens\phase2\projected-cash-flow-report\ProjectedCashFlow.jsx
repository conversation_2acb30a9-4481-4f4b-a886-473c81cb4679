import React, { useEffect, useState } from 'react';
import style from './scss/ProjectedCashFlow.module.scss';
import ls from 'local-storage';
import { mediaBreakpoint } from '../../../global/MediaBreakPointes';
import GlobalService from '../../../services/GlobalServices';
import { resturls } from '../../../utils/apiurls';
import { useNavigate } from 'react-router-dom';
import { BackIcon, DownloadIcon } from '../../../../assets/svgs';
import RenderOverallContent from './components/RenderOverallContent';

const ProjectedCashFLow = () => {
  const navigate = useNavigate();

  const [downloadModal, setDownloadModal] = useState(false);
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const [isLoading, setIsLoading] = useState(false);
  const [btnLoader, setbtnLoader] = useState(false);
  const [cashFlowInfo, setCashFlowInfo] = useState({});
  const business_id = ls.get('selectedBusiness')?.business_id;

  const obtainCashFlowDetails = () => {
    const projectCashFLow = ls.get('projectCashFLow');

    const currentDate = new Date();

    if (projectCashFLow) {
      const { data, expiry_at } = projectCashFLow;
      const expiryDate = new Date(expiry_at);

      if (expiryDate < currentDate) {
        ls.remove('projectCashFLow');
        fetchCashFlowData();
      } else {
        setCashFlowInfo(data);
      }
    } else {
      fetchCashFlowData();
    }
  };

  useEffect(() => {
    obtainCashFlowDetails();
  }, []);

  const fetchCashFlowData = async () => {
    setIsLoading(true);
    GlobalService.generalSelect(
      async (response) => {
        if (response?.data) {
          const { expiry_at } = response;
          const dataToStore = {
            data: response,
            expiry_at,
          };
          setCashFlowInfo(response);
          ls.set('projectCashFLow', dataToStore);
          // setCostDetails(data);
        }
        setIsLoading(false);
      },
      `${resturls.getProjectedCashFlow}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  const downloadFunction = () => {
    setbtnLoader(true);
    GlobalService.generalSelect(
      (respdata, error) => {
        if (error || !respdata) {
          console.error('Download failed:', error);
          alert('Failed to download the report. Please check your data or try again.');
          return; // Stop execution if error occurs or data is empty
        }

        setDownloadModal(false);
        setbtnLoader(false);

        // Convert response to a Blob with 'text/csv' MIME type
        const blob = new Blob([respdata], { type: 'text/csv' });

        // Create an anchor element to trigger the download
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `Project_Cash_Flow_Report.csv`; // Change file extension to .csv

        // Append the link to the DOM and simulate a click to start the download
        document.body.appendChild(link);
        link.click();

        // Clean up by revoking the object URL
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      },
      `${resturls.downloadProjectCashFlowReport}?business_id=${business_id}`,
      {},
      'GET'
    );
  };

  const commonProps = {
    cashFlowInfo,
    fetchCashFlowData,
    btnLoader,
    setDownloadModal,
    isLoading,
    downloadModal,
    downloadFunction,
  };

  if (isResponsive) {
    return (
      <div className={style.mobileViewContainer}>
        <div className={style.backIcon} onClick={() => navigate(-1)}>
          <BackIcon />
        </div>
        <div className={style.rightContentWrapper}>
          <RenderOverallContent {...commonProps} />
        </div>
        <div className={style.downloadBtnWrapper}>
          <button
            className={style.downloadBtn}
            onClick={() => {
              setDownloadModal(true);
            }}
          >
            <DownloadIcon /> Download Report
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <RenderOverallContent {...commonProps} />
    </>
  );
};

export default ProjectedCashFLow;
