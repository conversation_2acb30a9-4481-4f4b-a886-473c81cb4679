import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { Autocomplete, TextField, CircularProgress, Button } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import useQueryLoadMoreSearch from '../global/hooks/useQueryLoadMoreSearch';
import { isEqual } from 'lodash';

export default function SearchAutocomplete({
  url,
  searchParam = 'search',
  onSelect,
  optionLabel = 'label',
  optionValue = 'value',
  value = {},
  size = 'small',
  staticOptions = null,
  label = '',
  additionalParams = {},
  ...props
}) {
  const { globSelectedBusiness } = useAuth();
  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState(null);
  const [inputTouched, setInputTouched] = useState(false);

  const { options, isFetchingNextPage, isLoading, loadMore } = useQueryLoadMoreSearch({
    url,
    searchParam,
    searchValue: inputTouched ? inputValue : '',
    enabled: !!globSelectedBusiness?.business_id && !staticOptions,
    additionalParams,
  });

  const handleChange = useCallback(
    (_, newValue) => {
      if (newValue?.loadMore) return;
      setInputTouched(false);
      const selectedOptionValue = newValue?.[optionValue] ?? null;
      onSelect?.(selectedOptionValue, newValue);
    },
    [optionValue, optionLabel]
  );

  useEffect(() => {
    if (inputTouched || isLoading) return;
    if (value === null || value === undefined || value === '') {
      setSelectedOption(null);
      return;
    }
    const isValueObject = typeof value === 'object';
    if (isValueObject && (Object.keys(value).length === 0 || isEqual(value, selectedOption))) return;
    if (isValueObject) {
      setSelectedOption(value);
      return;
    }

    const list = staticOptions || options || [];
    const matched = list.find((option) => option?.[optionValue] === value) ?? null;
    setSelectedOption(matched);
  }, [value, optionValue, staticOptions, options, inputTouched, isLoading, selectedOption]);

  const filterOptions = useMemo(() => {
    return url ? (x) => x : undefined;
  }, [url]);

  const handleInputChange = useCallback((_, value, reason) => {
    setInputValue(value);
    if (reason === 'input') setInputTouched(true);
  }, []);

  return (
    <Autocomplete
      value={selectedOption}
      options={staticOptions || options || []}
      getOptionLabel={(option) => (option.loadMore ? '' : (option?.[optionLabel] ?? ''))}
      isOptionEqualToValue={(option, value) => option?.[optionValue] === value?.[optionValue]}
      loading={isLoading}
      openOnFocus
      inputValue={inputValue}
      onInputChange={handleInputChange}
      onChange={handleChange}
      renderInput={(params) => (
        <TextField
          {...params}
          {...props}
          size={size}
          label={label}
          slotProps={{
            input: {
              ...params.InputProps,
              endAdornment: (
                <>
                  {isLoading && <CircularProgress color="inherit" size={20} />}
                  {params.InputProps.endAdornment}
                </>
              ),
            },
          }}
        />
      )}
      renderOption={(props, option) => {
        if (option.loadMore) {
          return (
            <li {...props} key="__load_more__" className="flex justify-center py-2">
              <Button
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  loadMore();
                }}
                disabled={isFetchingNextPage}
                variant="outlined"
                className="min-w-[120px]"
              >
                {isFetchingNextPage ? (
                  <>
                    <CircularProgress size={16} className="mr-2" />
                    Loading...
                  </>
                ) : (
                  'Load More'
                )}
              </Button>
            </li>
          );
        }
        return (
          <li {...props} key={`${option?.[optionLabel] ?? ''}-${option?.[optionValue] ?? ''}`}>
            {option?.[optionLabel] || ''}
          </li>
        );
      }}
      noOptionsText={isLoading ? 'Loading...' : 'No options available'}
      loadingText="Loading options..."
      filterOptions={filterOptions}
      {...props}
    />
  );
}
