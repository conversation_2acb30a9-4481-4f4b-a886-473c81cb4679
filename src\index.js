import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import 'fomantic-ui-css/semantic.min.css';
import 'react-datepicker/dist/react-datepicker.css';
import { ThemeProvider } from '@mui/material';
import theme from './MUITheme';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <ThemeProvider theme={theme}>
    <App />
  </ThemeProvider>
);

reportWebVitals();
