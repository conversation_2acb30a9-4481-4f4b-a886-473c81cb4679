import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Modal } from 'semantic-ui-react';
import Header from '../global/Header';
import style from './scss/ticketCreation.module.scss';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useParams } from 'react-router-dom';
import TicketForm from './TicketForm';
import { Button } from 'semantic-ui-react';
import { mediaBreakpoint } from '../global/MediaBreakPointes';
import NavigationBar from './NavigationBar';
import ls from 'local-storage';
import { decryptData } from '../utils/cryptoUtils';

const currentRoleEncrypted = ls.get('access_token')?.role;

const CurentRole = currentRoleEncrypted && decryptData(currentRoleEncrypted);

const TicketCreation = () => {
  // const [previews, setPreviews] = useState([]);
  // const fileInputRef = useRef(null);
  const { userInfo } = useAuth();
  const [businessInfo, setBusinessInfo] = useState([]);
  const [fileIdList, setFileInfo] = useState([]);
  const [loadingFiles, setLoadingFiles] = useState([]);
  const [ticketInfo, setTicketInfo] = useState(false);
  const [isModalOpen, setModal] = useState(false);
  const [categoryList, setCategoryList] = useState([]);
  const [organisationList, setOrganisationList] = useState([]);
  const [userList, setUserList] = useState([]);
  const [nextPageUrl, setNextPageUrl] = useState(null);
  const [isFetching, setIsFetching] = useState(false);
  const [ctryNextPageUrl, setCtryNextPageUrl] = useState(null);
  const [ctryIsFetching, setCtryIsFetching] = useState(false);

  const navigate = useNavigate();
  const isResponsive = mediaBreakpoint?.mobile > window.innerWidth;
  const userId = userInfo?.user_id || userInfo?.userId;

  const { id } = useParams();

  const initialValues = {
    subject: '',
    description: '',
    attachments: [],
    priority: 'Normal',
    assignUser: '',
    status: '',
    category: '',
    organization: '',
    subCategory: '',
    assignToMe: false,
    assignTo: '',
    requestedBy: '',
  };

  const fetchUserBusinesses = useCallback(
    (url, id) => {
      if (!userId) {
        console.error('User ID is required to fetch businesses.');
        return;
      }

      setIsFetching(true); // Assuming you track fetching state for this as well
      console.log(`Fetching user businesses from: ${url}`);

      GlobalService.generalSelect(
        (respData) => {
          const { results, next } = respData;

          if (!results || results.length === 0) {
            console.error('Invalid or empty response data', { respData });
            setIsFetching(false);
            return;
          }

          // Find the specific business info for the given ID
          const updatedItem = results.find((data) => data.business_id === id);
          setBusinessInfo(updatedItem);

          // Map the response to create an organization list
          const list = results.map((business) => ({
            key: business.business_id,
            value: business.business_id,
            text: business.business_name,
          }));
          setOrganisationList((prevItems) => {
            const existingKeys = new Set(prevItems.map((item) => item.key));
            const uniqueNewBusinesses = list.filter((business) => !existingKeys.has(business.key));

            return [...prevItems, ...uniqueNewBusinesses];
          });
          setNextPageUrl(next);
          setIsFetching(false);
          console.log('User businesses fetched successfully', {
            businessesLoaded: list.length,
          });
        },
        url,
        {},
        'GET'
      );
    },
    [userId]
  );

  useEffect(() => {
    if (userId) {
      fetchUserBusinesses(`${resturls.getBusinesses}`, id);
    }
  }, [userId, id, fetchUserBusinesses]);

  const loadMoreItems = () => {
    if (nextPageUrl && !isFetching) {
      const secureUrl = nextPageUrl.replace('http://', 'https://');
      fetchUserBusinesses(secureUrl);
    }
  };

  const obtainUsersList = (selectedId) => {
    GlobalService.generalSelect(
      (respdata) => {
        if (respdata && respdata.data) {
          const { data } = respdata;
          const list = data.map((user) => ({
            key: user.user_id,
            value: user.user_id,
            text: user.full_name || user.email,
          }));

          // Sorting the list alphabetically by text
          const sortedList = list.sort((a, b) => a.text.localeCompare(b.text));

          setUserList(sortedList);
        }
      },
      `${resturls.obtainCategoryWiseUser}?user_type=accountants,superusers,managers`,
      {},
      'GET'
    );
  };

  console.log(userList, 'userList');

  const fetchCategoryList = useCallback(
    (url) => {
      setIsFetching(true); // Assuming you track fetching state for this as well
      console.log(`Fetching category list from: ${url}`);

      GlobalService.generalSelect(
        (respData) => {
          if (respData && respData.results) {
            const { next, results } = respData;

            const list = results.map((category) => ({
              key: category.id,
              value: category.id,
              text: category.name,
            }));

            setCategoryList((prevItems) => {
              const existingKeys = new Set(prevItems.map((item) => item.key));
              const uniqueNewCategories = list.filter((category) => !existingKeys.has(category.key));

              // Append unique new categories to the previous items
              return [...prevItems, ...uniqueNewCategories];
            });
            setCtryNextPageUrl(next);
            setCtryIsFetching(false);
          }
        },
        url,
        {},
        'GET'
      );
    },
    [] // No dependencies since we don't rely on other state or props here
  );

  useEffect(() => {
    fetchCategoryList(resturls.obtainCategortList);
  }, [fetchCategoryList]);

  const loadMoreCategory = () => {
    if (ctryNextPageUrl && !ctryIsFetching) {
      const secureUrl = ctryNextPageUrl.replace('http://', 'https://');
      fetchCategoryList(secureUrl);
    }
  };

  const handleFileUpload = async (files, name, setPreviewFiles) => {
    const fileIdList = [];
    const previewFiles = [];
    console.log(files, 'files');

    try {
      for (const file of files) {
        if (file instanceof File) {
          await uploadFile(file, fileIdList, name, previewFiles); // Upload the file and update the list
        } else if (file instanceof HTMLCanvasElement) {
          const blob = await new Promise((resolve) => file.toBlob(resolve, 'image/png'));
          const fileName = name || 'captured_image.png';
          const fileFromCanvas = new File([blob], fileName, { type: 'image/png' });
          await uploadFile(fileFromCanvas, fileIdList, name, previewFiles); // Upload the canvas file and update the list
        } else {
          console.warn('Item is not a valid File or Canvas element:', file);
        }
      }
      setFileInfo((prev) => [...prev, ...fileIdList]); // Append new file IDs to existing state
      setPreviewFiles((prev) => [...prev, ...previewFiles]); // Append new file IDs to existing state
      console.log('All responses:', fileIdList);
    } catch (error) {
      console.error('Error uploading files:', error);
      alert('There was an issue uploading the files.');
    }
  };

  const uploadFile = async (file, fileIdList, name, previewFiles) => {
    setLoadingFiles((prev) => [...prev, file.name]);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await new Promise((resolve, reject) => {
        GlobalService.generalSelect((respdata) => resolve(respdata), resturls.uploadFile, formData, 'POST');
      });

      fileIdList.push(response?.id);
      previewFiles.push(response);
      setLoadingFiles((prev) => prev.filter((name) => name !== file.name));
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('There was an issue uploading the file.');
    }
  };

  const handleSubmit = (values) => {
    // if (!values.subject || !values.priority) {
    //   toast.error('Please fill in all required fields before submiting.');
    //   return;

    // }

    console.log('role_____________', CurentRole);
    const userId = userInfo?.user_id || userInfo?.userId;

    const assignTo = values?.assignToMe ? userId : values?.assignTo;
    const assignToType2 = businessInfo?.accountant_id
      ? businessInfo?.accountant_id
      : businessInfo?.business_superuser_id;
    const subject = values.subject?.length > 0 ? values.subject : 'No subject';
    // const finalValue = values?.subCategory || values.category;

    const obj = {
      subject,
      description: values.description,
      priority: values.priority,
      created_by: userId,
      business: values?.organization.length > 0 ? values.organization : businessInfo?.business_id,
      assign_to: assignTo?.length === 0 ? Number(assignToType2) : Number(assignTo),
      attachements: fileIdList,
      ...(['accountant', 'manager', 'superuser'].includes(CurentRole) && { category: values.category }),
      ...(['accountant', 'manager', 'superuser'].includes(CurentRole) && { sub_category: values?.subCategory }),
      ...(['accountant', 'manager', 'superuser'].includes(CurentRole) && { requested_by: values?.requestedBy }),
    };

    console.log(obj, 'values');
    console.log(values, 'assig');
    console.log(values?.organization, 'org');

    GlobalService.generalSelect(
      (respdata) => {
        const { business } = respdata;
        if (respdata?.type !== 'error') {
          setTicketInfo(respdata);
          // navigate(`/ticketList/${business?.business_id}`)
        } else {
          toast.error(respdata?.data?.detail);
        }
        // const { data } = respdata;
      },
      `${resturls.createTicket}`,
      obj,
      'POST'
    );
  };

  const backIcon = () => (
    <svg width="16" height="16" cursor="pointer" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.79313 10.5L5.3125 8L7.79313 5.5M5.65719 8H10.6875"
        stroke="black"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14 8C14 4.6875 11.3125 2 8 2C4.6875 2 2 4.6875 2 8C2 11.3125 4.6875 14 8 14C11.3125 14 14 11.3125 14 8Z"
        stroke="black"
        stroke-miterlimit="10"
      />
    </svg>
  );

  const completedIcon = () => (
    <svg width="76" height="76" viewBox="0 0 76 76" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_211_9805)">
        <path
          d="M38.0026 5.83398C20.2984 5.83398 5.83594 20.2964 5.83594 38.0006C5.83594 55.7049 20.2984 70.1673 38.0026 70.1673C55.7068 70.1673 70.1693 55.7049 70.1693 38.0006C70.1693 20.2964 55.7068 5.83398 38.0026 5.83398Z"
          fill="#2E7A31"
          stroke="#DBF7DC"
          stroke-width="11"
        />
        <path
          d="M28 38.5333L34.3333 46L47 30"
          stroke="#DBF7DC"
          stroke-width="4.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_211_9805">
          <rect width="76" height="76" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );

  const renderCompletedContent = () => {
    return (
      <div className={ticketInfo && isResponsive ? 'dimmer' : ''}>
        <div className={`${ticketInfo ? style.completedModal : ''} ${style.completedPopup}`}>
          <div className={style.cmptIconWrapper}>{completedIcon()}</div>
          <h4>Details Submitted Successfully!</h4>
          <div className={style.cmptContent}>
            <p>Request ID: #{ticketInfo?.id}</p>
            <span onClick={() => navigate(`/ticket-view/${ticketInfo?.id}`)}>{`View details >`}</span>
          </div>
          {ticketInfo?.attachements?.length > 0 && <p className={style.cmptDesc}>Your document has been uploaded</p>}
          <Button
            className={style.cmptBtn}
            onClick={() => navigate(`/ticketList/${ticketInfo?.business?.business_id}`)}
          >
            Got it
          </Button>
        </div>
      </div>
    );
  };

  const dropdownOptions = [
    ...organisationList,
    ...(nextPageUrl && !isFetching
      ? [
          {
            key: 'load_more',
            text: 'Load More',
            value: 'load_more',
            disabled: isFetching,
            className: 'load-more-option',
          },
        ]
      : []),
  ];

  const categoryOptions = [
    ...categoryList,
    ...(ctryNextPageUrl && !ctryIsFetching
      ? [
          {
            key: 'load_more',
            text: 'Load More',
            value: 'load_more',
            disabled: ctryIsFetching,
            className: 'load-more-option',
          },
        ]
      : []),
  ];

  const renderContent = () => {
    if (isResponsive) {
      return (
        <div className={style.mainContainer}>
          {!isResponsive && (
            <div className={style.backIcon} onClickCapture={() => navigate(`/ticketList/${id ? id : ''}`)}>
              {backIcon()}
            </div>
          )}
          <TicketForm
            organisationList={organisationList}
            categoryList={categoryList}
            loadingFiles={loadingFiles}
            obtainUsersList={obtainUsersList}
            setTicketInfo={setTicketInfo}
            initialValues={initialValues}
            assignedUser={businessInfo?.accountant_name}
            onSubmit={handleSubmit}
            hideFields={[]}
            handleFileUpload={handleFileUpload}
            userList={userList}
            idList={fileIdList}
            editIdList={setFileInfo}
          />
        </div>
      );
    }
    return (
      <div className={style.ticketCreation}>
        <div className={style.navigationWrapper}>
          <NavigationBar />
        </div>
        <div className={style.rightContent}>
          <h4>New Ticket</h4>
          <hr />
          <TicketForm
            obtainUsersList={obtainUsersList}
            organisationList={dropdownOptions}
            categoryList={categoryOptions}
            loadingFiles={loadingFiles}
            setTicketInfo={setTicketInfo}
            initialValues={initialValues}
            assignedUser={businessInfo?.accountant_name}
            onSubmit={handleSubmit}
            hideFields={[]}
            handleFileUpload={handleFileUpload}
            userList={userList}
            idList={fileIdList}
            editIdList={setFileInfo}
            fetchUserBusinesses={fetchUserBusinesses}
            loadMoreItems={loadMoreItems}
            loadMoreCategory={loadMoreCategory}
            nextPageUrl={nextPageUrl}
            isFetching={isFetching}
          />
        </div>
      </div>
    );
  };

  return (
    <div>
      <Header />
      {renderContent()}
      {isResponsive ? (
        renderCompletedContent()
      ) : (
        <Modal
          size="small"
          open={ticketInfo}
          onClose={() => navigate(`/ticketList/${ticketInfo?.business?.business_id}`)}
          closeIcon
        >
          <Modal.Content>{renderCompletedContent()}</Modal.Content>
        </Modal>
      )}
      {/* {renderModal()} */}
    </div>
  );
};

export default TicketCreation;
