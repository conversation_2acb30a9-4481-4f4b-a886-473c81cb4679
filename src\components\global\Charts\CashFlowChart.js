import React from 'react';
import Chart from 'react-apexcharts';
import './scss/barChart.scss';
import { formatNumber } from '../../utils/dateUtils';

const CashFlowChart = ({ categories, receivables, payables, closingBalance }) => {
  // Ensure data is safe (no null or undefined values)
  // const safeBarData = barData.map((val) => val ?? 0);
  // const safeTrendlineData = trendlineData.map((val) => val ?? 0);

  if (!categories?.length || !receivables?.length || !payables?.length || !closingBalance?.length) {
    return null;
  }

  const options = {
    chart: {
      type: 'bar',
      stacked: true,
      background: 'transparent',
      toolbar: {
        show: false, // Disable the toolbar (menu icon)
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '70%',
        borderRadius: 4, // Rounded bars
        borderRadiusApplication: 'end', // Apply borderRadius to the end of the bar
        borderRadiusWhenStacked: 'all', // Apply borderRadius to all stacked bars
        dataLabels: {
          enabled: true,
          position: 'center', // Ensure labels are on top
          style: {
            fontSize: '12px',
            colors: ['#000'], // Adjust color if needed
          },
        },
      },
    },
    fill: {
      type: 'solid',
    },
    colors: ['#21A366', '#FFB200', '#4E5BA6'], // Green for top, Yellow for bottom, Blue for balance

    dataLabels: {
      enabled: true,
      formatter: (val, { seriesIndex }) => (seriesIndex === 2 ? '' : `₹ ${formatNumber(val)}`), // Keep original position
      style: {
        colors: ['#181D27'], // ✅ Set text color
        fontSize: '12px',
        fontFamily: 'DM Sans, sans-serif', // ✅ Set font to DM Sans
        fontWeight: 500,
        background: 'transparent',
      },
      offsetY: -20, // ✅ Keeps original position
    },

    stroke: {
      show: true,
      curve: 'straight', // Makes trendline straight
      width: [0, 0, 3],
    },
    markers: {
      categories,
      size: 5, // Always-visible trendline circles
      colors: ['#fff'],
      strokeColors: '#4E5BA6',
      strokeWidth: 1,
      hover: {
        size: 7,
      },
      style: {
        cursor: 'pointer',
      },
    },
    xaxis: {
      categories,
      axisBorder: {
        show: false,
        offsetX: -10,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        style: {
          colors: '#535862',
        },
        formatter: (value) => {
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            return value; // Return original value if not a valid date
          }

          const today = new Date();
          const isToday = date.toDateString() === today.toDateString();
          if (isToday) {
            return 'Today';
          }

          const currentYear = today.getFullYear();
          const yearSuffix = date.getFullYear() !== currentYear ? ` ${date.getFullYear()}` : '';

          return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short' }) + yearSuffix;
        },
      },
    },
    yaxis: {
      show: false,
    },
    grid: {
      show: false,
    },
    tooltip: {
      enabled: true,
      shared: false,
      followCursor: true, // ✅ Follows cursor to avoid cut-off
      theme: 'light',
      fillSeriesColor: false,
      position: 'top', // ✅ Positions tooltip on top
      style: {
        fontSize: '12px',
      },
      y: {
        formatter: (val, { seriesIndex }) => (seriesIndex < 2 ? `₹ ${formatNumber(val)}` : formatNumber(val)),
      },
    },

    legend: {
      position: 'bottom',
      horizontalAlign: 'left', // ✅ Moves legend to the left
      offsetY: 10,
      markers: {
        shape: 'circle', // ✅ Makes legend icons circular
      },
      labels: {
        colors: '#000',
        useSeriesColors: false,
        formatter: function (seriesName) {
          return seriesName === 'Account Receivables'
            ? 'Increase'
            : seriesName === 'Account Payables'
              ? 'Decrease'
              : 'Balance';
        },
      },
    },
  };

  const series = [
    {
      name: 'Increase',
      type: 'bar',
      data: receivables,
    },
    {
      name: 'Decrease',
      type: 'bar',
      data: payables,
    },
    {
      name: 'Balance',
      type: 'line',
      data: closingBalance,
    },
  ];

  const width = receivables?.length > 7 ? `${receivables?.length * 65}px` : '20%';

  return (
    <div className="chartMainContentWarpper">
      <div style={{ minWidth: width, height: '80%' }}>
        <Chart key={receivables} options={options} series={series} type="bar" height={400} />
      </div>
    </div>
  );
};

export default CashFlowChart;
