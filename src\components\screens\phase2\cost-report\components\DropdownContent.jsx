import React from 'react';
import { Input } from 'semantic-ui-react';
import style from '../scss/dropdownContent.module.scss';
import { CloseIcon, RadioSelectedIcon } from '../../../../../assets/svgs';
import { useAuth } from '../../../../../contexts/AuthContext';
import { timelineDropdownOptions } from '../../../../utils/constants';

const DropdownContent = ({
  selectedTimeline,
  handleSelectDropdown,
  activeInput,
  dateData,
  handleChange,
  setActiveInput,
  setChanges,
  changes,
  setOpenDropdown,
  popupRef,
  openDropdown,
  setDateData,
}) => {
  const { isMobileScreen } = useAuth();

  const handleIncreaseCount = () => {
    setOpenDropdown(false);
    setChanges(changes + 1);
  };

  const handleClrDates = () => {
    setDateData({ startDate: '', endDate: '' });
  };

  const today = new Date().toISOString().split('T')[0];
  const renderContent = () => (
    <div className={style.popupContainer} ref={popupRef} onClose={() => setOpenDropdown(false)}>
      {timelineDropdownOptions?.map((list) => (
        <div
          key={list?.value}
          className={`${selectedTimeline?.value === list?.value ? style.selectedItem : ''} ${style.dropDownItem}`}
          onClickCapture={() => handleSelectDropdown(list)}
        >
          <div className={style.option}>
            <span className={style.customRadio}>
              <RadioSelectedIcon />
            </span>
            <p>{list?.text}</p>
          </div>

          {selectedTimeline?.value === 'customDate' && selectedTimeline?.value === list?.value && (
            <>
              <div className={style.dateContainer}>
                {/* Start Date */}
                <div className={style.dateWrapper}>
                  {/* {activeInput.startDate || dateData?.startDate ? ( */}
                  <Input
                    className={style.dateInput}
                    type="date"
                    value={dateData?.startDate}
                    onChange={(e) => handleChange(e.target.value, 'startDate')}
                    onFocus={() => setActiveInput({ ...activeInput, startDate: true })}
                    max={today}
                    onBlur={() =>
                      setActiveInput((prev) => ({
                        ...prev,
                        startDate: !!dateData?.startDate,
                      }))
                    }
                  />
                  {/* ) : (
                      <div
                        className={style.emptyInput}
                        onClick={() => setActiveInput({ ...activeInput, startDate: true })}
                      >
                        <p>Start Date</p> <span><DropdownIcon /></span>
                      </div>
                    )} */}
                </div>
                <p>-</p>

                {/* End Date */}
                <div className={style.dateWrapper}>
                  {/* {activeInput.endDate || dateData?.endDate ? ( */}
                  <Input
                    className={style.dateInput}
                    type="date"
                    value={dateData?.endDate || today}
                    onChange={(e) => handleChange(e.target.value, 'endDate')}
                    onFocus={() => setActiveInput({ ...activeInput, endDate: true })}
                    max={today}
                    onBlur={() =>
                      setActiveInput((prev) => ({
                        ...prev,
                        endDate: !!dateData?.endDate,
                      }))
                    }
                  />
                  {/* ) : (
                      <div
                        className={style.emptyInput}
                        onClick={() => setActiveInput({ ...activeInput, endDate: true })}
                      >
                        <p>End Date</p> <span><DropdownIcon /></span>
                      </div>
                    )} */}
                </div>
              </div>

              {(dateData?.startDate || dateData?.endDate) && !isMobileScreen && (
                <div className={style.btnWrapper}>
                  <div className={style.clearBtn}>
                    <button disabled={!dateData?.startDate || !dateData?.endDate} onClick={handleClrDates}>
                      Clear
                    </button>
                  </div>
                  <div className={style.dateApplyBtn}>
                    <button
                      className={!dateData?.startDate || !dateData?.endDate ? style.disableBtn : ''}
                      disabled={!dateData?.startDate || !dateData?.endDate}
                      onClick={() => setChanges(changes + 1)}
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      ))}
    </div>
  );

  if (isMobileScreen) {
    return (
      <div className={isMobileScreen && openDropdown ? 'dimmer' : ''}>
        <div className={`${openDropdown ? style.openDropdown : ''} ${style.logListPopup}`}>
          <div className={style.listCloseIcon} onClickCapture={() => setOpenDropdown(false)}>
            {<CloseIcon />}
          </div>
          <div className={style.modalContent}>
            <h4>Time period</h4>
            <hr />
            {renderContent()}
            <hr />
            <div className={style.applyBtn}>
              <button className={style.downloadBtn} onClick={handleIncreaseCount}>
                Apply
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return renderContent();
};

export default DropdownContent;
