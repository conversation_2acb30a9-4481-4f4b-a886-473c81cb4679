import React from 'react';
import { Popup } from 'semantic-ui-react';
import { DownloadIcon, EmptyBox, SearchIcon, SortIcon, TotalInventoryIcon } from '../../../../../assets/svgs';
import ReportHeader from '../../../../global/components/ReportHeader';
import style from '../scss/mobileContent.module.scss';
import { LoadingWrapper } from '../../../../global/components';
import { formatAmount } from '../../../../utils/dateUtils';
import { useAuth } from '../../../../../contexts/AuthContext';

const MobileContent = ({
  filteredItems,
  inventoryDetails,
  handleRefresh,
  setDownloadModal,
  isLoading,
  renderPagination,
  handleDropdownList,
  openDropdown,
  setOpenDropdown,
  handleSearch,
  searchTerm,
}) => {
  const updatedList = filteredItems?.data?.length > 0 ? filteredItems?.data || [] : inventoryDetails?.data || [];
  const { isMobileScreen } = useAuth();

  return (
    <>
      <ReportHeader
        title="Inventory Report"
        data={inventoryDetails}
        handleRefresh={handleRefresh}
        setDownloadModal={setDownloadModal}
      />

      <LoadingWrapper loading={isLoading} minHeight={true}>
        <>
          <div className={style.mobileHeaderContent}>
            <div className={style.detailedWrapper}>
              <div className={style.overAllInfo}>
                <div className={style.leftInfo}>
                  <TotalInventoryIcon /> Total Inventory
                </div>
                <div className={style.rightInfo}>₹{inventoryDetails?.total_inventory_value}</div>
              </div>
            </div>
          </div>

          <div>
            <div className={style.reportHeader}>
              <h6>Detailed Report </h6>
              <div>
                <Popup
                  className={style.popup}
                  trigger={
                    <div className={style.sortFilter} onClick={() => handleDropdownList()}>
                      {' '}
                      <SortIcon />
                      <p>Sort</p>
                    </div>
                  }
                  // content={renderPopupContent}
                  position="bottom left"
                  on="click"
                  hoverable
                  basic
                  open={!isMobileScreen && openDropdown}
                  onClose={() => {
                    setOpenDropdown(false);
                  }}
                />
              </div>
            </div>
            <div className={style.filterWrapper}>
              <div className={style.customInput}>
                <SearchIcon />
                <input
                  type="text"
                  placeholder="Search by Inventory Name or Unit..."
                  onChange={handleSearch}
                  value={searchTerm}
                />
              </div>
            </div>
            {updatedList?.length === 0 ? (
              <div className={style.emptyMsg}>
                <EmptyBox />
                <p>Inventory details are currently unavailable.</p>
              </div>
            ) : (
              <div className={style.filterWrapper}>
                <div className={style.listContainer}>
                  <div className={style.listHeader}>
                    <span className={`${style.unitName} ${style.headerItem}`}>Item Name & Unit</span>
                    <span className={style.headerItem}>Qty & Unit</span>
                    <span className={`${style.headerItem} ${style.valueColumnHeader}`}>Value</span>
                  </div>

                  <ul className={style.itemList}>
                    {updatedList?.map((item, index) => (
                      <li key={index} className={style.item}>
                        <span className={`${style.unitName} ${style.itemName}`}>{item.item_name}</span>
                        <span className={style.itemName}>
                          {item.total_quantity} <span>{item?.unit ? item.unit : 'kg'}</span>
                        </span>
                        <span className={`${style.itemName} ${style.valueColumnItem}`}>
                          {formatAmount(item.total_value)}
                        </span>
                      </li>
                    ))}
                    {renderPagination()}
                  </ul>
                </div>
                <div className={style.downloadBtnWrapper}>
                  <button className={style.downloadBtn} onClick={() => setDownloadModal(true)}>
                    {<DownloadIcon />}Download Report
                  </button>
                </div>
              </div>
            )}
          </div>
        </>
      </LoadingWrapper>
    </>
  );
};

export default MobileContent;
