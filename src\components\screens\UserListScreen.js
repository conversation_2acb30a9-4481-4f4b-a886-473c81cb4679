import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import style from './scss/userListScreen.module.scss';
import NavigationBar from './NavigationBar';
import Header from '../global/Header';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import Avatar from 'react-avatar';
import { decryptData } from '../utils/cryptoUtils';
import ls from 'local-storage';

const UserListScreen = () => {
  const navigate = useNavigate();
  const [usersList, setUserList] = useState();
  const roleEncripted = ls.get('access_token')?.role;
  const role = roleEncripted && decryptData(roleEncripted);

  useEffect(() => {
    GlobalService.generalSelect(
      (respdata) => {
        const { data } = respdata;
        setUserList(data);
      },
      `${resturls.obtainCategoryWiseUser}?user_type=business_users`,
      {},
      'GET'
    );
  }, []);

  const plusIcon = () => (
    <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.5 2.83398C8.77614 2.83398 9 3.05784 9 3.33398V12.6673C9 12.9435 8.77614 13.1673 8.5 13.1673C8.22386 13.1673 8 12.9435 8 12.6673V3.33398C8 3.05784 8.22386 2.83398 8.5 2.83398Z"
        fill="#FDFDFD"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M3.33594 8C3.33594 7.72386 3.5598 7.5 3.83594 7.5H13.1693C13.4454 7.5 13.6693 7.72386 13.6693 8C13.6693 8.27614 13.4454 8.5 13.1693 8.5H3.83594C3.5598 8.5 3.33594 8.27614 3.33594 8Z"
        fill="#FDFDFD"
      />
    </svg>
  );

  const renderUserCard = () => {
    return usersList?.map((data) => {
      return (
        <div className={style.card}>
          <div className={style.avatarWrapper}>
            <Avatar className={style.avatar} color="#EAECF5" name={data?.email} />
          </div>
          <p>{data?.email}</p>
        </div>
      );
    });
  };
  return (
    <>
      <Header />
      <div className={style.userListScreen}>
        <div className={style.navigationWrapper}>
          <NavigationBar disable />
        </div>
        <div className={style.rightContentWrapper}>
          <div className={style.headerContent}>
            <h4>Users</h4>
            {role === 'superuser' && (
              <div onClickCapture={() => navigate('/createUser')} className={style.plusIcon}>
                {plusIcon()}
              </div>
            )}
          </div>
          <hr />
          <div className={style.listWrapper}>{renderUserCard()}</div>
        </div>
      </div>
    </>
  );
};

export default UserListScreen;
