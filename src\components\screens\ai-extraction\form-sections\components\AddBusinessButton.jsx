import React, { useState } from 'react';
import { Modal } from 'semantic-ui-react';
import { ChevronRight } from 'lucide-react';
import AddSupplierForm from './AddSupplierForm';

const StepItem = ({ number, title, description }) => (
  <div className="p-3 bg-[#F5F5F5] rounded-xl">
    <div className="flex gap-4">
      <div className="flex-shrink-0">
        <div className="w-6 h-6 rounded-full flex items-center justify-center border border-[#181D27] text-[#181D27]">
          <span>{number}</span>
        </div>
      </div>
      <div className="flex flex-col gap-2 font-thin">
        <span className="text-lg text-[#181D27]">{title}</span>
        <span className="text-[#717680] text-base">{description}</span>
      </div>
    </div>
  </div>
);

const steps = [
  {
    number: 1,
    title: 'Log in to your accounting platform',
    description: 'Navigate to your accounting platform and access the product master list',
  },
  {
    number: 2,
    title: 'Add the business details',
    description: 'Create a new product entry in your accounting platform using the relevant details',
  },
  {
    number: 3,
    title: 'Refresh the dropdown list',
    description: 'Return to this platform and click "Refresh" on the dropdown list to see the newly added product',
  },
];

function AddBusinessButton({ formData }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div onClick={() => setIsOpen(true)} className="text-accent2 cursor-pointer">
        <span className="flex items-center gap-1 w-fit text-nowrap border-b border-accent2">
          Add a Business <ChevronRight className="w-4 h-4" />
        </span>
      </div>

      {formData?.accounting_platform?.toLowerCase() === 'zoho' ? (
        <AddSupplierForm formData={formData} open={isOpen} onClose={() => setIsOpen(false)} />
      ) : (
        <Modal
          dimmer="blurring"
          open={isOpen}
          onClose={() => setIsOpen(false)}
          className="!w-[40em] rounded-xl overflow-y-scroll"
        >
          <div className="p-6 bg-white">
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-center">Follow these steps to add a new business:</h3>
            </div>

            <div className="space-y-4">
              {steps.map((step) => (
                <StepItem key={step.number} number={step.number} title={step.title} description={step.description} />
              ))}
            </div>
          </div>

          <div className="flex justify-center p-4 border-t border-[#E9EAEB] bg-[#FDFDFD]">
            <button
              onClick={() => setIsOpen(false)}
              className="w-50 h-10 flex items-center justify-center rounded-full bg-accent1 font-semibold text-base hover:bg-opacity-70 transition-colors"
            >
              Got it
            </button>
          </div>
        </Modal>
      )}
    </>
  );
}

export default AddBusinessButton;
