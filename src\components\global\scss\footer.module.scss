@import '../../../assets//scss/main.scss';

.footerContainer {
  // padding: 0.6em 1em;
  background-color: $white;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 950;
  ol {
    list-style: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
    padding: 0em;
    li {
      font-size: 1.2em !important;
      // font-weight: 600;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5em;
      width: 20%;
      svg {
        width: 20px;
        height: 20px;
      }
    }
  }
  .middleBtn {
    border-radius: 100%;
    background-color: $primaryColor;
    height: 3em;
    width: 3em;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.6em !important;
    position: relative;
    bottom: 0.5em;
    svg {
      height: 18px;
      width: 18px;
      stroke: white;
    }
  }
  .middleBtn::after {
    bottom: 77px !important;
  }
  .activeMenu {
    font-weight: 900;
    position: relative;
  }
  .activeMenu::after {
    content: '';
    height: 2.5px;
    width: 100%;
    background-color: #f6d659;
    bottom: 55px;
    left: 0;
    position: absolute;
    transition: all 0.3s ease-in-out;
  }
  .activeMenu::before {
    content: '';
    height: 5.5em;
    width: 100%;
    background: linear-gradient(to bottom, #ffff8866, transparent);
    bottom: -38px;
    left: 0;
    position: absolute;
    transition: all 0.3s ease-in-out;
  }
}

.popupContainer {
  padding: 1em;
  background-color: white;
  box-shadow: 0 0 2px 2px $borderColor;
  ol {
    display: block;
    li {
      margin-bottom: 1em;
      width: 100%;
      padding: 1em;
      align-items: flex-start;
      background-color: rgb(255, 255, 255);
      border-radius: 10px !important;
      box-shadow: 0 0 1px 1px $borderColor;
    }
  }
}
