import { memo, useEffect, useMemo } from 'react';
import { Form } from 'semantic-ui-react';
import useLoadMoreWithSearch from '../global/hooks/useLoadMoreWithSearch';
import { isValidValue, transformToDropdownOptions } from '../utils';

const SearchableFormSelect = ({
  search = true,
  url,
  queryParams,
  transformOptions,
  subOptions = '',
  initialValue = null,
  selectOnBlur = false,
  selectOnNavigation = false,
  autoSelectSingle = false,
  onFetched = null,
  ...restProps
}) => {
  const { data, isLoading, nextPageUrl, query, setQuery, LoadMoreButton } = useLoadMoreWithSearch(url, queryParams);

  useEffect(() => {
    onFetched && onFetched(data);
  }, [data]);

  const loadingBtnObj = useMemo(() => {
    if (!isLoading && nextPageUrl) {
      return [
        {
          key: 'load_more',
          text: 'Load More',
          value: 'load_more',
          disabled: !!isLoading,
          content: LoadMoreButton,
          className: 'load-more-option',
        },
      ];
    }
    return [];
  }, [isLoading, nextPageUrl]);

  const tranformedOptions = useMemo(() => {
    const options = transformToDropdownOptions(data, {
      keyProp: transformOptions?.key || 'id',
      valueProp: transformOptions?.value || 'id',
      textProp: transformOptions?.text || 'name',
      subOptions: subOptions,
    }).filter((opt) => isValidValue(opt.value));

    if (initialValue?.value && !options.some((opt) => opt.value === initialValue.value)) {
      options.unshift(initialValue);
    }

    return [...options, ...loadingBtnObj];
  }, [data]);

  useEffect(() => {
    if (!query) {
      if (!restProps.value && autoSelectSingle && data?.length === 1) {
        restProps.onChange?.(null, { value: data[0].id });
      }
    }
  }, [data]);
  return (
    <Form.Select
      options={tranformedOptions}
      onSearchChange={(e, data) => setQuery(data.searchQuery)}
      search={search}
      loading={isLoading}
      selectOnBlur={selectOnBlur}
      selectOnNavigation={selectOnNavigation}
      {...restProps}
    />
  );
};

export default memo(SearchableFormSelect);
