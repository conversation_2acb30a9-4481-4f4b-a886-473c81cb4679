import React from 'react';
import { Button } from 'semantic-ui-react';
import { CloseIcon, RadioSelectedIcon } from '../../../assets/svgs';
import style from './scss/reportSortFilter.module.scss';
import { useAuth } from '../../../contexts/AuthContext';
import { SortOptions } from '../../utils/constants';

const ReportSortFilter = ({
  selectedTimeline,
  handleSelectDropdown,
  handleClear,
  handleApplySort,
  openDropdown,
  handleClose,
  sortOption,
}) => {
  const { isMobileScreen } = useAuth();

  const dropdownOption = sortOption || SortOptions;

  const renderContent = () => (
    <div className={style.popupContainer}>
      {dropdownOption.map((group, index) => (
        <div key={`keyIs${group.title}-${index}`}>
          <p className={style.dropdownTitle}>{group.title}</p>
          {group.options.map((list) => (
            <div
              key={list.value}
              className={`${selectedTimeline?.value === list?.value ? style.selectedItem : ''} ${style.dropDownItem}`}
              onClickCapture={() => handleSelectDropdown(list, group.title)}
            >
              <div className={style.option}>
                <span className={style.customRadio}>
                  <RadioSelectedIcon />
                </span>
                <p>{list.text}</p>
              </div>
            </div>
          ))}
        </div>
      ))}
      {!isMobileScreen && (
        <div className={style.filterBtnWrapper}>
          <Button className={style.clearBtn} onClick={handleClear}>
            Clear
          </Button>
          <Button
            className={selectedTimeline?.value && style.applybtn}
            onClick={selectedTimeline?.value && handleApplySort}
          >
            Apply
          </Button>
        </div>
      )}
    </div>
  );

  if (isMobileScreen) {
    return (
      <div className={isMobileScreen && openDropdown ? 'dimmer' : ''} onClick={handleClose}>
        <div
          className={`${openDropdown ? style.openDropdown : ''} ${style.logListPopup}`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className={style.listCloseIcon} onClickCapture={() => handleClose()}>
            <CloseIcon />
          </div>
          <div className={style.modalContent}>
            <div>
              <h4>Time period</h4>
            </div>
            <hr />
            {renderContent()}
            <hr />
            <div className={style.filterBtnWrapper}>
              <Button className={style.clearBtn} onClick={handleClear}>
                Clear
              </Button>
              <Button className={selectedTimeline?.value && style.applybtn} onClick={handleApplySort}>
                Apply
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return renderContent();
};

export default ReportSortFilter;
