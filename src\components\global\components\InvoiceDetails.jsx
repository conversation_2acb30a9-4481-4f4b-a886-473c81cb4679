import React from 'react';
import style from './scss/activeTabContent.module.scss';
import { formatAmount, formatDateDayTypes } from '../../utils/dateUtils';

const InvoiceDetails = ({ invoiceInfo, labelName }) => {
  const getDateColor = (dateString) => {
    const formattedDate = formatDateDayTypes(dateString);

    if (formattedDate === 'Today') return '#F79009';
    if (formattedDate === 'Yesterday' || formattedDate.includes('days ago')) return '#F04438';

    return '#4E5BA6';
  };
  return (
    <div>
      <div className={style.invoiceTitleWrapper}>
        <p>Name & Invoice</p>
        <p className={style.invoiceDate}>Due Date</p>
        <p className={style.invoiceAmount}>{labelName}</p>
      </div>
      {invoiceInfo?.data?.map((info, idx) => (
        <div key={`${info?.invoice_number}id${idx}`} className={style.invoiceContent}>
          <div className={style.idInfo}>
            <p>{info?.invoice_number || 'N/A'}</p>
            <span>{info?.ledger_name}</span>
          </div>
          <p className={style.invoiceDate} style={{ color: getDateColor(info?.due_date) }}>
            {info?.due_date ? formatDateDayTypes(info?.due_date) : '-'}
          </p>
          <p className={style.invoiceAmount}>{formatAmount(info?.amount)}</p>
        </div>
      ))}
    </div>
  );
};

export default InvoiceDetails;
