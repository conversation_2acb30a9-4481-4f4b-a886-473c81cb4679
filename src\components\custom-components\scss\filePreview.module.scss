@import '../../../assets/scss/main.scss';

.filePreviewWrapper {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid #d7d7d7;
  gap: 0.7em;
  padding: 0.5em 1em 0.5em 0.5em;
  border-radius: 10px;
  margin: 0.5em 0;
  cursor: pointer;
  position: relative;

  @include for_media(mobileScreen) {
    width: 100%;
  }

  p {
    font-size: 1.1em !important;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-user-select: none;
    user-select: none;
    white-space: nowrap;
    line-height: 35px;
    margin: 0;
    margin-right: 0.5em;
  }

  .imageWrapper {
    flex-shrink: 0;
    padding: 0.3em;
    border-radius: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f7f7f7;
    border: 1px solid #d7d7d7;
    width: 50px;
    height: 50px;

    img {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
      object-fit: contain;
    }
  }

  .closeIconWrapper {
    svg {
      width: 18px;
      height: 18px;

      path {
        fill: black;
      }
    }
  }
}
