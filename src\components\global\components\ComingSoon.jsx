import React from 'react';
import HistoryIcon from '@mui/icons-material/History';

function ComingSoon({
  title = 'Coming Soon',
  description = "This feature is currently under development. We're working hard to bring you something amazing!",
  icon: CustomIcon = null,
}) {
  const IconComponent = CustomIcon || HistoryIcon;

  return (
    <div className="flex flex-col items-center justify-center text-center p-8 gap-4 h-full w-full select-none">
      <div className="flex items-center justify-center w-16 h-16 bg-blue-50 rounded-full">
        <IconComponent className="!w-10 !h-10 text-blue-600" />
      </div>

      <div className="flex flex-col items-center justify-center gap-2 w-full max-w-md">
        <h2 className="text-xl font-semibold text-gray-800 m-0">{title}</h2>
        <p className="text-base text-gray-600 leading-relaxed m-0">{description}</p>
      </div>
    </div>
  );
}

export default ComingSoon;
