import React, { useState, useRef, useCallback, useMemo } from 'react';
import {
  Button,
  Modal,
  Box,
  Typography,
  Alert,
  LinearProgress,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import { Upload } from 'lucide-react';
import useDragAndDrop from '../global/hooks/useDragAndDrop';
import uploadService from '../services/uploadService';
import { getErrorMessage } from '../utils/apiUtils';

function UploadBtn({
  onFilesSelected,
  title = 'Upload Files',
  multiple = false,
  acceptedFileTypes = '.csv',
  className = '',
  uploadUrl = null,
  selectionOptions = null, // Array of {label, value, url} objects for selection
  selectionLabel = 'Select Option',
}) {
  const [open, setOpen] = useState(false);
  const [files, setFiles] = useState([]);
  const [message, setMessage] = useState(null);
  const [uploadingFiles, setUploadingFiles] = useState(new Set());
  const [selectedOption, setSelectedOption] = useState('');
  const fileInputRef = useRef(null);

  const currentUploadUrl = useMemo(() => {
    if (!selectionOptions) return uploadUrl;
    const selected = selectionOptions?.find((option) => option.value === selectedOption);
    return selected ? selected.url : uploadUrl;
  }, [uploadUrl, selectionOptions, selectedOption]);

  const acceptedTypes = useMemo(
    () => acceptedFileTypes.split(',').map((type) => type.trim().toLowerCase()),
    [acceptedFileTypes]
  );

  const handleClose = () => {
    setOpen(false);
    setFiles([]);
    setMessage(null);
    setUploadingFiles(new Set());
    if (selectionOptions) setSelectedOption('');
  };

  const validateFiles = useCallback(
    (fileList) => {
      const filesArray = Array.from(fileList);

      if (!multiple && filesArray.length > 1) {
        return { validFiles: [], error: 'Only one file can be uploaded' };
      }

      const validFiles = filesArray.filter((file) => {
        const fileType = file.type.toLowerCase();
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

        return acceptedTypes.some((type) => {
          return type.startsWith('.') ? fileExtension === type : fileType.includes(type);
        });
      });

      if (!validFiles.length) {
        return { validFiles: [], error: `Unsupported file type` };
      }

      const fileObjects = validFiles.map((file) => ({
        id: crypto.randomUUID(),
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        status: 'ready',
        progress: 0,
      }));

      return { validFiles: fileObjects };
    },
    [acceptedTypes, multiple]
  );

  const handleUploadFile = async (file) => {
    setUploadingFiles((prev) => new Set(prev).add(file.id));
    setFiles((prev) =>
      prev.map((f) => (f.id === file.id ? { ...f, status: 'uploading', progress: 0, errorMessage: null } : f))
    );

    try {
      const response = await uploadService.uploadFile(currentUploadUrl, file.file, (progress) => {
        setFiles((prev) => prev.map((f) => (f.id === file.id ? { ...f, progress } : f)));
      });

      setFiles((prev) =>
        prev.map((f) =>
          f.id === file.id ? { ...f, status: 'success', progress: 100, responseId: response.data?.id } : f
        )
      );
    } catch (error) {
      const errorMessage = getErrorMessage(error);
      setFiles((prev) =>
        prev.map((f) => (f.id === file.id ? { ...f, status: 'error', progress: 0, errorMessage } : f))
      );
    } finally {
      setUploadingFiles((prev) => {
        const newSet = new Set(prev);
        newSet.delete(file.id);
        return newSet;
      });
    }
  };

  const processSelectedFiles = (selectedFiles) => {
    setMessage(null);

    // Check if selection is required but not made
    if (selectionOptions && !selectedOption) {
      setMessage({ type: 'error', text: `Please select a ${selectionLabel.toLowerCase()} first` });
      return;
    }

    const { validFiles, error } = validateFiles(selectedFiles);
    if (error) setMessage({ type: 'error', text: error });
    if (validFiles?.length) {
      setFiles(validFiles);
      if (currentUploadUrl) validFiles.forEach(handleUploadFile);
      if (onFilesSelected) onFilesSelected(validFiles.map((f) => f.file));
    }
  };
  const handleRetryUpload = (fileId) => {
    const fileToRetry = files.find((f) => f.id === fileId);
    if (fileToRetry) handleUploadFile(fileToRetry);
  };
  const isAnyFileUploading = uploadingFiles.size > 0;
  const { isDragActive, eventHandlers } = useDragAndDrop((droppedFiles) => processSelectedFiles(droppedFiles));

  return (
    <>
      <Button variant="contained" startIcon={<Upload size={16} />} onClick={() => setOpen(true)} className={className}>
        Upload
      </Button>

      <Modal open={open} onClose={handleClose} {...eventHandlers}>
        <Box
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
          bg-white rounded-lg shadow-xl p-6 w-full max-w-md transition-colors 
          ${isDragActive ? 'bg-blue-50 border-2 border-blue-500' : ''}`}
        >
          <Typography variant="h6" className="!mb-4">
            {title}
          </Typography>

          {selectionOptions && (
            <FormControl component="fieldset" className="mb-4 w-full">
              <FormLabel component="legend" className="mb-2">
                {selectionLabel}
              </FormLabel>
              <RadioGroup
                row
                value={selectedOption}
                onChange={(e) => {
                  setSelectedOption(e.target.value);
                  setMessage(null);
                }}
                className="w-full"
              >
                {selectionOptions.map((option) => (
                  <FormControlLabel
                    key={option.value}
                    value={option.value}
                    control={<Radio />}
                    label={option.label}
                    className="mb-1"
                  />
                ))}
              </RadioGroup>
            </FormControl>
          )}

          <div
            className={`border-2 border-dashed rounded-lg p-8 mb-4 text-center cursor-pointer 
            transition-colors ${isDragActive ? 'border-blue-500 bg-blue-100' : 'border-gray-300 hover:border-blue-400'}`}
            onClick={() => fileInputRef.current.click()}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
            <Typography variant="body1" className="mb-1">
              {isDragActive ? 'Drop files here' : 'Drag and drop files here'}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              or <span className="text-blue-500 font-medium">browse files</span>
            </Typography>
            <input
              ref={fileInputRef}
              type="file"
              multiple={multiple}
              accept={acceptedFileTypes}
              onChange={(e) => e.target.files?.length && processSelectedFiles(e.target.files)}
              className="hidden"
            />
          </div>

          {message && (
            <Alert severity={message.type} className="mb-4">
              {message.text}
            </Alert>
          )}

          {files.length > 0 && (
            <Box className="mb-4 max-h-32 overflow-y-auto">
              <Typography variant="subtitle2" className="mb-1">
                Selected Files:
              </Typography>
              <div className="space-y-2">
                {files.map((file) => (
                  <div key={file.id} className="text-sm border rounded p-2">
                    <div className="flex justify-between items-center mb-1">
                      <span className="truncate">{file.name}</span>
                      <span className="text-xs text-gray-500">
                        (
                        {file.size > 1048576
                          ? `${(file.size / 1048576).toFixed(2)} MB`
                          : `${(file.size / 1024).toFixed(1)} KB`}
                        )
                      </span>
                    </div>

                    {file.status === 'uploading' && (
                      <div className="space-y-1">
                        <LinearProgress variant="determinate" value={file.progress} className="h-1" />
                        <span className="text-xs text-blue-600">{file.progress}%</span>
                      </div>
                    )}

                    {file.status === 'success' && <div className="text-xs text-green-600">✓ Uploaded successfully</div>}

                    {file.status === 'error' && (
                      <div className="space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-red-600">✗ Upload failed</span>
                          <Button size="small" onClick={() => handleRetryUpload(file.id)} className="text-xs">
                            Retry
                          </Button>
                        </div>
                        {file.errorMessage && (
                          <div className="text-xs text-red-500 bg-red-50 p-1 rounded">{file.errorMessage}</div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Box>
          )}

          <Box className="flex justify-end gap-2">
            <Button onClick={handleClose} color="inherit">
              Cancel
            </Button>
            {currentUploadUrl && (
              <Button onClick={handleClose} variant="contained" disabled={isAnyFileUploading || files.length === 0}>
                {isAnyFileUploading ? 'Uploading...' : 'Done'}
              </Button>
            )}
          </Box>
        </Box>
      </Modal>
    </>
  );
}

export default UploadBtn;
