import Axios from 'axios';
import ls from 'local-storage';
import { restbaseurl } from '../utils/constants';
import { stringify } from 'qs';

const ensureHttps = (url) => {
  if (url.startsWith('http://')) {
    return url.replace('http://', 'https://');
  }
  return url;
};

const apiClient = Axios.create({
  baseURL: restbaseurl,
  paramsSerializer: (params) => stringify(params, { indices: false }),
});

// necessary additions and modifications before actual request made
apiClient.interceptors.request.use(
  (config) => {
    config.url = ensureHttps(config.url);

    const token = ls.get('access_token')?.data;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for global error handling
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401 && document.location.pathname !== '/login') {
      // Redirect on unauthorized
      document.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
