@import '../../../assets/scss/main.scss';

.optionsContainer {
  display: flex;
  flex-direction: column;
  margin: 1em 0;

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .optionsWrapper {
    box-shadow: 0 0 2px 2px $borderColor;
    border-radius: 0.625em;

    .labelWrapper {
      padding: 1em;
      box-shadow: 0 0 2px 2px $borderColor;
      border-radius: 0.625em;
      @include clickable;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .iconWrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s ease;

        .icon {
          width: 1.25em;
          height: 1.25em;
          color: #101828;
        }
      }

      p {
        margin: 0;
        font-weight: 500;
      }
    }

    .listingWrapper {
      padding: 0 1.5em;
      margin: 1rem 0;
      max-height: 35em;
      overflow: hidden;
      overflow-y: auto;
      @include hide-scrollbar;

      .searchWrapper {
        position: relative;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        width: 100%;

        .searchIcon {
          position: absolute;
          left: 0.75em;
          width: 1.125em;
          height: 1.125em;
          color: #717680;
        }

        .searchInput {
          width: 100%;
          padding: 0.8em 2.5em;
          border: 1px solid #e9eaeb;
          border-radius: 0.5em;
          background-color: #f5f5f5;
          font-size: 1em;
          color: #101828;

          &::placeholder {
            color: #717680;
          }

          &:focus {
            outline: none;
            border-color: #9ea5d1;
          }
        }
      }

      .listingItemContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5em;
        cursor: pointer;
        border-radius: 0.5em;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }
      }

      .listingItem {
        display: flex;
        gap: 1em;
        align-items: center;
        margin: 0.5em 0;

        svg {
          height: 1.875em;
          width: 1.875em;
          rect {
            fill: #eaecf5;
          }
        }

        .businessLogo {
          display: flex;
          align-items: center;
          justify-content: center;

          svg,
          img {
            height: 1.875em;
            width: 1.875em;
            object-fit: contain;
          }
        }

        span {
          height: 1.25em;
          width: 1.25em;
          border-radius: 100%;
        }

        p {
          margin: 0;
          font-size: 1.2em;
          color: #101828;
        }
      }

      .loadMoreBtnWrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1em 0;

        button {
          background-color: #fdf7dd !important;
          color: #7e6607 !important;
          border: 1px solid #f9e699 !important;
          border-radius: 35px !important;
          font-weight: 600 !important;

          &:hover {
            background-color: #f9e699 !important;
          }
        }
      }

      .loaderWrapper {
        display: flex;
        justify-content: center;
        margin-top: 1em;
      }

      .noResults {
        text-align: center;
        padding: 1.25em;
        color: #666;
        font-size: 1.1em;
      }
    }
  }
}
